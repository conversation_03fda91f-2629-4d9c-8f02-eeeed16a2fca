import { User, UserRole, Permission, PaginatedResponse } from '@/types'
import apiClient from './apiClient'

class UserService {
  // 用户管理
  async getUsers(params: {
    page?: number
    pageSize?: number
    search?: string
    role?: string
    isActive?: boolean
  }): Promise<PaginatedResponse<User>> {
    const queryParams = new URLSearchParams()
    
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.pageSize) queryParams.append('page_size', params.pageSize.toString())
    if (params.search) queryParams.append('search', params.search)
    if (params.role) queryParams.append('role', params.role)
    if (params.isActive !== undefined) queryParams.append('is_active', params.isActive.toString())

    return await apiClient.get<PaginatedResponse<User>>(`/users?${queryParams.toString()}`)
  }

  async getUserById(id: string): Promise<User> {
    return await apiClient.get<User>(`/users/${id}`)
  }

  async createUser(userData: {
    username: string
    email: string
    password: string
    roleId: string
    isActive?: boolean
  }): Promise<User> {
    return await apiClient.post<User>('/users', userData)
  }

  async updateUser(id: string, userData: Partial<User>): Promise<User> {
    return await apiClient.put<User>(`/users/${id}`, userData)
  }

  async deleteUser(id: string): Promise<void> {
    return await apiClient.delete<void>(`/users/${id}`)
  }

  async activateUser(id: string): Promise<User> {
    return await apiClient.patch<User>(`/users/${id}/activate`)
  }

  async deactivateUser(id: string): Promise<User> {
    return await apiClient.patch<User>(`/users/${id}/deactivate`)
  }

  async resetUserPassword(id: string, newPassword: string): Promise<void> {
    return await apiClient.post<void>(`/users/${id}/reset-password`, { newPassword })
  }

  // 角色管理
  async getRoles(): Promise<UserRole[]> {
    return await apiClient.get<UserRole[]>('/roles')
  }

  async getRoleById(id: string): Promise<UserRole> {
    return await apiClient.get<UserRole>(`/roles/${id}`)
  }

  async createRole(roleData: {
    name: string
    permissions: string[]
  }): Promise<UserRole> {
    return await apiClient.post<UserRole>('/roles', roleData)
  }

  async updateRole(id: string, roleData: {
    name?: string
    permissions?: string[]
  }): Promise<UserRole> {
    return await apiClient.put<UserRole>(`/roles/${id}`, roleData)
  }

  async deleteRole(id: string): Promise<void> {
    return await apiClient.delete<void>(`/roles/${id}`)
  }

  // 权限管理
  async getPermissions(): Promise<Permission[]> {
    return await apiClient.get<Permission[]>('/permissions')
  }

  async getUserPermissions(userId: string): Promise<Permission[]> {
    return await apiClient.get<Permission[]>(`/users/${userId}/permissions`)
  }

  async assignRoleToUser(userId: string, roleId: string): Promise<User> {
    return await apiClient.post<User>(`/users/${userId}/roles`, { roleId })
  }

  async removeRoleFromUser(userId: string, roleId: string): Promise<User> {
    return await apiClient.delete<User>(`/users/${userId}/roles/${roleId}`)
  }

  // 用户活动日志
  async getUserActivityLog(userId: string, params: {
    page?: number
    pageSize?: number
    startDate?: string
    endDate?: string
  }): Promise<PaginatedResponse<{
    id: string
    action: string
    resource: string
    timestamp: string
    ipAddress: string
    userAgent: string
  }>> {
    const queryParams = new URLSearchParams()
    
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.pageSize) queryParams.append('page_size', params.pageSize.toString())
    if (params.startDate) queryParams.append('start_date', params.startDate)
    if (params.endDate) queryParams.append('end_date', params.endDate)

    return await apiClient.get(`/users/${userId}/activity?${queryParams.toString()}`)
  }

  // 批量操作
  async bulkUpdateUsers(userIds: string[], updates: Partial<User>): Promise<User[]> {
    return await apiClient.post<User[]>('/users/bulk-update', {
      userIds,
      updates,
    })
  }

  async bulkDeleteUsers(userIds: string[]): Promise<void> {
    return await apiClient.post<void>('/users/bulk-delete', { userIds })
  }
}

export const userService = new UserService()
export default userService
