from sqlalchemy import Column, DateTime, ForeignKey, String, Text, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class UserActivity(Base):
    __tablename__ = "user_activities"

    id = Column(String, primary_key=True, index=True)
    
    # 用户信息
    user_id = Column(String, ForeignKey("users.id"), nullable=False, index=True)
    
    # 活动信息
    action = Column(String, nullable=False, index=True)  # 'login', 'logout', 'create', 'update', 'delete'
    resource = Column(String, nullable=True, index=True)  # 操作的资源类型
    resource_id = Column(String, nullable=True)  # 操作的资源ID
    description = Column(Text, nullable=True)  # 活动描述
    
    # 请求信息
    ip_address = Column(String, nullable=True)
    user_agent = Column(Text, nullable=True)
    request_method = Column(String, nullable=True)  # GET, POST, PUT, DELETE
    request_path = Column(String, nullable=True)
    
    # 额外数据
    extra_data = Column(JSON, nullable=True)  # 额外的元数据
    
    # 时间戳
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    
    # 关联关系
    user = relationship("User", back_populates="activities")
    
    def __repr__(self):
        return f"<UserActivity(id={self.id}, user_id={self.user_id}, action={self.action})>"
