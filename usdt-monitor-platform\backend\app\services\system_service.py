import uuid
from datetime import datetime, timedelta
from typing import List, Optional, <PERSON><PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_

from app.models.system import SystemSettings, Alert
from app.schemas.system import SystemSettingsCreate, SystemSettingsUpdate, AlertCreate


class SystemService:
    def get_settings(self, db: Session) -> Optional[SystemSettings]:
        """获取系统设置"""
        return db.query(SystemSettings).first()

    def create_default_settings(self, db: Session) -> SystemSettings:
        """创建默认系统设置"""
        settings = SystemSettings(
            id=str(uuid.uuid4()),
            monitoring_interval=30,
            alert_threshold=0.1,
            data_retention_days=30,
            enable_notifications=True,
            email_notifications=True,
            sms_notifications=False
        )
        
        db.add(settings)
        db.commit()
        db.refresh(settings)
        return settings

    def update_settings(
        self,
        db: Session,
        settings_update: SystemSettingsUpdate,
        updated_by: str
    ) -> SystemSettings:
        """更新系统设置"""
        settings = self.get_settings(db)
        
        if not settings:
            # 如果没有设置记录，创建一个
            settings = self.create_default_settings(db)
        
        # 更新字段
        update_data = settings_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(settings, field, value)
        
        settings.updated_by = updated_by
        settings.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(settings)
        return settings

    # 告警管理
    def get_alerts(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        alert_type: Optional[str] = None,
        severity: Optional[str] = None,
        unread_only: bool = False,
        user_id: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Tuple[List[Alert], int]:
        """获取告警列表"""
        query = db.query(Alert)
        
        # 过滤条件
        if alert_type:
            query = query.filter(Alert.type == alert_type)
        if severity:
            query = query.filter(Alert.severity == severity)
        if unread_only:
            query = query.filter(Alert.is_read == False)
        if user_id:
            query = query.filter(Alert.user_id == user_id)
        if start_date:
            query = query.filter(Alert.timestamp >= start_date)
        if end_date:
            query = query.filter(Alert.timestamp <= end_date)
        
        # 按时间倒序
        query = query.order_by(desc(Alert.timestamp))
        
        total = query.count()
        alerts = query.offset(skip).limit(limit).all()
        
        return alerts, total

    def create_alert(self, db: Session, alert_create: AlertCreate) -> Alert:
        """创建告警"""
        alert = Alert(
            id=str(uuid.uuid4()),
            **alert_create.dict()
        )
        
        db.add(alert)
        db.commit()
        db.refresh(alert)
        return alert

    def mark_alert_as_read(self, db: Session, alert_id: str) -> Optional[Alert]:
        """标记告警为已读"""
        alert = db.query(Alert).filter(Alert.id == alert_id).first()
        if not alert:
            return None
        
        alert.is_read = True
        alert.read_at = datetime.utcnow()
        
        db.commit()
        db.refresh(alert)
        return alert

    def mark_all_alerts_as_read(self, db: Session, user_id: Optional[str] = None) -> int:
        """标记所有告警为已读"""
        query = db.query(Alert).filter(Alert.is_read == False)
        
        if user_id:
            query = query.filter(Alert.user_id == user_id)
        
        count = query.count()
        query.update({
            "is_read": True,
            "read_at": datetime.utcnow()
        })
        
        db.commit()
        return count

    def delete_alert(self, db: Session, alert_id: str) -> bool:
        """删除告警"""
        alert = db.query(Alert).filter(Alert.id == alert_id).first()
        if not alert:
            return False
        
        db.delete(alert)
        db.commit()
        return True

    def get_unread_alert_count(self, db: Session, user_id: Optional[str] = None) -> int:
        """获取未读告警数量"""
        query = db.query(Alert).filter(Alert.is_read == False)
        
        if user_id:
            query = query.filter(Alert.user_id == user_id)
        
        return query.count()

    def cleanup_old_alerts(self, db: Session, retention_days: int = 30) -> int:
        """清理旧告警"""
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        
        deleted_count = db.query(Alert).filter(
            Alert.timestamp < cutoff_date,
            Alert.is_resolved == True
        ).delete()
        
        db.commit()
        return deleted_count

    def get_system_status(self, db: Session) -> dict:
        """获取系统状态"""
        # 这里可以添加更多的系统健康检查
        return {
            "status": "healthy",
            "uptime": 0,  # 需要实际计算
            "version": "1.0.0",
            "environment": "development",
            "services": {
                "database": "running",
                "api": "running",
                "data_collector": "running"
            }
        }

    def get_system_metrics(self, db: Session, period: str = "1h") -> dict:
        """获取系统指标"""
        # 这里应该从监控系统获取实际指标
        # 目前返回模拟数据
        return {
            "cpu": [],
            "memory": [],
            "disk": [],
            "network": []
        }


system_service = SystemService()
