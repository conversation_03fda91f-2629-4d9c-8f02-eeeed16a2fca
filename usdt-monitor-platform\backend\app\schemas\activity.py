from datetime import datetime
from typing import Any, Dict, Optional
from pydantic import BaseModel


class UserActivityBase(BaseModel):
    action: str
    resource: Optional[str] = None
    resource_id: Optional[str] = None
    description: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    request_method: Optional[str] = None
    request_path: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class UserActivityCreate(UserActivityBase):
    user_id: str


class UserActivity(UserActivityBase):
    id: str
    user_id: str
    timestamp: datetime

    class Config:
        from_attributes = True
