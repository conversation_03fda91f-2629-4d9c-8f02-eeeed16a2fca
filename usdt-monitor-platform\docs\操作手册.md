# USDT监控平台操作手册

## 1. 系统概述

USDT监控平台是一个实时监控USDT价格波动的Web应用系统，提供实时价格监控、历史数据分析、用户管理等功能。

### 1.1 系统访问
- **前端地址**: http://localhost:5173
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/api/v1/docs

### 1.2 默认账户
- **管理员账户**: 
  - 用户名: admin
  - 邮箱: <EMAIL>
  - 密码: admin123

## 2. 系统部署

### 2.1 环境要求
- **操作系统**: Windows 10/11, macOS, Linux
- **Python**: 3.8+
- **Node.js**: 16+
- **数据库**: SQLite (开发) / PostgreSQL (生产)

### 2.2 快速启动

#### 2.2.1 后端启动
```bash
# 进入后端目录
cd backend

# 安装依赖
pip install -r requirements.txt

# 初始化数据库
python -m app.utils.init_db

# 启动后端服务
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

#### 2.2.2 前端启动
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动前端服务
npm run dev
```

#### 2.2.3 数据收集器启动
```bash
# 进入数据收集器目录
cd data-collector

# 启动数据收集器
python collector.py
```

### 2.3 Docker部署 (可选)
```bash
# 构建和启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 3. 用户操作指南

### 3.1 用户注册和登录

#### 3.1.1 用户注册
1. 访问系统首页
2. 点击"注册"按钮
3. 填写注册信息:
   - 用户名 (3-20个字符)
   - 邮箱地址
   - 密码 (至少8个字符)
   - 确认密码
   - 姓名 (可选)
4. 点击"注册"完成注册
5. 注册成功后自动跳转到仪表板

#### 3.1.2 用户登录
1. 访问系统首页
2. 输入用户名/邮箱和密码
3. 点击"登录"按钮
4. 登录成功后跳转到仪表板

#### 3.1.3 密码修改
1. 登录后点击右上角用户头像
2. 选择"修改密码"
3. 输入当前密码和新密码
4. 点击"确认修改"

### 3.2 仪表板使用

#### 3.2.1 实时数据查看
- **当前价格**: 显示USDT当前价格 (精确到6位小数)
- **24h变化**: 显示24小时价格变化 (绝对值和百分比)
- **市值**: 显示当前市值 (以十亿美元为单位)
- **24h交易量**: 显示24小时交易量
- **详细信息**: 包括名称、符号、排名、供应量等

#### 3.2.2 数据刷新
- 点击"刷新数据"按钮手动更新数据
- 系统会显示数据更新状态和最后更新时间

### 3.3 历史数据查询

#### 3.3.1 时间范围选择
- 1小时: 显示最近1小时的数据
- 24小时: 显示最近24小时的数据
- 7天: 显示最近7天的数据
- 30天: 显示最近30天的数据

#### 3.3.2 数据间隔选择
- 1分钟: 每分钟一个数据点
- 5分钟: 每5分钟一个数据点
- 15分钟: 每15分钟一个数据点
- 1小时: 每小时一个数据点
- 1天: 每天一个数据点

#### 3.3.3 数据导出
1. 选择时间范围和数据间隔
2. 点击"导出数据"按钮
3. 选择导出格式 (CSV/Excel)
4. 下载导出文件

## 4. 管理员操作指南

### 4.1 用户管理

#### 4.1.1 查看用户列表
1. 登录管理员账户
2. 进入"用户管理"页面
3. 查看所有用户信息:
   - 用户名、邮箱、姓名
   - 账户状态 (激活/停用)
   - 角色信息
   - 注册时间、最后登录时间

#### 4.1.2 搜索和筛选用户
- **搜索**: 在搜索框中输入用户名、邮箱或姓名
- **筛选**: 按角色、状态筛选用户
- **排序**: 按注册时间、最后登录时间排序

#### 4.1.3 创建新用户
1. 点击"创建用户"按钮
2. 填写用户信息:
   - 用户名、邮箱、密码
   - 姓名 (可选)
   - 选择角色
   - 设置账户状态
3. 点击"创建"完成

#### 4.1.4 编辑用户
1. 在用户列表中点击"编辑"按钮
2. 修改用户信息
3. 点击"保存"确认修改

#### 4.1.5 用户状态管理
- **激活用户**: 点击"激活"按钮
- **停用用户**: 点击"停用"按钮
- **删除用户**: 点击"删除"按钮 (谨慎操作)

### 4.2 角色权限管理

#### 4.2.1 查看角色列表
1. 进入"用户管理" > "角色管理"
2. 查看所有系统角色:
   - 超级管理员: 所有权限
   - 管理员: 用户管理、系统设置权限
   - 普通用户: 基本查看权限
   - 查看者: 只读权限

#### 4.2.2 权限说明
- **用户管理权限**: 创建、编辑、删除用户
- **系统设置权限**: 修改系统配置
- **数据查看权限**: 查看USDT数据
- **数据导出权限**: 导出历史数据

### 4.3 系统设置

#### 4.3.1 基本设置
1. 进入"系统设置"页面
2. 配置系统参数:
   - **监控间隔**: 数据收集间隔 (秒)
   - **告警阈值**: 价格变化告警阈值 (%)
   - **数据保留**: 历史数据保留天数
   - **通知设置**: 启用/禁用邮件通知

#### 4.3.2 保存设置
1. 修改配置参数
2. 点击"保存设置"按钮
3. 系统会显示保存成功提示

### 4.4 告警管理

#### 4.4.1 查看告警
1. 进入"系统管理" > "告警管理"
2. 查看告警列表:
   - 告警类型 (价格异常、系统错误等)
   - 严重程度 (信息、警告、错误、严重)
   - 告警时间
   - 处理状态

#### 4.4.2 处理告警
- **标记已读**: 点击"标记已读"按钮
- **批量操作**: 选择多个告警进行批量处理
- **删除告警**: 删除已处理的告警

### 4.5 系统监控

#### 4.5.1 系统状态
1. 进入"系统监控"页面
2. 查看系统状态:
   - 系统运行时间
   - 服务状态 (数据库、API、数据收集器)
   - 最后更新时间

#### 4.5.2 数据源状态
- **连接状态**: 各数据源的连接状态
- **可靠性**: 数据源可靠性评分
- **错误日志**: 数据收集错误信息

## 5. 故障排除

### 5.1 常见问题

#### 5.1.1 无法登录
**问题**: 输入正确的用户名和密码但无法登录
**解决方案**:
1. 检查用户名和密码是否正确
2. 确认账户是否被停用
3. 检查网络连接
4. 清除浏览器缓存和Cookie

#### 5.1.2 数据不更新
**问题**: 仪表板数据长时间不更新
**解决方案**:
1. 检查数据收集器是否正常运行
2. 检查网络连接
3. 查看系统日志中的错误信息
4. 重启数据收集器服务

#### 5.1.3 页面加载缓慢
**问题**: 页面加载速度很慢
**解决方案**:
1. 检查网络连接速度
2. 清除浏览器缓存
3. 检查服务器资源使用情况
4. 优化数据库查询

### 5.2 错误代码说明

#### 5.2.1 HTTP状态码
- **400**: 请求参数错误
- **401**: 未授权，需要登录
- **403**: 权限不足
- **404**: 资源不存在
- **500**: 服务器内部错误

#### 5.2.2 业务错误码
- **AUTH_001**: 用户名或密码错误
- **AUTH_002**: 账户已被停用
- **DATA_001**: 数据源连接失败
- **DATA_002**: 数据格式错误

### 5.3 日志查看

#### 5.3.1 后端日志
```bash
# 查看后端运行日志
tail -f backend/logs/app.log

# 查看错误日志
tail -f backend/logs/error.log
```

#### 5.3.2 数据收集器日志
```bash
# 查看数据收集器日志
tail -f data-collector/logs/collector.log
```

#### 5.3.3 系统日志
- 在系统管理页面查看系统运行日志
- 在告警管理页面查看错误告警

## 6. 维护指南

### 6.1 定期维护

#### 6.1.1 数据库维护
- 定期清理过期的历史数据
- 优化数据库索引
- 备份重要数据

#### 6.1.2 系统更新
- 定期更新系统依赖包
- 应用安全补丁
- 更新系统配置

#### 6.1.3 性能监控
- 监控系统资源使用情况
- 检查API响应时间
- 分析用户访问模式

### 6.2 备份和恢复

#### 6.2.1 数据备份
```bash
# 备份SQLite数据库
cp backend/usdt_monitor.db backup/usdt_monitor_$(date +%Y%m%d).db

# 备份配置文件
cp backend/.env backup/env_$(date +%Y%m%d).backup
```

#### 6.2.2 数据恢复
```bash
# 恢复数据库
cp backup/usdt_monitor_20231201.db backend/usdt_monitor.db

# 重启服务
systemctl restart usdt-monitor
```

## 7. 测试指南

### 7.1 功能测试

#### 7.1.1 用户认证测试
1. **注册测试**:
   - 使用有效信息注册新用户
   - 测试用户名/邮箱重复验证
   - 测试密码强度验证

2. **登录测试**:
   - 使用正确凭据登录
   - 测试错误凭据处理
   - 测试账户状态验证

#### 7.1.2 数据显示测试
1. **实时数据测试**:
   - 验证价格数据显示正确
   - 测试数据刷新功能
   - 检查数据精度

2. **历史数据测试**:
   - 测试不同时间范围查询
   - 验证数据分页功能
   - 测试数据导出功能

#### 7.1.3 权限控制测试
1. **角色权限测试**:
   - 测试不同角色的访问权限
   - 验证API权限控制
   - 测试页面访问控制

### 7.2 性能测试

#### 7.2.1 负载测试
- 模拟多用户并发访问
- 测试API响应时间
- 监控系统资源使用

#### 7.2.2 压力测试
- 测试系统最大承载能力
- 验证错误处理机制
- 测试系统恢复能力

## 8. 联系支持

如果遇到无法解决的问题，请联系技术支持:
- **邮箱**: <EMAIL>
- **电话**: +86-xxx-xxxx-xxxx
- **工作时间**: 周一至周五 9:00-18:00

提供问题描述时，请包含:
- 问题发生的具体时间
- 错误信息截图
- 操作步骤描述
- 系统环境信息
