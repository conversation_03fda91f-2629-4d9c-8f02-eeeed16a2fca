from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel


class USDTDataBase(BaseModel):
    timestamp: datetime
    unix_timestamp: int
    name: str = "Tether"
    symbol: str = "USDT"
    current_price_usd: float
    price_precision: Optional[str] = None
    market_cap_usd: Optional[int] = None
    market_cap_rank: Optional[int] = None
    total_volume_usd: Optional[int] = None
    circulating_supply: Optional[float] = None
    total_supply: Optional[float] = None
    price_change_24h: Optional[float] = None
    price_change_percentage_24h: Optional[float] = None
    price_change_from_last: Optional[float] = None
    market_cap_change_24h: Optional[int] = None
    market_cap_change_percentage_24h: Optional[float] = None
    data_source: Optional[str] = None
    last_updated: Optional[str] = None


class USDTDataCreate(USDTDataBase):
    pass


class USDTData(USDTDataBase):
    id: str
    created_at: datetime

    class Config:
        from_attributes = True


class USDTStats(BaseModel):
    latest: Optional[USDTData] = None
    price_history: List[USDTData] = []
    volatility_24h: float = 0.0
    average_price_24h: float = 0.0
    max_price_24h: float = 0.0
    min_price_24h: float = 0.0
    total_records: int = 0


class USDTDataQuery(BaseModel):
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    limit: Optional[int] = 100
    page: Optional[int] = 1
    page_size: Optional[int] = 20


class PriceHistoryQuery(BaseModel):
    period: str = "24h"  # 1h, 24h, 7d, 30d
    interval: Optional[str] = "1h"  # 1m, 5m, 15m, 1h, 1d
