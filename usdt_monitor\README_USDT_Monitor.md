# USDT高频市值监控程序

## 🚀 功能说明
这是一个高频USDT市值监控程序，支持1-60秒的自定义监控间隔，能够捕获更细微的价格变化。

## 📁 文件说明
- `usdt_monitor.py` - 主监控程序（支持命令行参数）
- `start_usdt_monitor.bat` - 交互式启动脚本
- `高频监控_1秒.bat` - 1秒间隔超高频监控
- `高频监控_3秒.bat` - 3秒间隔高频监控
- `推荐监控_5秒.bat` - 5秒间隔推荐设置
- `usdt_market_data.json` - 数据存储文件

## 📊 监控的数据包括
- **高精度价格** (8位小数精度)
- 与上次采集的价格变化百分比
- 市值和排名信息
- 24小时价格和市值变化
- Unix时间戳便于数据分析
- 详细的交易量和供应量信息

## 使用方法

### 方法1：直接运行Python脚本
```bash
cd usdt_monitor
python usdt_monitor.py
```

### 方法2：使用批处理文件（Windows）
双击 `usdt_monitor` 文件夹中的 `start_usdt_monitor.bat` 文件

### 方法3：命令行运行
```bash
cd d:\GitHubProjects\myAIProjects\usdt_monitor
python usdt_monitor.py
```

## 停止程序
按 `Ctrl + C` 组合键停止程序

## 数据文件
- 数据保存在 `usdt_market_data.json` 文件中
- 每次查询的结果都会追加到文件中
- 数据格式为JSON数组，包含时间戳和完整的市值信息

## 依赖库
程序需要以下Python库：
- `requests` - 用于HTTP请求
- `json` - 用于数据处理
- `datetime` - 用于时间戳
- `time` - 用于定时

如果缺少依赖库，请运行：
```bash
pip install requests
```

## 数据源
使用 CoinGecko API 获取数据：
- API地址：https://api.coingecko.com/api/v3/coins/tether
- 免费使用，无需API密钥
- 数据更新频率高，准确可靠

## 注意事项
1. 确保网络连接正常
2. 程序会持续运行直到手动停止
3. 数据文件会不断增长，定期清理或备份
4. 如果API请求失败，程序会自动重试
