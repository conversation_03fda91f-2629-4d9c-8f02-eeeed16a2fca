import { USDTData, USDTStats, PaginatedResponse } from '@/types'
import apiClient from './apiClient'

class USDTService {
  async getCurrentData(): Promise<USDTData> {
    return await apiClient.get<USDTData>('/usdt/current')
  }

  async getHistoricalData(params: {
    startDate?: string
    endDate?: string
    limit?: number
    page?: number
    pageSize?: number
  }): Promise<USDTData[]> {
    const queryParams = new URLSearchParams()
    
    if (params.startDate) queryParams.append('start_date', params.startDate)
    if (params.endDate) queryParams.append('end_date', params.endDate)
    if (params.limit) queryParams.append('limit', params.limit.toString())
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.pageSize) queryParams.append('page_size', params.pageSize.toString())

    return await apiClient.get<USDTData[]>(`/usdt/historical?${queryParams.toString()}`)
  }

  async getStats(): Promise<USDTStats> {
    return await apiClient.get<USDTStats>('/usdt/stats')
  }

  async getPriceHistory(params: {
    period: '1h' | '24h' | '7d' | '30d'
    interval?: '1m' | '5m' | '15m' | '1h' | '1d'
  }): Promise<USDTData[]> {
    const queryParams = new URLSearchParams()
    queryParams.append('period', params.period)
    if (params.interval) queryParams.append('interval', params.interval)

    return await apiClient.get<USDTData[]>(`/usdt/price-history?${queryParams.toString()}`)
  }

  async getVolatilityData(period: '24h' | '7d' | '30d'): Promise<{
    volatility: number
    maxPrice: number
    minPrice: number
    averagePrice: number
    priceRange: number
  }> {
    return await apiClient.get(`/usdt/volatility?period=${period}`)
  }

  async exportData(params: {
    startDate: string
    endDate: string
    format: 'csv' | 'json' | 'xlsx'
  }): Promise<Blob> {
    const queryParams = new URLSearchParams()
    queryParams.append('start_date', params.startDate)
    queryParams.append('end_date', params.endDate)
    queryParams.append('format', params.format)

    const response = await apiClient.getInstance().get(`/usdt/export?${queryParams.toString()}`, {
      responseType: 'blob',
    })

    return response.data
  }

  async getDataSources(): Promise<{
    sources: Array<{
      name: string
      status: 'active' | 'inactive' | 'error'
      lastUpdate: string
      reliability: number
    }>
  }> {
    return await apiClient.get('/usdt/data-sources')
  }

  async getSystemHealth(): Promise<{
    status: 'healthy' | 'warning' | 'error'
    dataCollector: {
      status: 'running' | 'stopped' | 'error'
      lastUpdate: string
      recordsCount: number
    }
    database: {
      status: 'connected' | 'disconnected'
      responseTime: number
    }
    apis: Array<{
      name: string
      status: 'online' | 'offline'
      responseTime: number
    }>
  }> {
    return await apiClient.get('/usdt/health')
  }
}

export const usdtService = new USDTService()
export default usdtService
