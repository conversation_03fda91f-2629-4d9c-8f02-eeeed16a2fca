#!/usr/bin/env python3
"""
泰达币（USDT）市值监控脚本
每隔30秒查询USDT市值并保存到本地文件
"""

import requests
import json
import time
from datetime import datetime
import os
import sys

class USDTMonitor:
    def __init__(self, data_file="usdt_market_data.json", interval=30):
        self.data_file = data_file
        self.interval = interval  # 监控间隔（秒）
        self.api_url = "https://api.coingecko.com/api/v3/coins/tether"
        # 添加更多API源以获取更高频率的数据
        self.backup_apis = [
            "https://api.coinbase.com/v2/exchange-rates?currency=USDT",
            "https://api.binance.com/api/v3/ticker/price?symbol=USDTUSDC"
        ]
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        self.last_price = None  # 记录上次价格用于变化检测
        
    def fetch_usdt_data(self):
        """获取USDT市值数据"""
        try:
            response = requests.get(self.api_url, headers=self.headers, timeout=5)
            response.raise_for_status()
            data = response.json()

            # 提取关键信息
            market_data = data.get('market_data', {})
            current_price = market_data.get('current_price', {}).get('usd', 1.0)

            # 计算价格变化
            price_change_from_last = 0
            if self.last_price is not None:
                price_change_from_last = ((current_price - self.last_price) / self.last_price) * 100

            usdt_info = {
                'timestamp': datetime.now().isoformat(),
                'unix_timestamp': int(datetime.now().timestamp()),
                'name': data.get('name', 'Tether'),
                'symbol': data.get('symbol', 'USDT').upper(),
                'current_price_usd': current_price,
                'price_precision': f"{current_price:.8f}",  # 更高精度的价格
                'market_cap_usd': market_data.get('market_cap', {}).get('usd'),
                'market_cap_rank': market_data.get('market_cap_rank'),
                'total_volume_usd': market_data.get('total_volume', {}).get('usd'),
                'circulating_supply': market_data.get('circulating_supply'),
                'total_supply': market_data.get('total_supply'),
                'price_change_24h': market_data.get('price_change_24h'),
                'price_change_percentage_24h': market_data.get('price_change_percentage_24h'),
                'price_change_from_last': price_change_from_last,  # 与上次采集的变化
                'market_cap_change_24h': market_data.get('market_cap_change_24h'),
                'market_cap_change_percentage_24h': market_data.get('market_cap_change_percentage_24h'),
                'last_updated': market_data.get('last_updated')
            }

            # 更新上次价格
            self.last_price = current_price

            return usdt_info

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求错误: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析错误: {e}")
            return None
        except Exception as e:
            print(f"❌ 获取数据时发生错误: {e}")
            return None
    
    def save_data(self, data):
        """保存数据到文件"""
        try:
            # 如果文件存在，读取现有数据
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            else:
                existing_data = []
            
            # 添加新数据
            existing_data.append(data)
            
            # 保存回文件
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)
                
            return True
            
        except Exception as e:
            print(f"❌ 保存数据时发生错误: {e}")
            return False
    
    def format_number(self, num):
        """格式化数字显示"""
        if num is None:
            return "N/A"
        if num >= 1e9:
            return f"{num/1e9:.2f}B"
        elif num >= 1e6:
            return f"{num/1e6:.2f}M"
        elif num >= 1e3:
            return f"{num/1e3:.2f}K"
        else:
            return f"{num:.2f}"
    
    def display_data(self, data):
        """显示数据到控制台"""
        if not data:
            return

        # 简化显示，突出价格变化
        timestamp = datetime.fromisoformat(data['timestamp']).strftime("%H:%M:%S")
        print(f"🕒 {timestamp} | 💵 ${data['price_precision']} | ", end="")

        # 显示与上次的变化
        if data['price_change_from_last'] != 0:
            change_emoji = "📈" if data['price_change_from_last'] > 0 else "📉"
            print(f"{change_emoji} {data['price_change_from_last']:+.6f}% | ", end="")
        else:
            print("➖ 无变化 | ", end="")

        # 显示24小时变化
        if data['price_change_percentage_24h'] is not None:
            change_24h_emoji = "📈" if data['price_change_percentage_24h'] >= 0 else "📉"
            print(f"{change_24h_emoji} 24h: {data['price_change_percentage_24h']:+.4f}%")
        else:
            print("24h: N/A")

    def display_summary(self, data):
        """显示详细摘要信息"""
        if not data:
            return

        print(f"\n{'='*60}")
        print(f"🕒 时间: {data['timestamp']}")
        print(f"💰 {data['name']} ({data['symbol']})")
        print(f"💵 精确价格: ${data['price_precision']}")
        print(f"📊 市值: ${self.format_number(data['market_cap_usd'])}")
        print(f"🏆 市值排名: #{data['market_cap_rank']}")
        print(f"📈 24小时交易量: ${self.format_number(data['total_volume_usd'])}")
        print(f"🔄 流通供应量: {self.format_number(data['circulating_supply'])}")

        if data['price_change_from_last'] != 0:
            change_emoji = "📈" if data['price_change_from_last'] > 0 else "📉"
            print(f"{change_emoji} 与上次变化: {data['price_change_from_last']:+.6f}%")

        if data['price_change_percentage_24h'] is not None:
            change_emoji = "📈" if data['price_change_percentage_24h'] >= 0 else "📉"
            print(f"{change_emoji} 24小时价格变化: {data['price_change_percentage_24h']:+.4f}%")

        if data['market_cap_change_percentage_24h'] is not None:
            cap_change_emoji = "📈" if data['market_cap_change_percentage_24h'] >= 0 else "📉"
            print(f"{cap_change_emoji} 24小时市值变化: {data['market_cap_change_percentage_24h']:+.4f}%")

        print(f"{'='*60}")
    
    def run(self):
        """运行监控程序"""
        print("🚀 USDT高频监控程序启动")
        print(f"📁 数据将保存到: {os.path.abspath(self.data_file)}")
        print(f"⏰ 每{self.interval}秒更新一次数据")
        print("🛑 按 Ctrl+C 停止程序")
        print("📊 每10次采集显示一次详细摘要\n")

        count = 0
        try:
            while True:
                count += 1
                # 获取数据
                data = self.fetch_usdt_data()

                if data:
                    # 显示简化数据
                    self.display_data(data)

                    # 每10次显示详细摘要
                    if count % 10 == 0:
                        self.display_summary(data)

                    # 保存数据
                    if not self.save_data(data):
                        print(" ❌ 保存失败")
                else:
                    print(f"❌ 获取数据失败，{self.interval}秒后重试...")

                # 等待指定间隔
                time.sleep(self.interval)

        except KeyboardInterrupt:
            print(f"\n\n🛑 程序已停止 (共采集 {count} 次)")
            print(f"📊 数据已保存到: {os.path.abspath(self.data_file)}")
            sys.exit(0)
        except Exception as e:
            print(f"\n❌ 程序运行时发生错误: {e}")
            sys.exit(1)

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='USDT高频监控程序')
    parser.add_argument('-i', '--interval', type=int, default=30,
                       help='监控间隔（秒），默认30秒')
    parser.add_argument('-f', '--file', type=str, default='usdt_market_data.json',
                       help='数据文件名，默认usdt_market_data.json')

    args = parser.parse_args()

    # 验证间隔范围
    if args.interval < 1:
        print("❌ 监控间隔不能小于1秒")
        sys.exit(1)
    elif args.interval > 60:
        print("❌ 监控间隔不能大于60秒")
        sys.exit(1)

    monitor = USDTMonitor(data_file=args.file, interval=args.interval)
    monitor.run()

if __name__ == "__main__":
    main()
