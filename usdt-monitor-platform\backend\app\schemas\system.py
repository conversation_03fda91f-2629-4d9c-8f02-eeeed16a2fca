from datetime import datetime
from typing import Optional
from pydantic import BaseModel


class SystemSettingsBase(BaseModel):
    monitoring_interval: int = 30
    alert_threshold: float = 0.1
    data_retention_days: int = 30
    enable_notifications: bool = True
    email_notifications: bool = True
    sms_notifications: bool = False


class SystemSettingsCreate(SystemSettingsBase):
    pass


class SystemSettingsUpdate(BaseModel):
    monitoring_interval: Optional[int] = None
    alert_threshold: Optional[float] = None
    data_retention_days: Optional[int] = None
    enable_notifications: Optional[bool] = None
    email_notifications: Optional[bool] = None
    sms_notifications: Optional[bool] = None


class SystemSettings(SystemSettingsBase):
    id: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    updated_by: Optional[str] = None

    class Config:
        from_attributes = True


class AlertBase(BaseModel):
    type: str
    severity: str
    title: str
    message: str
    related_data_id: Optional[str] = None
    user_id: Optional[str] = None


class AlertCreate(AlertBase):
    pass


class AlertUpdate(BaseModel):
    is_read: Optional[bool] = None
    is_resolved: Optional[bool] = None


class Alert(AlertBase):
    id: str
    is_read: bool = False
    is_resolved: bool = False
    timestamp: datetime
    read_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class SystemStatus(BaseModel):
    status: str = "healthy"
    uptime: int
    version: str
    environment: str
    services: dict = {}


class SystemMetrics(BaseModel):
    cpu: list = []
    memory: list = []
    disk: list = []
    network: list = []
