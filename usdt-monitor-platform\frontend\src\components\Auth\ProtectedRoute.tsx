import React from 'react'
import { Navigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { Result, Button } from 'antd'
import { RootState } from '@/store'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredPermission?: string
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredPermission 
}) => {
  const { isAuthenticated, user } = useSelector((state: RootState) => state.auth)

  // 如果未认证，重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }

  // 如果需要特定权限但用户没有该权限
  if (requiredPermission && user) {
    // 检查用户是否为超级用户
    if (user.is_superuser) {
      return <>{children}</>
    }

    // 检查用户的所有角色中是否有所需权限
    const hasPermission = user.roles?.some(role =>
      role.permissions?.some(permission => permission.name === requiredPermission)
    )

    if (!hasPermission) {
      return (
        <Result
          status="403"
          title="403"
          subTitle="抱歉，您没有权限访问此页面。"
          extra={
            <Button type="primary" onClick={() => window.history.back()}>
              返回
            </Button>
          }
        />
      )
    }
  }

  return <>{children}</>
}

export default ProtectedRoute
