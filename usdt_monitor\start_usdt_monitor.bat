@echo off
echo ========================================
echo        USDT高频监控程序
echo ========================================
echo.
echo 选择监控频率:
echo 1. 每1秒监控 (超高频)
echo 2. 每3秒监控 (高频)
echo 3. 每5秒监控 (中频，推荐)
echo 4. 每10秒监控 (低频)
echo 5. 自定义间隔
echo.
set /p choice="请选择 (1-5): "

cd /d "%~dp0"

if "%choice%"=="1" (
    echo 启动1秒间隔监控...
    python usdt_monitor.py -i 1
) else if "%choice%"=="2" (
    echo 启动3秒间隔监控...
    python usdt_monitor.py -i 3
) else if "%choice%"=="3" (
    echo 启动5秒间隔监控...
    python usdt_monitor.py -i 5
) else if "%choice%"=="4" (
    echo 启动10秒间隔监控...
    python usdt_monitor.py -i 10
) else if "%choice%"=="5" (
    set /p interval="请输入监控间隔(秒): "
    echo 启动自定义间隔监控...
    python usdt_monitor.py -i %interval%
) else (
    echo 无效选择，使用默认5秒间隔
    python usdt_monitor.py
)

echo.
echo 程序已结束，按任意键退出...
pause > nul
