/* USDT Dashboard 自定义样式 */

:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
}

body {
    background-color: #f5f6fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 渐变背景 */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

/* 价格显示 */
#current-price {
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.8; }
    100% { opacity: 1; }
}

/* 徽章样式 */
.badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

/* 连接状态 */
.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-connected {
    color: var(--success-color) !important;
}

.status-disconnected {
    color: var(--danger-color) !important;
}

.status-connecting {
    color: var(--warning-color) !important;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

/* 表格样式 */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* 按钮组 */
.btn-group .btn {
    border-radius: 20px;
    margin: 0 2px;
}

.btn-group .btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* 数值动画 */
.number-animate {
    transition: all 0.3s ease;
}

.number-up {
    color: var(--success-color);
    animation: numberUp 0.5s ease;
}

.number-down {
    color: var(--danger-color);
    animation: numberDown 0.5s ease;
}

@keyframes numberUp {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); color: var(--success-color); }
    100% { transform: scale(1); }
}

@keyframes numberDown {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); color: var(--danger-color); }
    100% { transform: scale(1); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2.5rem;
    }
    
    .card-body h4 {
        font-size: 1.2rem;
    }
    
    .btn-group .btn {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
}

/* 图标样式 */
.fa-2x {
    opacity: 0.7;
}

/* 卡片标题 */
.card-title {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 导航栏 */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

/* 工具提示 */
.tooltip {
    font-size: 0.8rem;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}
