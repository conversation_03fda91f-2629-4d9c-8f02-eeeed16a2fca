#!/usr/bin/env python3
"""
USDT Dashboard 启动脚本
"""
import os
import sys
from app import create_app

def main():
    """主函数"""
    # 设置环境
    os.environ.setdefault('FLASK_ENV', 'development')
    
    # 创建应用
    config_name = os.environ.get('FLASK_ENV', 'development')
    app = create_app(config_name)
    
    # 启动后台更新线程
    app.start_background_updater()
    
    # 运行应用
    try:
        print("🚀 启动USDT实时监控仪表板...")
        print(f"📊 访问地址: http://localhost:5000")
        print(f"📁 数据文件: {app.config['DATA_FILE_PATH']}")
        print("🛑 按 Ctrl+C 停止服务器")
        
        app.socketio.run(
            app,
            host='0.0.0.0',
            port=5000,
            debug=app.config['DEBUG'],
            use_reloader=False  # 避免重复启动后台线程
        )
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
