#!/usr/bin/env python3
"""
USDT监控平台系统测试脚本
测试前后端API连接和核心功能
"""

import requests
import json
import time
import sys
from datetime import datetime

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

class SystemTester:
    def __init__(self):
        self.token = None
        self.session = requests.Session()
        self.test_results = []

    def log_test(self, test_name, success, message="", data=None):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "data": data,
            "timestamp": datetime.now().isoformat()
        })

    def test_health_check(self):
        """测试系统健康检查"""
        try:
            response = self.session.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                self.log_test("系统健康检查", True, f"状态: {data.get('data', {}).get('status', 'unknown')}")
                return True
            else:
                self.log_test("系统健康检查", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("系统健康检查", False, f"连接失败: {str(e)}")
            return False

    def test_user_login(self):
        """测试用户登录"""
        try:
            login_data = {
                "username": "admin",
                "password": "admin123"
            }
            
            response = self.session.post(
                f"{BASE_URL}/auth/login",
                json=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and 'data' in data:
                    self.token = data['data'].get('access_token')
                    user_info = data['data'].get('user', {})
                    username = user_info.get('username', 'unknown')
                    
                    # 设置认证头
                    self.session.headers.update({
                        'Authorization': f'Bearer {self.token}'
                    })
                    
                    self.log_test("用户登录", True, f"用户: {username}, Token获取成功")
                    return True
                else:
                    self.log_test("用户登录", False, "响应格式错误")
                    return False
            else:
                self.log_test("用户登录", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("用户登录", False, f"请求失败: {str(e)}")
            return False

    def test_get_current_user(self):
        """测试获取当前用户信息"""
        if not self.token:
            self.log_test("获取用户信息", False, "未登录")
            return False
            
        try:
            response = self.session.get(f"{BASE_URL}/auth/me", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and 'data' in data:
                    user = data['data']
                    username = user.get('username', 'unknown')
                    email = user.get('email', 'unknown')
                    self.log_test("获取用户信息", True, f"用户: {username}, 邮箱: {email}")
                    return True
                else:
                    self.log_test("获取用户信息", False, "响应格式错误")
                    return False
            else:
                self.log_test("获取用户信息", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("获取用户信息", False, f"请求失败: {str(e)}")
            return False

    def test_usdt_current_data(self):
        """测试获取USDT当前数据"""
        if not self.token:
            self.log_test("USDT当前数据", False, "未登录")
            return False
            
        try:
            response = self.session.get(f"{BASE_URL}/usdt/current", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and 'data' in data:
                    usdt_data = data['data']
                    price = usdt_data.get('currentPriceUsd', 0)
                    symbol = usdt_data.get('symbol', 'unknown')
                    timestamp = usdt_data.get('timestamp', 'unknown')
                    
                    self.log_test("USDT当前数据", True, 
                                f"币种: {symbol}, 价格: ${price:.6f}, 时间: {timestamp}")
                    return True
                else:
                    self.log_test("USDT当前数据", False, "响应格式错误")
                    return False
            elif response.status_code == 404:
                self.log_test("USDT当前数据", False, "暂无数据 - 数据收集器可能未运行")
                return False
            else:
                self.log_test("USDT当前数据", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("USDT当前数据", False, f"请求失败: {str(e)}")
            return False

    def test_usdt_historical_data(self):
        """测试获取USDT历史数据"""
        if not self.token:
            self.log_test("USDT历史数据", False, "未登录")
            return False
            
        try:
            response = self.session.get(
                f"{BASE_URL}/usdt/historical?page=1&page_size=5", 
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and 'data' in data:
                    historical_data = data['data']
                    total = historical_data.get('total', 0)
                    items = historical_data.get('items', [])
                    
                    self.log_test("USDT历史数据", True, 
                                f"总记录数: {total}, 返回条数: {len(items)}")
                    return True
                else:
                    self.log_test("USDT历史数据", False, "响应格式错误")
                    return False
            else:
                self.log_test("USDT历史数据", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("USDT历史数据", False, f"请求失败: {str(e)}")
            return False

    def test_user_management(self):
        """测试用户管理功能"""
        if not self.token:
            self.log_test("用户管理", False, "未登录")
            return False
            
        try:
            response = self.session.get(
                f"{BASE_URL}/users?page=1&page_size=5", 
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and 'data' in data:
                    user_data = data['data']
                    total = user_data.get('total', 0)
                    items = user_data.get('items', [])
                    
                    self.log_test("用户管理", True, 
                                f"用户总数: {total}, 返回条数: {len(items)}")
                    return True
                else:
                    self.log_test("用户管理", False, "响应格式错误")
                    return False
            elif response.status_code == 403:
                self.log_test("用户管理", False, "权限不足")
                return False
            else:
                self.log_test("用户管理", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("用户管理", False, f"请求失败: {str(e)}")
            return False

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始USDT监控平台系统测试")
        print("=" * 50)
        
        # 测试顺序很重要
        tests = [
            self.test_health_check,
            self.test_user_login,
            self.test_get_current_user,
            self.test_usdt_current_data,
            self.test_usdt_historical_data,
            self.test_user_management,
        ]
        
        for test in tests:
            test()
            time.sleep(0.5)  # 避免请求过快
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 50)
        print("📊 测试结果统计")
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"通过率: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests == 0:
            print("\n🎉 所有测试通过！系统运行正常。")
        else:
            print(f"\n⚠️ 有 {failed_tests} 个测试失败，请检查系统状态。")
        
        return failed_tests == 0

def main():
    """主函数"""
    tester = SystemTester()
    success = tester.run_all_tests()
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
