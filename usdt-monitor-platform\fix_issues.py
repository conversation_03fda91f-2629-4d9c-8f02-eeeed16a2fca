#!/usr/bin/env python3
"""
快速修复脚本 - 解决前后端连接问题
"""

import requests
import json
import time

def test_backend_apis():
    """测试后端API是否正常"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试后端API状态...")
    
    # 1. 健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端健康检查: 正常")
        else:
            print(f"❌ 后端健康检查: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端连接失败: {e}")
        return False
    
    # 2. 测试CORS预检请求
    try:
        response = requests.options(f"{base_url}/api/v1/auth/login", timeout=5)
        print(f"🔍 CORS预检请求: HTTP {response.status_code}")
        if response.status_code in [200, 204]:
            print("✅ CORS配置: 正常")
        else:
            print("❌ CORS配置: 有问题")
    except Exception as e:
        print(f"❌ CORS测试失败: {e}")
    
    # 3. 测试登录API
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        response = requests.post(
            f"{base_url}/api/v1/auth/login",
            json=login_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 登录API: 正常")
                return True
            else:
                print(f"❌ 登录API: 响应格式错误 - {data}")
        else:
            print(f"❌ 登录API: HTTP {response.status_code}")
            print(f"响应: {response.text}")
    except Exception as e:
        print(f"❌ 登录API测试失败: {e}")
    
    return False

def check_frontend_status():
    """检查前端状态"""
    print("\n🌐 检查前端状态...")
    
    ports = [5173, 3000]
    for port in ports:
        try:
            response = requests.get(f"http://localhost:{port}", timeout=3)
            if response.status_code == 200:
                print(f"✅ 前端端口 {port}: 可访问")
            else:
                print(f"⚠️ 前端端口 {port}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ 前端端口 {port}: 无法访问")

def main():
    """主函数"""
    print("🚀 USDT监控平台问题诊断和修复")
    print("=" * 50)
    
    # 测试后端
    backend_ok = test_backend_apis()
    
    # 检查前端
    check_frontend_status()
    
    print("\n" + "=" * 50)
    print("📋 问题诊断结果:")
    
    if backend_ok:
        print("✅ 后端API: 正常工作")
    else:
        print("❌ 后端API: 有问题")
    
    print("\n🔧 建议的解决方案:")
    print("1. 确保后端服务正在运行: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload")
    print("2. 确保前端服务正在运行: npm run dev")
    print("3. 检查CORS配置是否正确")
    print("4. 使用测试页面验证登录: file:///d:/GitHubProjects/myAIProjects/usdt-monitor-platform/test_login.html")
    
    print("\n🌐 访问地址:")
    print("- 后端API文档: http://localhost:8000/api/v1/docs")
    print("- 前端应用: http://localhost:5173 或 http://localhost:3000")
    print("- 登录测试页面: test_login.html")
    
    print("\n📞 如果问题仍然存在:")
    print("1. 检查浏览器控制台错误信息")
    print("2. 检查后端日志输出")
    print("3. 确认防火墙没有阻止端口访问")

if __name__ == "__main__":
    main()
