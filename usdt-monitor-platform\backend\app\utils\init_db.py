import uuid
from sqlalchemy.orm import Session

from app.core.database import SessionLocal, engine
from app.core.security import get_password_hash
from app.core.config import settings
from app.models import Base, User, Role, Permission, user_roles, role_permissions


def init_database():
    """初始化数据库"""
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    db = SessionLocal()
    try:
        # 创建默认权限
        create_default_permissions(db)
        
        # 创建默认角色
        create_default_roles(db)
        
        # 创建超级用户
        create_superuser(db)
        
        print("✅ 数据库初始化完成!")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        db.rollback()
    finally:
        db.close()


def create_default_permissions(db: Session):
    """创建默认权限"""
    permissions_data = [
        # 用户管理权限
        {"name": "user_management", "resource": "users", "action": "manage", "description": "用户管理权限"},
        {"name": "user_read", "resource": "users", "action": "read", "description": "查看用户"},
        {"name": "user_create", "resource": "users", "action": "create", "description": "创建用户"},
        {"name": "user_update", "resource": "users", "action": "update", "description": "更新用户"},
        {"name": "user_delete", "resource": "users", "action": "delete", "description": "删除用户"},
        
        # 系统设置权限
        {"name": "system_settings", "resource": "system", "action": "manage", "description": "系统设置权限"},
        {"name": "system_read", "resource": "system", "action": "read", "description": "查看系统信息"},
        {"name": "system_update", "resource": "system", "action": "update", "description": "更新系统设置"},
        
        # 数据权限
        {"name": "data_read", "resource": "data", "action": "read", "description": "查看数据"},
        {"name": "data_export", "resource": "data", "action": "export", "description": "导出数据"},
        
        # 告警权限
        {"name": "alert_manage", "resource": "alerts", "action": "manage", "description": "管理告警"},
        {"name": "alert_read", "resource": "alerts", "action": "read", "description": "查看告警"},
    ]
    
    for perm_data in permissions_data:
        # 检查权限是否已存在
        existing_perm = db.query(Permission).filter(Permission.name == perm_data["name"]).first()
        if not existing_perm:
            permission = Permission(
                id=str(uuid.uuid4()),
                **perm_data
            )
            db.add(permission)
    
    db.commit()
    print("✅ 默认权限创建完成")


def create_default_roles(db: Session):
    """创建默认角色"""
    # 获取所有权限
    all_permissions = db.query(Permission).all()
    
    # 管理员角色 - 拥有所有权限
    admin_role = db.query(Role).filter(Role.name == "admin").first()
    if not admin_role:
        admin_role = Role(
            id=str(uuid.uuid4()),
            name="admin",
            description="系统管理员，拥有所有权限"
        )
        admin_role.permissions = all_permissions
        db.add(admin_role)
    
    # 普通用户角色 - 基本权限
    user_permissions = [p for p in all_permissions if p.name in [
        "data_read", "alert_read"
    ]]
    user_role = db.query(Role).filter(Role.name == "user").first()
    if not user_role:
        user_role = Role(
            id=str(uuid.uuid4()),
            name="user",
            description="普通用户，拥有基本查看权限"
        )
        user_role.permissions = user_permissions
        db.add(user_role)
    
    # 查看者角色 - 只读权限
    viewer_permissions = [p for p in all_permissions if "read" in p.action]
    viewer_role = db.query(Role).filter(Role.name == "viewer").first()
    if not viewer_role:
        viewer_role = Role(
            id=str(uuid.uuid4()),
            name="viewer",
            description="查看者，只有查看权限"
        )
        viewer_role.permissions = viewer_permissions
        db.add(viewer_role)
    
    db.commit()
    print("✅ 默认角色创建完成")


def create_superuser(db: Session):
    """创建超级用户"""
    # 检查超级用户是否已存在
    superuser = db.query(User).filter(User.email == settings.FIRST_SUPERUSER).first()
    if superuser:
        print("⚠️ 超级用户已存在")
        return
    
    # 获取管理员角色
    admin_role = db.query(Role).filter(Role.name == "admin").first()
    
    # 创建超级用户
    superuser = User(
        id=str(uuid.uuid4()),
        username="admin",
        email=settings.FIRST_SUPERUSER,
        full_name="系统管理员",
        hashed_password=get_password_hash(settings.FIRST_SUPERUSER_PASSWORD),
        is_active=True,
        is_superuser=True,
        is_verified=True
    )
    
    if admin_role:
        superuser.roles = [admin_role]
    
    db.add(superuser)
    db.commit()
    
    print(f"✅ 超级用户创建完成")
    print(f"   邮箱: {settings.FIRST_SUPERUSER}")
    print(f"   密码: {settings.FIRST_SUPERUSER_PASSWORD}")


if __name__ == "__main__":
    init_database()
