// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  role: UserRole;
  isActive: boolean;
  createdAt: string;
  lastLogin?: string;
}

export interface UserRole {
  id: string;
  name: string;
  permissions: Permission[];
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
}

// 认证相关类型
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
}

// USDT数据相关类型
export interface USDTData {
  id: string;
  timestamp: string;
  unixTimestamp: number;
  name: string;
  symbol: string;
  currentPriceUsd: number;
  pricePrecision: string;
  marketCapUsd: number;
  marketCapRank: number;
  totalVolumeUsd: number;
  circulatingSupply: number;
  totalSupply: number;
  priceChange24h: number;
  priceChangePercentage24h: number;
  priceChangeFromLast: number;
  marketCapChange24h: number;
  marketCapChangePercentage24h: number;
  lastUpdated: string;
  dataSource: string;
}

export interface USDTStats {
  latest: USDTData;
  priceHistory: USDTData[];
  volatility24h: number;
  averagePrice24h: number;
  maxPrice24h: number;
  minPrice24h: number;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 系统设置类型
export interface SystemSettings {
  monitoringInterval: number;
  alertThreshold: number;
  dataRetentionDays: number;
  enableNotifications: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
}

// 告警类型
export interface Alert {
  id: string;
  type: 'price_change' | 'volume_change' | 'system_error';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  isRead: boolean;
  userId?: string;
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: 'usdt_update' | 'alert' | 'system_status';
  data: any;
  timestamp: string;
}
