#!/usr/bin/env python3
"""
快速测试脚本 - 验证后端API是否正常工作
"""

import requests
import json

def test_backend():
    base_url = "http://localhost:8000"
    
    print("🔍 测试后端API...")
    
    # 1. 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查: 正常")
        else:
            print(f"❌ 健康检查: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查: 连接失败 - {e}")
        return False
    
    # 2. 测试API文档
    try:
        response = requests.get(f"{base_url}/api/v1/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API文档: 可访问")
        else:
            print(f"❌ API文档: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ API文档: {e}")
    
    # 3. 测试登录API
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        response = requests.post(
            f"{base_url}/api/v1/auth/login",
            json=login_data,
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 登录API: 正常")
                token = data['data'].get('access_token')
                
                # 4. 测试认证API
                headers = {'Authorization': f'Bearer {token}'}
                response = requests.get(
                    f"{base_url}/api/v1/auth/me",
                    headers=headers,
                    timeout=5
                )
                if response.status_code == 200:
                    print("✅ 认证API: 正常")
                else:
                    print(f"❌ 认证API: HTTP {response.status_code}")
                
                # 5. 测试USDT数据API
                response = requests.get(
                    f"{base_url}/api/v1/usdt/current",
                    headers=headers,
                    timeout=10
                )
                if response.status_code == 200:
                    print("✅ USDT数据API: 正常")
                elif response.status_code == 404:
                    print("⚠️ USDT数据API: 暂无数据")
                else:
                    print(f"❌ USDT数据API: HTTP {response.status_code}")
                    
            else:
                print(f"❌ 登录API: 响应格式错误")
        else:
            print(f"❌ 登录API: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"❌ 登录API: {e}")
    
    print("\n🌐 请在浏览器中访问:")
    print(f"- 后端健康检查: {base_url}/health")
    print(f"- API文档: {base_url}/api/v1/docs")
    print(f"- 前端应用: http://localhost:5173")
    print(f"- 前端应用(备用): http://localhost:3000")

if __name__ == "__main__":
    test_backend()
