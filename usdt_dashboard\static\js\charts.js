/**
 * USDT Dashboard 图表模块
 */

const Charts = {
    // 图表实例
    priceChart: null,
    statsChart: null,

    // 图表配置
    config: {
        colors: {
            primary: '#007bff',
            success: '#28a745',
            danger: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                x: {
                    display: true,
                    grid: {
                        display: false
                    }
                },
                y: {
                    display: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                }
            }
        }
    },

    // 初始化
    init() {
        console.log('Charts 初始化...');
        this.initPriceChart();
        this.initStatsChart();
    },

    // 初始化价格图表
    initPriceChart() {
        const ctx = document.getElementById('price-chart');
        if (!ctx) return;

        this.priceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'USDT 价格 (USD)',
                    data: [],
                    borderColor: this.config.colors.primary,
                    backgroundColor: this.config.colors.primary + '20',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4,
                    pointRadius: 2,
                    pointHoverRadius: 6
                }]
            },
            options: {
                ...this.config.options,
                scales: {
                    ...this.config.options.scales,
                    y: {
                        ...this.config.options.scales.y,
                        beginAtZero: false,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toFixed(8);
                            },
                            // 增加刻度密度以显示微小变化
                            maxTicksLimit: 8
                        }
                    }
                },
                plugins: {
                    ...this.config.options.plugins,
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `价格: $${context.parsed.y.toFixed(10)}`;
                            },
                            afterLabel: function(context) {
                                // 显示与前一个点的变化
                                const dataset = context.dataset.data;
                                const currentIndex = context.dataIndex;
                                if (currentIndex > 0) {
                                    const current = dataset[currentIndex];
                                    const previous = dataset[currentIndex - 1];
                                    const change = ((current - previous) / previous * 100);
                                    return `变化: ${change >= 0 ? '+' : ''}${change.toFixed(8)}%`;
                                }
                                return '';
                            }
                        }
                    }
                }
            }
        });
    },

    // 初始化统计图表
    initStatsChart() {
        const ctx = document.getElementById('stats-chart');
        if (!ctx) return;

        this.statsChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['价格变化', '市值变化', '交易量'],
                datasets: [{
                    data: [0, 0, 0],
                    backgroundColor: [
                        this.config.colors.success,
                        this.config.colors.info,
                        this.config.colors.warning
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                return `${label}: ${value.toFixed(4)}%`;
                            }
                        }
                    }
                }
            }
        });
    },

    // 更新价格图表
    updatePriceChart(priceHistory) {
        if (!this.priceChart || !priceHistory || priceHistory.length === 0) return;

        const labels = [];
        const prices = [];

        priceHistory.forEach(record => {
            const time = new Date(record.timestamp);
            labels.push(time.toLocaleTimeString());
            prices.push(parseFloat(record.price || 1.0));
        });

        // 限制数据点数量
        const maxPoints = 50;
        if (labels.length > maxPoints) {
            const step = Math.ceil(labels.length / maxPoints);
            const filteredLabels = [];
            const filteredPrices = [];
            
            for (let i = 0; i < labels.length; i += step) {
                filteredLabels.push(labels[i]);
                filteredPrices.push(prices[i]);
            }
            
            this.priceChart.data.labels = filteredLabels;
            this.priceChart.data.datasets[0].data = filteredPrices;
        } else {
            this.priceChart.data.labels = labels;
            this.priceChart.data.datasets[0].data = prices;
        }

        // 动态调整Y轴范围 - 针对USDT的微小波动优化
        if (prices.length > 0) {
            const minPrice = Math.min(...prices);
            const maxPrice = Math.max(...prices);
            const range = maxPrice - minPrice;

            // 如果价格变化很小（USDT的情况），放大显示范围
            if (range < 0.001) {
                // 对于极小的变化，使用固定的小范围
                const center = (minPrice + maxPrice) / 2;
                const displayRange = Math.max(range * 10, 0.0001); // 至少显示0.0001的范围
                this.priceChart.options.scales.y.min = center - displayRange / 2;
                this.priceChart.options.scales.y.max = center + displayRange / 2;
            } else {
                // 正常情况下的padding
                const padding = range * 0.1;
                this.priceChart.options.scales.y.min = Math.max(0, minPrice - padding);
                this.priceChart.options.scales.y.max = maxPrice + padding;
            }
        }

        this.priceChart.update('none');
    },

    // 更新统计图表
    updateStatsChart(statistics) {
        if (!this.statsChart || !statistics) return;

        const priceChange = Math.abs(parseFloat(statistics.price_change_24h || 0));
        const marketCapChange = Math.abs(parseFloat(statistics.market_cap_change_24h || 0));
        const volumeRatio = statistics.total_volume_24h ? 
            (statistics.total_volume_24h / statistics.market_cap * 100) : 0;

        this.statsChart.data.datasets[0].data = [
            priceChange,
            marketCapChange / 1e9, // 转换为十亿单位
            Math.min(volumeRatio, 100) // 限制在100%以内
        ];

        // 更新标签
        this.statsChart.data.labels = [
            `价格变化 (${priceChange.toFixed(4)}%)`,
            `市值变化 (${(marketCapChange / 1e9).toFixed(2)}B)`,
            `交易量比率 (${volumeRatio.toFixed(2)}%)`
        ];

        this.statsChart.update('none');
    },

    // 创建迷你图表
    createMiniChart(canvasId, data, type = 'line') {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        return new Chart(ctx, {
            type: type,
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        display: false
                    },
                    y: {
                        display: false
                    }
                },
                elements: {
                    point: {
                        radius: 0
                    }
                }
            }
        });
    },

    // 销毁图表
    destroy() {
        if (this.priceChart) {
            this.priceChart.destroy();
            this.priceChart = null;
        }
        if (this.statsChart) {
            this.statsChart.destroy();
            this.statsChart = null;
        }
    },

    // 重新初始化
    reinit() {
        this.destroy();
        this.init();
    }
};

// 导出到全局
window.Charts = Charts;
