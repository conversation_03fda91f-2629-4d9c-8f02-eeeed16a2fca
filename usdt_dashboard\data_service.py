"""
数据服务层 - 处理USDT监控数据
"""
import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging

logger = logging.getLogger(__name__)

class USDTDataService:
    """USDT数据服务类"""
    
    def __init__(self, data_file_path: str):
        self.data_file_path = data_file_path
        self._cache = None
        self._last_modified = None
    
    def _load_data(self) -> List[Dict]:
        """加载数据文件"""
        try:
            if not os.path.exists(self.data_file_path):
                logger.warning(f"数据文件不存在: {self.data_file_path}")
                return []
            
            # 检查文件是否被修改
            current_modified = os.path.getmtime(self.data_file_path)
            if self._cache is None or current_modified != self._last_modified:
                with open(self.data_file_path, 'r', encoding='utf-8') as f:
                    self._cache = json.load(f)
                self._last_modified = current_modified
                logger.info(f"数据已重新加载，共 {len(self._cache)} 条记录")
            
            return self._cache
        except Exception as e:
            logger.error(f"加载数据文件失败: {e}")
            return []
    
    def get_latest_data(self) -> Optional[Dict]:
        """获取最新数据"""
        data = self._load_data()
        return data[-1] if data else None
    
    def get_recent_data(self, hours: int = 24) -> List[Dict]:
        """获取最近N小时的数据"""
        data = self._load_data()
        if not data:
            return []
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_data = []
        
        for record in reversed(data):
            try:
                record_time = datetime.fromisoformat(record['timestamp'])
                if record_time >= cutoff_time:
                    recent_data.append(record)
                else:
                    break
            except (KeyError, ValueError):
                continue
        
        return list(reversed(recent_data))
    
    def get_price_history(self, limit: int = 100) -> List[Dict]:
        """获取价格历史数据"""
        data = self._load_data()
        if not data:
            return []
        
        # 取最后limit条记录
        recent_data = data[-limit:] if len(data) > limit else data
        
        price_history = []
        for record in recent_data:
            try:
                price_history.append({
                    'timestamp': record['timestamp'],
                    'price': float(record.get('current_price_usd', 1.0)),
                    'price_precision': record.get('price_precision', '1.00000000'),
                    'volume': record.get('total_volume_usd', 0),
                    'market_cap': record.get('market_cap_usd', 0)
                })
            except (KeyError, ValueError, TypeError):
                continue
        
        return price_history
    
    def get_statistics(self) -> Dict:
        """获取统计数据"""
        data = self._load_data()
        if not data:
            return {}
        
        latest = data[-1] if data else {}
        recent_24h = self.get_recent_data(24)
        
        # 计算统计指标
        stats = {
            'total_records': len(data),
            'latest_price': latest.get('current_price_usd', 1.0),
            'latest_timestamp': latest.get('timestamp', ''),
            'market_cap': latest.get('market_cap_usd', 0),
            'market_cap_rank': latest.get('market_cap_rank', 0),
            'total_volume_24h': latest.get('total_volume_usd', 0),
            'price_change_24h': latest.get('price_change_percentage_24h', 0),
            'market_cap_change_24h': latest.get('market_cap_change_percentage_24h', 0),
            'circulating_supply': latest.get('circulating_supply', 0),
            'records_24h': len(recent_24h)
        }
        
        # 计算价格波动范围
        if recent_24h:
            prices = [float(r.get('current_price_usd', 1.0)) for r in recent_24h]
            stats['price_min_24h'] = min(prices)
            stats['price_max_24h'] = max(prices)
            stats['price_avg_24h'] = sum(prices) / len(prices)
        
        return stats
    
    def get_dashboard_data(self) -> Dict:
        """获取仪表板所需的完整数据"""
        return {
            'latest': self.get_latest_data(),
            'statistics': self.get_statistics(),
            'price_history': self.get_price_history(),
            'recent_data': self.get_recent_data(1)  # 最近1小时
        }
