# USDT监控平台

一个实时监控USDT价格波动的Web应用系统，提供准确、及时的USDT市场数据和分析功能。

## 🚀 功能特性

### 核心功能
- **实时价格监控**: 实时显示USDT当前价格和市场数据
- **历史数据分析**: 提供多时间段的历史价格数据和趋势分析
- **用户管理系统**: 支持用户注册、登录、权限控制
- **数据可视化**: 直观的图表展示价格变化趋势
- **告警通知**: 价格异常波动告警功能
- **数据导出**: 支持历史数据导出为CSV/Excel格式

### 技术特性
- **高精度数据**: 价格精确到小数点后6位
- **多数据源**: 集成多个可靠的数据源确保数据准确性
- **实时更新**: 30秒自动更新数据
- **响应式设计**: 支持桌面和移动设备
- **安全认证**: JWT Token认证和RBAC权限控制
- **容器化部署**: 支持Docker一键部署

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │    │  后端 (FastAPI)  │    │ 数据收集器 (Python)│
│                 │    │                 │    │                 │
│ - 用户界面      │◄──►│ - REST API      │◄──►│ - 数据采集      │
│ - 状态管理      │    │ - 用户认证      │    │ - 数据处理      │
│ - 路由管理      │    │ - 权限控制      │    │ - 定时任务      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   数据库 (SQLite) │    │ 外部API (CoinGecko)│
                       │                 │    │                 │
                       │ - 用户数据      │    │ - 市场数据      │
                       │ - USDT数据      │    │ - 价格信息      │
                       │ - 系统配置      │    │ - 交易量数据    │
                       └─────────────────┘    └─────────────────┘
```

## 🛠️ 技术栈

### 前端
- **React 18** - 用户界面框架
- **TypeScript** - 类型安全的JavaScript
- **Ant Design** - UI组件库
- **Redux Toolkit** - 状态管理
- **Vite** - 构建工具
- **Axios** - HTTP客户端

### 后端
- **Python 3.8+** - 编程语言
- **FastAPI** - 现代Web框架
- **SQLAlchemy** - ORM框架
- **Pydantic** - 数据验证
- **JWT** - 身份认证
- **Uvicorn** - ASGI服务器

### 数据库
- **SQLite** - 开发环境数据库
- **PostgreSQL** - 生产环境数据库

### 部署
- **Docker** - 容器化
- **Nginx** - 反向代理
- **Let's Encrypt** - SSL证书

## 📦 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- Git

### 1. 克隆项目
```bash
git clone https://github.com/your-repo/usdt-monitor-platform.git
cd usdt-monitor-platform
```

### 2. 后端设置
```bash
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 初始化数据库
python -m app.utils.init_db

# 启动后端服务
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 3. 前端设置
```bash
cd frontend

# 安装依赖
npm install

# 启动前端服务
npm run dev
```

### 4. 数据收集器设置
```bash
cd data-collector

# 启动数据收集器
python collector.py
```

### 5. 访问应用
- 前端地址: http://localhost:5173
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/api/v1/docs

### 6. 默认账户
- 用户名: `admin`
- 邮箱: `<EMAIL>`
- 密码: `admin123`

## 🐳 Docker部署

### 使用Docker Compose一键部署
```bash
# 克隆项目
git clone https://github.com/your-repo/usdt-monitor-platform.git
cd usdt-monitor-platform

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 服务访问
- 前端: http://localhost
- 后端API: http://localhost:8000
- 数据库: localhost:5432

## 📖 文档

- [系统设计报告](docs/系统设计报告.md) - 详细的系统架构和设计说明
- [需求文档](docs/需求文档.md) - 功能需求和非功能需求
- [操作手册](docs/操作手册.md) - 用户和管理员操作指南
- [部署指南](docs/部署指南.md) - 详细的部署和配置说明

## 🔧 配置说明

### 环境变量配置

#### 后端配置 (.env)
```env
# 应用配置
PROJECT_NAME=USDT监控平台
VERSION=1.0.0
DEBUG=true
HOST=0.0.0.0
PORT=8000

# 安全配置
SECRET_KEY=your-super-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# 数据库配置
DATABASE_URL=sqlite:///./usdt_monitor.db

# CORS配置
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:5173"]

# 超级用户配置
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=admin123
```

#### 前端配置 (.env.local)
```env
VITE_API_BASE_URL=http://localhost:8000/api/v1
```

## 🧪 测试

### 运行测试
```bash
# 后端测试
cd backend
pytest

# 前端测试
cd frontend
npm test
```

### 测试覆盖率
```bash
# 后端测试覆盖率
cd backend
pytest --cov=app

# 前端测试覆盖率
cd frontend
npm run test:coverage
```

## 📊 API文档

### 主要API端点

#### 认证API
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `GET /api/v1/auth/me` - 获取当前用户信息

#### USDT数据API
- `GET /api/v1/usdt/current` - 获取当前USDT数据
- `GET /api/v1/usdt/historical` - 获取历史数据
- `GET /api/v1/usdt/stats` - 获取统计数据

#### 用户管理API
- `GET /api/v1/users` - 获取用户列表
- `POST /api/v1/users` - 创建用户
- `PUT /api/v1/users/{user_id}` - 更新用户

### 在线API文档
启动后端服务后，访问 http://localhost:8000/api/v1/docs 查看完整的API文档。

## 🔒 安全特性

- **JWT认证**: 基于JSON Web Token的身份认证
- **RBAC权限**: 基于角色的访问控制
- **密码加密**: BCrypt密码哈希
- **HTTPS支持**: SSL/TLS加密传输
- **CORS保护**: 跨域请求保护
- **输入验证**: 防止SQL注入和XSS攻击

## 🚀 性能优化

- **数据库索引**: 优化查询性能
- **连接池**: 数据库连接池管理
- **缓存策略**: Redis缓存热点数据
- **代码分割**: 前端代码懒加载
- **CDN加速**: 静态资源CDN分发

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 开发规范
- 遵循代码风格指南
- 编写单元测试
- 更新相关文档
- 确保CI/CD通过

## 📝 更新日志

### v1.0.0 (2024-01-01)
- ✨ 初始版本发布
- 🚀 实时USDT价格监控
- 👥 用户管理系统
- 📊 数据可视化
- 🔐 安全认证
- 📱 响应式设计

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- **项目主页**: https://github.com/your-repo/usdt-monitor-platform
- **问题反馈**: https://github.com/your-repo/usdt-monitor-platform/issues
- **邮箱**: <EMAIL>

## 🙏 致谢

感谢以下开源项目和服务:
- [CoinGecko API](https://coingecko.com) - 提供加密货币市场数据
- [React](https://reactjs.org) - 前端框架
- [FastAPI](https://fastapi.tiangolo.com) - 后端框架
- [Ant Design](https://ant.design) - UI组件库

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
