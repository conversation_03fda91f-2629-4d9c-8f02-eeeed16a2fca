from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.schemas.user import User, UserCreate, UserUpdate, Role, Permission
from app.schemas.common import ApiResponse, PaginatedResponse
from app.services.user_service import user_service
from app.api.deps import get_current_user, require_user_management

router = APIRouter()


@router.get("", response_model=ApiResponse[PaginatedResponse[User]])
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    role_id: Optional[str] = Query(None, description="角色ID"),
    is_active: Optional[bool] = Query(None, description="是否活跃"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_user_management)
):
    """获取用户列表"""
    skip = (page - 1) * page_size
    users, total = user_service.get_users(
        db, skip=skip, limit=page_size, search=search, 
        role_id=role_id, is_active=is_active
    )
    
    paginated_data = PaginatedResponse.create(
        items=users,
        total=total,
        page=page,
        page_size=page_size
    )
    
    return ApiResponse(success=True, data=paginated_data)


@router.get("/{user_id}", response_model=ApiResponse[User])
async def get_user(
    user_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_user_management)
):
    """获取用户详情"""
    user = user_service.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    return ApiResponse(success=True, data=user)


@router.post("", response_model=ApiResponse[User])
async def create_user(
    user_create: UserCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_user_management)
):
    """创建用户"""
    try:
        user = user_service.create_user(db, user_create)
        return ApiResponse(success=True, data=user)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/{user_id}", response_model=ApiResponse[User])
async def update_user(
    user_id: str,
    user_update: UserUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_user_management)
):
    """更新用户"""
    try:
        user = user_service.update_user(db, user_id, user_update)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        return ApiResponse(success=True, data=user)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{user_id}", response_model=ApiResponse[dict])
async def delete_user(
    user_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_user_management)
):
    """删除用户"""
    # 不能删除自己
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己的账户"
        )
    
    success = user_service.delete_user(db, user_id)
    if not success:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    return ApiResponse(success=True, data={"message": "用户删除成功"})


@router.patch("/{user_id}/activate", response_model=ApiResponse[User])
async def activate_user(
    user_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_user_management)
):
    """激活用户"""
    user = user_service.activate_user(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    return ApiResponse(success=True, data=user)


@router.patch("/{user_id}/deactivate", response_model=ApiResponse[User])
async def deactivate_user(
    user_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_user_management)
):
    """停用用户"""
    # 不能停用自己
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能停用自己的账户"
        )
    
    user = user_service.deactivate_user(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    return ApiResponse(success=True, data=user)


@router.get("/{user_id}/permissions", response_model=ApiResponse[List[Permission]])
async def get_user_permissions(
    user_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_user_management)
):
    """获取用户权限"""
    permissions = user_service.get_user_permissions(db, user_id)
    return ApiResponse(success=True, data=permissions)


# 角色管理
@router.get("/roles/all", response_model=ApiResponse[List[Role]])
async def get_roles(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_user_management)
):
    """获取所有角色"""
    roles = user_service.get_roles(db)
    return ApiResponse(success=True, data=roles)


@router.get("/permissions/all", response_model=ApiResponse[List[Permission]])
async def get_permissions(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_user_management)
):
    """获取所有权限"""
    permissions = user_service.get_permissions(db)
    return ApiResponse(success=True, data=permissions)
