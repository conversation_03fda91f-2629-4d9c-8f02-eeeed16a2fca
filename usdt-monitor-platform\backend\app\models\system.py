from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Float, <PERSON><PERSON>ey, Integer, String, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class SystemSettings(Base):
    __tablename__ = "system_settings"

    id = Column(String, primary_key=True, index=True)
    
    # 监控设置
    monitoring_interval = Column(Integer, default=30)  # 监控间隔（秒）
    alert_threshold = Column(Float, default=0.1)       # 告警阈值（百分比）
    data_retention_days = Column(Integer, default=30)  # 数据保留天数
    
    # 通知设置
    enable_notifications = Column(Boolean, default=True)
    email_notifications = Column(Boolean, default=True)
    sms_notifications = Column(Boolean, default=False)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    updated_by = Column(String, ForeignKey("users.id"), nullable=True)


class Alert(Base):
    __tablename__ = "alerts"

    id = Column(String, primary_key=True, index=True)
    
    # 告警信息
    type = Column(String, nullable=False, index=True)  # 'price_change', 'volume_change', 'system_error'
    severity = Column(String, nullable=False, index=True)  # 'low', 'medium', 'high', 'critical'
    title = Column(String, nullable=False)
    message = Column(Text, nullable=False)
    
    # 状态
    is_read = Column(Boolean, default=False, index=True)
    is_resolved = Column(Boolean, default=False)
    
    # 关联数据
    related_data_id = Column(String, nullable=True)  # 关联的USDT数据ID
    user_id = Column(String, ForeignKey("users.id"), nullable=True)
    
    # 时间戳
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    read_at = Column(DateTime(timezone=True), nullable=True)
    resolved_at = Column(DateTime(timezone=True), nullable=True)
    
    # 关联关系
    user = relationship("User", back_populates="alerts")
    
    def __repr__(self):
        return f"<Alert(id={self.id}, type={self.type}, severity={self.severity})>"
