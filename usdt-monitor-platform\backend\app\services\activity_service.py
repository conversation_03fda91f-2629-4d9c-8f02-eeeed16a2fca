import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_

from app.models.activity import UserActivity
from app.schemas.activity import UserActivityCreate


class ActivityService:
    def log_activity(
        self,
        db: Session,
        user_id: str,
        action: str,
        resource: Optional[str] = None,
        resource_id: Optional[str] = None,
        description: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        request_method: Optional[str] = None,
        request_path: Optional[str] = None,
        extra_data: Optional[dict] = None
    ) -> UserActivity:
        """记录用户活动"""
        activity = UserActivity(
            id=str(uuid.uuid4()),
            user_id=user_id,
            action=action,
            resource=resource,
            resource_id=resource_id,
            description=description,
            ip_address=ip_address,
            user_agent=user_agent,
            request_method=request_method,
            request_path=request_path,
            extra_data=extra_data
        )
        
        db.add(activity)
        db.commit()
        db.refresh(activity)
        return activity

    def get_user_activities(
        self,
        db: Session,
        user_id: str,
        skip: int = 0,
        limit: int = 100,
        action: Optional[str] = None,
        resource: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Tuple[List[UserActivity], int]:
        """获取用户活动记录"""
        query = db.query(UserActivity).filter(UserActivity.user_id == user_id)
        
        # 过滤条件
        if action:
            query = query.filter(UserActivity.action == action)
        if resource:
            query = query.filter(UserActivity.resource == resource)
        if start_date:
            query = query.filter(UserActivity.timestamp >= start_date)
        if end_date:
            query = query.filter(UserActivity.timestamp <= end_date)
        
        # 按时间倒序
        query = query.order_by(desc(UserActivity.timestamp))
        
        total = query.count()
        activities = query.offset(skip).limit(limit).all()
        
        return activities, total

    def get_all_activities(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        user_id: Optional[str] = None,
        action: Optional[str] = None,
        resource: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Tuple[List[UserActivity], int]:
        """获取所有活动记录"""
        query = db.query(UserActivity)
        
        # 过滤条件
        if user_id:
            query = query.filter(UserActivity.user_id == user_id)
        if action:
            query = query.filter(UserActivity.action == action)
        if resource:
            query = query.filter(UserActivity.resource == resource)
        if start_date:
            query = query.filter(UserActivity.timestamp >= start_date)
        if end_date:
            query = query.filter(UserActivity.timestamp <= end_date)
        
        # 按时间倒序
        query = query.order_by(desc(UserActivity.timestamp))
        
        total = query.count()
        activities = query.offset(skip).limit(limit).all()
        
        return activities, total

    def cleanup_old_activities(self, db: Session, retention_days: int = 90) -> int:
        """清理旧的活动记录"""
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        
        deleted_count = db.query(UserActivity).filter(
            UserActivity.timestamp < cutoff_date
        ).delete()
        
        db.commit()
        return deleted_count

    def get_activity_stats(self, db: Session, user_id: Optional[str] = None) -> dict:
        """获取活动统计"""
        query = db.query(UserActivity)
        
        if user_id:
            query = query.filter(UserActivity.user_id == user_id)
        
        # 获取最近24小时的活动
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=24)
        
        recent_activities = query.filter(
            UserActivity.timestamp >= start_time
        ).count()
        
        # 获取总活动数
        total_activities = query.count()
        
        # 获取最常见的操作
        # 这里简化处理，实际可以使用更复杂的查询
        
        return {
            "recent_activities_24h": recent_activities,
            "total_activities": total_activities,
            "most_common_actions": [],  # 可以添加更详细的统计
        }


activity_service = ActivityService()
