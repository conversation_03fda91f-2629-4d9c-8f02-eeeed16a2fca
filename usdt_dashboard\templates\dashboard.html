<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <!-- 自定义样式 -->
    <link href="{{ url_for('static', filename='css/dashboard.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>{{ title }}
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="fas fa-circle text-success me-1" id="connection-status"></i>
                    <span id="connection-text">连接中...</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 顶部价格显示 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body text-center">
                        <h1 class="display-4 mb-0">
                            $<span id="current-price">1.00000000</span>
                        </h1>
                        <p class="lead mb-0">
                            <span id="price-change" class="badge bg-success">+0.00%</span>
                            <small class="ms-2">24小时变化</small>
                        </p>
                        <small class="text-light">
                            最后更新: <span id="last-update">--</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 关键指标卡片 -->
        <div class="row mb-4">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">市值</h6>
                                <h4 id="market-cap">$0</h4>
                            </div>
                            <div class="text-primary">
                                <i class="fas fa-coins fa-2x"></i>
                            </div>
                        </div>
                        <small class="text-muted">
                            排名: #<span id="market-rank">0</span>
                        </small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">24h交易量</h6>
                                <h4 id="volume-24h">$0</h4>
                            </div>
                            <div class="text-info">
                                <i class="fas fa-exchange-alt fa-2x"></i>
                            </div>
                        </div>
                        <small class="text-muted">
                            <span id="volume-change">0%</span> 24h变化
                        </small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">流通供应量</h6>
                                <h4 id="circulating-supply">0</h4>
                            </div>
                            <div class="text-warning">
                                <i class="fas fa-layer-group fa-2x"></i>
                            </div>
                        </div>
                        <small class="text-muted">USDT</small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">数据记录</h6>
                                <h4 id="total-records">0</h4>
                            </div>
                            <div class="text-success">
                                <i class="fas fa-database fa-2x"></i>
                            </div>
                        </div>
                        <small class="text-muted">
                            24h: <span id="records-24h">0</span> 条
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="row mb-4">
            <div class="col-lg-8 mb-3">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>价格趋势
                        </h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary active" data-period="1">1小时</button>
                            <button type="button" class="btn btn-outline-primary" data-period="6">6小时</button>
                            <button type="button" class="btn btn-outline-primary" data-period="24">24小时</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="price-chart" height="300"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-3">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>24小时统计
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="stats-chart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最新数据表格 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>最新数据
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>价格 (USD)</th>
                                        <th>市值</th>
                                        <th>24h变化</th>
                                        <th>交易量</th>
                                    </tr>
                                </thead>
                                <tbody id="data-table">
                                    <!-- 数据将通过JavaScript填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定义脚本 -->
    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
    <script src="{{ url_for('static', filename='js/charts.js') }}"></script>
    <script src="{{ url_for('static', filename='js/websocket.js') }}"></script>
    
    <script>
        // 初始化应用
        document.addEventListener('DOMContentLoaded', function() {
            Dashboard.init();
            Charts.init();
            WebSocketClient.init();
        });
    </script>
</body>
</html>
