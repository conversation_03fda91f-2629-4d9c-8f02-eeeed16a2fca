# USDT监控平台系统设计报告

## 1. 项目概述

### 1.1 项目背景
USDT监控平台是一个实时监控USDT价格波动的Web应用系统，旨在为用户提供准确、及时的USDT市场数据和分析。

### 1.2 项目目标
- 实时监控USDT价格变化
- 提供历史数据分析和可视化
- 支持多用户管理和权限控制
- 提供告警和通知功能
- 确保数据的准确性和系统的稳定性

### 1.3 技术栈
- **前端**: React 18 + TypeScript + Ant Design + Redux Toolkit
- **后端**: Python FastAPI + SQLAlchemy + Pydantic
- **数据库**: SQLite (开发环境) / PostgreSQL (生产环境)
- **数据收集**: Python异步爬虫 + 多数据源API
- **部署**: Docker + Docker Compose

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │    │  后端 (FastAPI)  │    │ 数据收集器 (Python)│
│                 │    │                 │    │                 │
│ - 用户界面      │◄──►│ - REST API      │◄──►│ - 数据采集      │
│ - 状态管理      │    │ - 用户认证      │    │ - 数据处理      │
│ - 路由管理      │    │ - 权限控制      │    │ - 定时任务      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   数据库 (SQLite) │    │ 外部API (CoinGecko)│
                       │                 │    │                 │
                       │ - 用户数据      │    │ - 市场数据      │
                       │ - USDT数据      │    │ - 价格信息      │
                       │ - 系统配置      │    │ - 交易量数据    │
                       └─────────────────┘    └─────────────────┘
```

### 2.2 模块划分

#### 2.2.1 前端模块
- **认证模块**: 用户登录、注册、权限验证
- **仪表板模块**: 数据展示、图表可视化
- **用户管理模块**: 用户CRUD、角色权限管理
- **系统设置模块**: 系统配置、告警设置

#### 2.2.2 后端模块
- **认证服务**: JWT token管理、用户验证
- **用户服务**: 用户管理、角色权限
- **USDT数据服务**: 数据查询、统计分析
- **系统服务**: 配置管理、告警处理

#### 2.2.3 数据收集模块
- **数据采集器**: 从多个数据源获取USDT数据
- **数据处理器**: 数据清洗、格式化、验证
- **存储管理器**: 数据持久化、历史数据管理

## 3. 数据库设计

### 3.1 核心表结构

#### 3.1.1 用户相关表
```sql
-- 用户表
CREATE TABLE users (
    id VARCHAR PRIMARY KEY,
    username VARCHAR UNIQUE NOT NULL,
    email VARCHAR UNIQUE NOT NULL,
    hashed_password VARCHAR NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_superuser BOOLEAN DEFAULT FALSE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME,
    last_login DATETIME,
    full_name VARCHAR,
    avatar_url VARCHAR
);

-- 角色表
CREATE TABLE roles (
    id VARCHAR PRIMARY KEY,
    name VARCHAR UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME
);

-- 权限表
CREATE TABLE permissions (
    id VARCHAR PRIMARY KEY,
    name VARCHAR UNIQUE NOT NULL,
    resource VARCHAR NOT NULL,
    action VARCHAR NOT NULL,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 用户角色关联表
CREATE TABLE user_roles (
    user_id VARCHAR,
    role_id VARCHAR,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (role_id) REFERENCES roles(id)
);

-- 角色权限关联表
CREATE TABLE role_permissions (
    role_id VARCHAR,
    permission_id VARCHAR,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id),
    FOREIGN KEY (permission_id) REFERENCES permissions(id)
);
```

#### 3.1.2 USDT数据表
```sql
CREATE TABLE usdt_data (
    id VARCHAR PRIMARY KEY,
    timestamp DATETIME NOT NULL,
    unix_timestamp BIGINT NOT NULL,
    name VARCHAR,
    symbol VARCHAR,
    current_price_usd FLOAT NOT NULL,
    price_precision VARCHAR,
    market_cap_usd BIGINT,
    market_cap_rank INTEGER,
    total_volume_usd BIGINT,
    circulating_supply FLOAT,
    total_supply FLOAT,
    price_change_24h FLOAT,
    price_change_percentage_24h FLOAT,
    price_change_from_last FLOAT,
    market_cap_change_24h BIGINT,
    market_cap_change_percentage_24h FLOAT,
    data_source VARCHAR,
    last_updated VARCHAR,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_usdt_data_timestamp ON usdt_data(timestamp);
CREATE INDEX idx_usdt_data_unix_timestamp ON usdt_data(unix_timestamp);
```

#### 3.1.3 系统管理表
```sql
-- 系统设置表
CREATE TABLE system_settings (
    id VARCHAR PRIMARY KEY,
    monitoring_interval INTEGER,
    alert_threshold FLOAT,
    data_retention_days INTEGER,
    enable_notifications BOOLEAN,
    email_notifications BOOLEAN,
    sms_notifications BOOLEAN,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME,
    updated_by VARCHAR,
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- 告警表
CREATE TABLE alerts (
    id VARCHAR PRIMARY KEY,
    type VARCHAR NOT NULL,
    severity VARCHAR NOT NULL,
    title VARCHAR NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    is_resolved BOOLEAN DEFAULT FALSE,
    related_data_id VARCHAR,
    user_id VARCHAR,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    read_at DATETIME,
    resolved_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 用户活动日志表
CREATE TABLE user_activities (
    id VARCHAR PRIMARY KEY,
    user_id VARCHAR NOT NULL,
    action VARCHAR NOT NULL,
    resource VARCHAR,
    resource_id VARCHAR,
    description TEXT,
    ip_address VARCHAR,
    user_agent TEXT,
    request_method VARCHAR,
    request_path VARCHAR,
    extra_data JSON,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 3.2 数据关系图
```
users ──┐
        ├── user_roles ──── roles ──── role_permissions ──── permissions
        │
        ├── alerts
        │
        ├── user_activities
        │
        └── system_settings (updated_by)

usdt_data (独立表，存储市场数据)
```

## 4. API设计

### 4.1 认证API
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/logout` - 用户登出
- `GET /api/v1/auth/me` - 获取当前用户信息
- `POST /api/v1/auth/refresh` - 刷新token
- `POST /api/v1/auth/change-password` - 修改密码

### 4.2 USDT数据API
- `GET /api/v1/usdt/current` - 获取当前USDT数据
- `GET /api/v1/usdt/historical` - 获取历史数据
- `GET /api/v1/usdt/stats` - 获取统计数据
- `GET /api/v1/usdt/price-history` - 获取价格历史
- `GET /api/v1/usdt/volatility` - 获取波动率数据
- `GET /api/v1/usdt/health` - 获取系统健康状态

### 4.3 用户管理API
- `GET /api/v1/users` - 获取用户列表
- `POST /api/v1/users` - 创建用户
- `GET /api/v1/users/{user_id}` - 获取用户详情
- `PUT /api/v1/users/{user_id}` - 更新用户
- `DELETE /api/v1/users/{user_id}` - 删除用户
- `GET /api/v1/users/roles/all` - 获取所有角色
- `GET /api/v1/users/permissions/all` - 获取所有权限

### 4.4 系统管理API
- `GET /api/v1/system/settings` - 获取系统设置
- `PUT /api/v1/system/settings` - 更新系统设置
- `GET /api/v1/system/alerts` - 获取告警列表
- `POST /api/v1/system/alerts` - 创建告警
- `GET /api/v1/system/status` - 获取系统状态
- `GET /api/v1/system/metrics` - 获取系统指标

## 5. 安全设计

### 5.1 认证机制
- JWT Token认证
- Access Token (30分钟有效期)
- Refresh Token (7天有效期)
- 密码BCrypt加密

### 5.2 权限控制
- 基于角色的访问控制 (RBAC)
- 细粒度权限管理
- API级别权限验证
- 前端路由权限控制

### 5.3 数据安全
- SQL注入防护
- XSS攻击防护
- CSRF保护
- 敏感数据加密存储

## 6. 性能优化

### 6.1 前端优化
- 代码分割和懒加载
- 组件缓存和优化
- 状态管理优化
- 图片和资源优化

### 6.2 后端优化
- 数据库查询优化
- 缓存策略
- 异步处理
- 连接池管理

### 6.3 数据库优化
- 索引优化
- 查询优化
- 数据分页
- 历史数据清理

## 7. 监控和日志

### 7.1 系统监控
- 应用性能监控
- 数据库性能监控
- 服务器资源监控
- 业务指标监控

### 7.2 日志管理
- 结构化日志
- 日志级别管理
- 日志轮转和清理
- 错误追踪和告警

## 8. 部署架构

### 8.1 开发环境
- 本地开发环境
- SQLite数据库
- 热重载支持
- 调试工具集成

### 8.2 生产环境
- Docker容器化部署
- PostgreSQL数据库
- Nginx反向代理
- SSL/TLS加密
- 负载均衡
- 自动备份和恢复

## 9. 扩展性设计

### 9.1 水平扩展
- 微服务架构支持
- 数据库分片
- 缓存集群
- 负载均衡

### 9.2 功能扩展
- 插件化架构
- 多币种支持
- 高级分析功能
- 移动端支持

## 10. 风险评估

### 10.1 技术风险
- 数据源可用性
- 系统性能瓶颈
- 安全漏洞风险
- 第三方依赖风险

### 10.2 业务风险
- 数据准确性
- 服务可用性
- 用户体验
- 合规性要求

### 10.3 风险缓解
- 多数据源备份
- 容错机制
- 安全审计
- 监控告警
- 灾难恢复计划
