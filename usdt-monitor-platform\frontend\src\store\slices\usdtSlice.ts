import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { USDTData, USDTStats } from '@/types'
import { usdtService } from '@/services/usdtService'

interface USDTState {
  currentData: USDTData | null
  historicalData: USDTData[]
  stats: USDTStats | null
  loading: boolean
  error: string | null
  isConnected: boolean
  lastUpdate: string | null
}

const initialState: USDTState = {
  currentData: null,
  historicalData: [],
  stats: null,
  loading: false,
  error: null,
  isConnected: false,
  lastUpdate: null,
}

// 异步actions
export const fetchCurrentData = createAsyncThunk(
  'usdt/fetchCurrentData',
  async (_, { rejectWithValue }) => {
    try {
      return await usdtService.getCurrentData()
    } catch (error: any) {
      return rejectWithValue(error.message || '获取当前数据失败')
    }
  }
)

export const fetchHistoricalData = createAsyncThunk(
  'usdt/fetchHistoricalData',
  async (params: { startDate?: string; endDate?: string; limit?: number }, { rejectWithValue }) => {
    try {
      return await usdtService.getHistoricalData(params)
    } catch (error: any) {
      return rejectWithValue(error.message || '获取历史数据失败')
    }
  }
)

export const fetchStats = createAsyncThunk(
  'usdt/fetchStats',
  async (_, { rejectWithValue }) => {
    try {
      return await usdtService.getStats()
    } catch (error: any) {
      return rejectWithValue(error.message || '获取统计数据失败')
    }
  }
)

const usdtSlice = createSlice({
  name: 'usdt',
  initialState,
  reducers: {
    updateCurrentData: (state, action: PayloadAction<USDTData>) => {
      state.currentData = action.payload
      state.lastUpdate = new Date().toISOString()
      
      // 更新历史数据
      if (state.historicalData.length > 0) {
        const lastItem = state.historicalData[state.historicalData.length - 1]
        if (lastItem.id !== action.payload.id) {
          state.historicalData.push(action.payload)
          // 保持最近1000条记录
          if (state.historicalData.length > 1000) {
            state.historicalData = state.historicalData.slice(-1000)
          }
        }
      } else {
        state.historicalData.push(action.payload)
      }
    },
    setConnectionStatus: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload
    },
    clearError: (state) => {
      state.error = null
    },
    addHistoricalData: (state, action: PayloadAction<USDTData>) => {
      state.historicalData.push(action.payload)
      // 保持最近1000条记录
      if (state.historicalData.length > 1000) {
        state.historicalData = state.historicalData.slice(-1000)
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch current data
      .addCase(fetchCurrentData.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchCurrentData.fulfilled, (state, action) => {
        state.loading = false
        state.currentData = action.payload
        state.lastUpdate = new Date().toISOString()
        state.error = null
      })
      .addCase(fetchCurrentData.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Fetch historical data
      .addCase(fetchHistoricalData.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchHistoricalData.fulfilled, (state, action) => {
        state.loading = false
        state.historicalData = action.payload
        state.error = null
      })
      .addCase(fetchHistoricalData.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Fetch stats
      .addCase(fetchStats.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchStats.fulfilled, (state, action) => {
        state.loading = false
        state.stats = action.payload
        state.error = null
      })
      .addCase(fetchStats.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  },
})

export const { 
  updateCurrentData, 
  setConnectionStatus, 
  clearError, 
  addHistoricalData 
} = usdtSlice.actions

export default usdtSlice.reducer
