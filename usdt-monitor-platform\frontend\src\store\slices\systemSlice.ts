import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { SystemSettings, Alert } from '@/types'
import { systemService } from '@/services/systemService'

interface SystemState {
  settings: SystemSettings | null
  alerts: Alert[]
  unreadAlertCount: number
  systemStatus: 'online' | 'offline' | 'maintenance'
  loading: boolean
  error: string | null
}

const initialState: SystemState = {
  settings: null,
  alerts: [],
  unreadAlertCount: 0,
  systemStatus: 'online',
  loading: false,
  error: null,
}

// 异步actions
export const fetchSettings = createAsyncThunk(
  'system/fetchSettings',
  async (_, { rejectWithValue }) => {
    try {
      return await systemService.getSettings()
    } catch (error: any) {
      return rejectWithValue(error.message || '获取系统设置失败')
    }
  }
)

export const updateSettings = createAsyncThunk(
  'system/updateSettings',
  async (settings: Partial<SystemSettings>, { rejectWithValue }) => {
    try {
      return await systemService.updateSettings(settings)
    } catch (error: any) {
      return rejectWithValue(error.message || '更新系统设置失败')
    }
  }
)

export const fetchAlerts = createAsyncThunk(
  'system/fetchAlerts',
  async (params: { page?: number; pageSize?: number; unreadOnly?: boolean }, { rejectWithValue }) => {
    try {
      return await systemService.getAlerts(params)
    } catch (error: any) {
      return rejectWithValue(error.message || '获取告警信息失败')
    }
  }
)

export const markAlertAsRead = createAsyncThunk(
  'system/markAlertAsRead',
  async (alertId: string, { rejectWithValue }) => {
    try {
      await systemService.markAlertAsRead(alertId)
      return alertId
    } catch (error: any) {
      return rejectWithValue(error.message || '标记告警失败')
    }
  }
)

const systemSlice = createSlice({
  name: 'system',
  initialState,
  reducers: {
    addAlert: (state, action: PayloadAction<Alert>) => {
      state.alerts.unshift(action.payload)
      if (!action.payload.isRead) {
        state.unreadAlertCount += 1
      }
      // 保持最近100条告警
      if (state.alerts.length > 100) {
        state.alerts = state.alerts.slice(0, 100)
      }
    },
    setSystemStatus: (state, action: PayloadAction<'online' | 'offline' | 'maintenance'>) => {
      state.systemStatus = action.payload
    },
    clearError: (state) => {
      state.error = null
    },
    markAllAlertsAsRead: (state) => {
      state.alerts.forEach(alert => {
        alert.isRead = true
      })
      state.unreadAlertCount = 0
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch settings
      .addCase(fetchSettings.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchSettings.fulfilled, (state, action) => {
        state.loading = false
        state.settings = action.payload
        state.error = null
      })
      .addCase(fetchSettings.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Update settings
      .addCase(updateSettings.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateSettings.fulfilled, (state, action) => {
        state.loading = false
        state.settings = action.payload
        state.error = null
      })
      .addCase(updateSettings.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Fetch alerts
      .addCase(fetchAlerts.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchAlerts.fulfilled, (state, action) => {
        state.loading = false
        state.alerts = action.payload.items
        state.unreadAlertCount = action.payload.items.filter(alert => !alert.isRead).length
        state.error = null
      })
      .addCase(fetchAlerts.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Mark alert as read
      .addCase(markAlertAsRead.fulfilled, (state, action) => {
        const alert = state.alerts.find(a => a.id === action.payload)
        if (alert && !alert.isRead) {
          alert.isRead = true
          state.unreadAlertCount -= 1
        }
      })
  },
})

export const { 
  addAlert, 
  setSystemStatus, 
  clearError, 
  markAllAlertsAsRead 
} = systemSlice.actions

export default systemSlice.reducer
