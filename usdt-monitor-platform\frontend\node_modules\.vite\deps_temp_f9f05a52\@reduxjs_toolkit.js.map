{"version": 3, "sources": ["../../redux/src/utils/formatProdErrorMessage.ts", "../../redux/src/utils/symbol-observable.ts", "../../redux/src/utils/actionTypes.ts", "../../redux/src/utils/isPlainObject.ts", "../../redux/src/utils/kindOf.ts", "../../redux/src/createStore.ts", "../../redux/src/utils/warning.ts", "../../redux/src/combineReducers.ts", "../../redux/src/bindActionCreators.ts", "../../redux/src/compose.ts", "../../redux/src/applyMiddleware.ts", "../../redux/src/utils/isAction.ts", "../../immer/src/utils/env.ts", "../../immer/src/utils/errors.ts", "../../immer/src/utils/common.ts", "../../immer/src/utils/plugins.ts", "../../immer/src/core/scope.ts", "../../immer/src/core/finalize.ts", "../../immer/src/core/proxy.ts", "../../immer/src/core/immerClass.ts", "../../immer/src/core/current.ts", "../../immer/src/plugins/patches.ts", "../../immer/src/plugins/mapset.ts", "../../immer/src/immer.ts", "../../reselect/src/devModeChecks/identityFunctionCheck.ts", "../../reselect/src/devModeChecks/inputStabilityCheck.ts", "../../reselect/src/devModeChecks/setGlobalDevModeChecks.ts", "../../reselect/src/utils.ts", "../../reselect/src/autotrackMemoize/autotracking.ts", "../../reselect/src/autotrackMemoize/tracking.ts", "../../reselect/src/autotrackMemoize/proxy.ts", "../../reselect/src/lruMemoize.ts", "../../reselect/src/autotrackMemoize/autotrackMemoize.ts", "../../reselect/src/weakMapMemoize.ts", "../../reselect/src/createSelectorCreator.ts", "../../reselect/src/createStructuredSelector.ts", "../../redux-thunk/dist/redux-thunk.mjs", "../../@reduxjs/toolkit/src/index.ts", "../../@reduxjs/toolkit/src/createDraftSafeSelector.ts", "../../@reduxjs/toolkit/src/configureStore.ts", "../../@reduxjs/toolkit/src/devtoolsExtension.ts", "../../@reduxjs/toolkit/src/getDefaultMiddleware.ts", "../../@reduxjs/toolkit/src/createAction.ts", "../../@reduxjs/toolkit/src/tsHelpers.ts", "../../@reduxjs/toolkit/src/actionCreatorInvariantMiddleware.ts", "../../@reduxjs/toolkit/src/utils.ts", "../../@reduxjs/toolkit/src/immutableStateInvariantMiddleware.ts", "../../@reduxjs/toolkit/src/serializableStateInvariantMiddleware.ts", "../../@reduxjs/toolkit/src/autoBatchEnhancer.ts", "../../@reduxjs/toolkit/src/getDefaultEnhancers.ts", "../../@reduxjs/toolkit/src/createReducer.ts", "../../@reduxjs/toolkit/src/mapBuilders.ts", "../../@reduxjs/toolkit/src/matchers.ts", "../../@reduxjs/toolkit/src/nanoid.ts", "../../@reduxjs/toolkit/src/createAsyncThunk.ts", "../../@reduxjs/toolkit/src/createSlice.ts", "../../@reduxjs/toolkit/src/entities/entity_state.ts", "../../@reduxjs/toolkit/src/entities/state_selectors.ts", "../../@reduxjs/toolkit/src/entities/state_adapter.ts", "../../@reduxjs/toolkit/src/entities/utils.ts", "../../@reduxjs/toolkit/src/entities/unsorted_state_adapter.ts", "../../@reduxjs/toolkit/src/entities/sorted_state_adapter.ts", "../../@reduxjs/toolkit/src/entities/create_adapter.ts", "../../@reduxjs/toolkit/src/listenerMiddleware/index.ts", "../../@reduxjs/toolkit/src/listenerMiddleware/exceptions.ts", "../../@reduxjs/toolkit/src/listenerMiddleware/utils.ts", "../../@reduxjs/toolkit/src/listenerMiddleware/task.ts", "../../@reduxjs/toolkit/src/dynamicMiddleware/index.ts", "../../@reduxjs/toolkit/src/combineSlices.ts", "../../@reduxjs/toolkit/src/formatProdErrorMessage.ts"], "sourcesContent": ["/**\n * Adapted from React: https://github.com/facebook/react/blob/master/packages/shared/formatProdErrorMessage.js\n *\n * Do not require this module directly! Use normal throw error calls. These messages will be replaced with error codes\n * during build.\n * @param {number} code\n */\nexport function formatProdErrorMessage(code: number) {\n  return `Minified Redux error #${code}; visit https://redux.js.org/Errors?code=${code} for the full message or ` + 'use the non-minified dev environment for full errors. ';\n}", "declare global {\n  interface SymbolConstructor {\n    readonly observable: symbol;\n  }\n}\nconst $$observable = /* #__PURE__ */(() => typeof Symbol === 'function' && Symbol.observable || '@@observable')();\nexport default $$observable;", "/**\n * These are private action types reserved by Redux.\n * For any unknown actions, you must return the current state.\n * If the current state is undefined, you must return the initial state.\n * Do not reference these action types directly in your code.\n */\n\nconst randomString = () => Math.random().toString(36).substring(7).split('').join('.');\nconst ActionTypes = {\n  INIT: `@@redux/INIT${/* #__PURE__ */randomString()}`,\n  REPLACE: `@@redux/REPLACE${/* #__PURE__ */randomString()}`,\n  PROBE_UNKNOWN_ACTION: () => `@@redux/PROBE_UNKNOWN_ACTION${randomString()}`\n};\nexport default ActionTypes;", "/**\n * @param obj The object to inspect.\n * @returns True if the argument appears to be a plain object.\n */\nexport default function isPlainObject(obj: any): obj is object {\n  if (typeof obj !== 'object' || obj === null) return false;\n  let proto = obj;\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n  return Object.getPrototypeOf(obj) === proto || Object.getPrototypeOf(obj) === null;\n}", "// Inlined / shortened version of `kindOf` from https://github.com/jonschlinkert/kind-of\nexport function miniKindOf(val: any): string {\n  if (val === void 0) return 'undefined';\n  if (val === null) return 'null';\n  const type = typeof val;\n  switch (type) {\n    case 'boolean':\n    case 'string':\n    case 'number':\n    case 'symbol':\n    case 'function':\n      {\n        return type;\n      }\n  }\n  if (Array.isArray(val)) return 'array';\n  if (isDate(val)) return 'date';\n  if (isError(val)) return 'error';\n  const constructorName = ctorName(val);\n  switch (constructorName) {\n    case 'Symbol':\n    case 'Promise':\n    case 'WeakMap':\n    case 'WeakSet':\n    case 'Map':\n    case 'Set':\n      return constructorName;\n  }\n\n  // other\n  return Object.prototype.toString.call(val).slice(8, -1).toLowerCase().replace(/\\s/g, '');\n}\nfunction ctorName(val: any): string | null {\n  return typeof val.constructor === 'function' ? val.constructor.name : null;\n}\nfunction isError(val: any) {\n  return val instanceof Error || typeof val.message === 'string' && val.constructor && typeof val.constructor.stackTraceLimit === 'number';\n}\nfunction isDate(val: any) {\n  if (val instanceof Date) return true;\n  return typeof val.toDateString === 'function' && typeof val.getDate === 'function' && typeof val.setDate === 'function';\n}\nexport function kindOf(val: any) {\n  let typeOfVal: string = typeof val;\n  if (process.env.NODE_ENV !== 'production') {\n    typeOfVal = miniKindOf(val);\n  }\n  return typeOfVal;\n}", "import { formatProdErrorMessage as _formatProdErrorMessage13 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage12 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage11 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage10 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage9 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage8 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage7 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage6 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage5 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage4 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage3 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage2 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage } from \"src/utils/formatProdErrorMessage\";\nimport $$observable from './utils/symbol-observable';\nimport { Store, StoreEnhancer, Dispatch, Observer, ListenerCallback, UnknownIfNonSpecific } from './types/store';\nimport { Action } from './types/actions';\nimport { Reducer } from './types/reducers';\nimport ActionTypes from './utils/actionTypes';\nimport isPlainObject from './utils/isPlainObject';\nimport { kindOf } from './utils/kindOf';\n\n/**\n * @deprecated\n *\n * **We recommend using the `configureStore` method\n * of the `@reduxjs/toolkit` package**, which replaces `createStore`.\n *\n * Redux Toolkit is our recommended approach for writing Redux logic today,\n * including store setup, reducers, data fetching, and more.\n *\n * **For more details, please read this Redux docs page:**\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * `configureStore` from Redux Toolkit is an improved version of `createStore` that\n * simplifies setup and helps avoid common bugs.\n *\n * You should not be using the `redux` core package by itself today, except for learning purposes.\n * The `createStore` method from the core `redux` package will not be removed, but we encourage\n * all users to migrate to using Redux Toolkit for all Redux code.\n *\n * If you want to use `createStore` without this visual deprecation warning, use\n * the `legacy_createStore` import instead:\n *\n * `import { legacy_createStore as createStore} from 'redux'`\n *\n */\nexport function createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}>(reducer: Reducer<S, A>, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext;\n/**\n * @deprecated\n *\n * **We recommend using the `configureStore` method\n * of the `@reduxjs/toolkit` package**, which replaces `createStore`.\n *\n * Redux Toolkit is our recommended approach for writing Redux logic today,\n * including store setup, reducers, data fetching, and more.\n *\n * **For more details, please read this Redux docs page:**\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * `configureStore` from Redux Toolkit is an improved version of `createStore` that\n * simplifies setup and helps avoid common bugs.\n *\n * You should not be using the `redux` core package by itself today, except for learning purposes.\n * The `createStore` method from the core `redux` package will not be removed, but we encourage\n * all users to migrate to using Redux Toolkit for all Redux code.\n *\n * If you want to use `createStore` without this visual deprecation warning, use\n * the `legacy_createStore` import instead:\n *\n * `import { legacy_createStore as createStore} from 'redux'`\n *\n */\nexport function createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}, PreloadedState = S>(reducer: Reducer<S, A, PreloadedState>, preloadedState?: PreloadedState | undefined, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext;\nexport function createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}, PreloadedState = S>(reducer: Reducer<S, A, PreloadedState>, preloadedState?: PreloadedState | StoreEnhancer<Ext, StateExt> | undefined, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext {\n  if (typeof reducer !== 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(2) : `Expected the root reducer to be a function. Instead, received: '${kindOf(reducer)}'`);\n  }\n  if (typeof preloadedState === 'function' && typeof enhancer === 'function' || typeof enhancer === 'function' && typeof arguments[3] === 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(0) : 'It looks like you are passing several store enhancers to ' + 'createStore(). This is not supported. Instead, compose them ' + 'together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.');\n  }\n  if (typeof preloadedState === 'function' && typeof enhancer === 'undefined') {\n    enhancer = (preloadedState as StoreEnhancer<Ext, StateExt>);\n    preloadedState = undefined;\n  }\n  if (typeof enhancer !== 'undefined') {\n    if (typeof enhancer !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(1) : `Expected the enhancer to be a function. Instead, received: '${kindOf(enhancer)}'`);\n    }\n    return enhancer(createStore)(reducer, (preloadedState as PreloadedState | undefined));\n  }\n  let currentReducer = reducer;\n  let currentState: S | PreloadedState | undefined = (preloadedState as PreloadedState | undefined);\n  let currentListeners: Map<number, ListenerCallback> | null = new Map();\n  let nextListeners = currentListeners;\n  let listenerIdCounter = 0;\n  let isDispatching = false;\n\n  /**\n   * This makes a shallow copy of currentListeners so we can use\n   * nextListeners as a temporary list while dispatching.\n   *\n   * This prevents any bugs around consumers calling\n   * subscribe/unsubscribe in the middle of a dispatch.\n   */\n  function ensureCanMutateNextListeners() {\n    if (nextListeners === currentListeners) {\n      nextListeners = new Map();\n      currentListeners.forEach((listener, key) => {\n        nextListeners.set(key, listener);\n      });\n    }\n  }\n\n  /**\n   * Reads the state tree managed by the store.\n   *\n   * @returns The current state tree of your application.\n   */\n  function getState(): S {\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage4(3) : 'You may not call store.getState() while the reducer is executing. ' + 'The reducer has already received the state as an argument. ' + 'Pass it down from the top reducer instead of reading it from the store.');\n    }\n    return (currentState as S);\n  }\n\n  /**\n   * Adds a change listener. It will be called any time an action is dispatched,\n   * and some part of the state tree may potentially have changed. You may then\n   * call `getState()` to read the current state tree inside the callback.\n   *\n   * You may call `dispatch()` from a change listener, with the following\n   * caveats:\n   *\n   * 1. The subscriptions are snapshotted just before every `dispatch()` call.\n   * If you subscribe or unsubscribe while the listeners are being invoked, this\n   * will not have any effect on the `dispatch()` that is currently in progress.\n   * However, the next `dispatch()` call, whether nested or not, will use a more\n   * recent snapshot of the subscription list.\n   *\n   * 2. The listener should not expect to see all state changes, as the state\n   * might have been updated multiple times during a nested `dispatch()` before\n   * the listener is called. It is, however, guaranteed that all subscribers\n   * registered before the `dispatch()` started will be called with the latest\n   * state by the time it exits.\n   *\n   * @param listener A callback to be invoked on every dispatch.\n   * @returns A function to remove this change listener.\n   */\n  function subscribe(listener: () => void) {\n    if (typeof listener !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage5(4) : `Expected the listener to be a function. Instead, received: '${kindOf(listener)}'`);\n    }\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage6(5) : 'You may not call store.subscribe() while the reducer is executing. ' + 'If you would like to be notified after the store has been updated, subscribe from a ' + 'component and invoke store.getState() in the callback to access the latest state. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n    }\n    let isSubscribed = true;\n    ensureCanMutateNextListeners();\n    const listenerId = listenerIdCounter++;\n    nextListeners.set(listenerId, listener);\n    return function unsubscribe() {\n      if (!isSubscribed) {\n        return;\n      }\n      if (isDispatching) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage7(6) : 'You may not unsubscribe from a store listener while the reducer is executing. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n      }\n      isSubscribed = false;\n      ensureCanMutateNextListeners();\n      nextListeners.delete(listenerId);\n      currentListeners = null;\n    };\n  }\n\n  /**\n   * Dispatches an action. It is the only way to trigger a state change.\n   *\n   * The `reducer` function, used to create the store, will be called with the\n   * current state tree and the given `action`. Its return value will\n   * be considered the **next** state of the tree, and the change listeners\n   * will be notified.\n   *\n   * The base implementation only supports plain object actions. If you want to\n   * dispatch a Promise, an Observable, a thunk, or something else, you need to\n   * wrap your store creating function into the corresponding middleware. For\n   * example, see the documentation for the `redux-thunk` package. Even the\n   * middleware will eventually dispatch plain object actions using this method.\n   *\n   * @param action A plain object representing “what changed”. It is\n   * a good idea to keep actions serializable so you can record and replay user\n   * sessions, or use the time travelling `redux-devtools`. An action must have\n   * a `type` property which may not be `undefined`. It is a good idea to use\n   * string constants for action types.\n   *\n   * @returns For convenience, the same action object you dispatched.\n   *\n   * Note that, if you use a custom middleware, it may wrap `dispatch()` to\n   * return something else (for example, a Promise you can await).\n   */\n  function dispatch(action: A) {\n    if (!isPlainObject(action)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage8(7) : `Actions must be plain objects. Instead, the actual type was: '${kindOf(action)}'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.`);\n    }\n    if (typeof action.type === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage9(8) : 'Actions may not have an undefined \"type\" property. You may have misspelled an action type string constant.');\n    }\n    if (typeof action.type !== 'string') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage10(17) : `Action \"type\" property must be a string. Instead, the actual type was: '${kindOf(action.type)}'. Value was: '${action.type}' (stringified)`);\n    }\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage11(9) : 'Reducers may not dispatch actions.');\n    }\n    try {\n      isDispatching = true;\n      currentState = currentReducer(currentState, action);\n    } finally {\n      isDispatching = false;\n    }\n    const listeners = currentListeners = nextListeners;\n    listeners.forEach(listener => {\n      listener();\n    });\n    return action;\n  }\n\n  /**\n   * Replaces the reducer currently used by the store to calculate the state.\n   *\n   * You might need this if your app implements code splitting and you want to\n   * load some of the reducers dynamically. You might also need this if you\n   * implement a hot reloading mechanism for Redux.\n   *\n   * @param nextReducer The reducer for the store to use instead.\n   */\n  function replaceReducer(nextReducer: Reducer<S, A>): void {\n    if (typeof nextReducer !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage12(10) : `Expected the nextReducer to be a function. Instead, received: '${kindOf(nextReducer)}`);\n    }\n    currentReducer = ((nextReducer as unknown) as Reducer<S, A, PreloadedState>);\n\n    // This action has a similar effect to ActionTypes.INIT.\n    // Any reducers that existed in both the new and old rootReducer\n    // will receive the previous state. This effectively populates\n    // the new state tree with any relevant data from the old one.\n    dispatch(({\n      type: ActionTypes.REPLACE\n    } as A));\n  }\n\n  /**\n   * Interoperability point for observable/reactive libraries.\n   * @returns A minimal observable of state changes.\n   * For more information, see the observable proposal:\n   * https://github.com/tc39/proposal-observable\n   */\n  function observable() {\n    const outerSubscribe = subscribe;\n    return {\n      /**\n       * The minimal observable subscription method.\n       * @param observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */\n      subscribe(observer: unknown) {\n        if (typeof observer !== 'object' || observer === null) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage13(11) : `Expected the observer to be an object. Instead, received: '${kindOf(observer)}'`);\n        }\n        function observeState() {\n          const observerAsObserver = (observer as Observer<S>);\n          if (observerAsObserver.next) {\n            observerAsObserver.next(getState());\n          }\n        }\n        observeState();\n        const unsubscribe = outerSubscribe(observeState);\n        return {\n          unsubscribe\n        };\n      },\n      [$$observable]() {\n        return this;\n      }\n    };\n  }\n\n  // When a store is created, an \"INIT\" action is dispatched so that every\n  // reducer returns their initial state. This effectively populates\n  // the initial state tree.\n  dispatch(({\n    type: ActionTypes.INIT\n  } as A));\n  const store = (({\n    dispatch: (dispatch as Dispatch<A>),\n    subscribe,\n    getState,\n    replaceReducer,\n    [$$observable]: observable\n  } as unknown) as Store<S, A, StateExt> & Ext);\n  return store;\n}\n\n/**\n * Creates a Redux store that holds the state tree.\n *\n * **We recommend using `configureStore` from the\n * `@reduxjs/toolkit` package**, which replaces `createStore`:\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * The only way to change the data in the store is to call `dispatch()` on it.\n *\n * There should only be a single store in your app. To specify how different\n * parts of the state tree respond to actions, you may combine several reducers\n * into a single reducer function by using `combineReducers`.\n *\n * @param {Function} reducer A function that returns the next state tree, given\n * the current state tree and the action to handle.\n *\n * @param {any} [preloadedState] The initial state. You may optionally specify it\n * to hydrate the state from the server in universal apps, or to restore a\n * previously serialized user session.\n * If you use `combineReducers` to produce the root reducer function, this must be\n * an object with the same shape as `combineReducers` keys.\n *\n * @param {Function} [enhancer] The store enhancer. You may optionally specify it\n * to enhance the store with third-party capabilities such as middleware,\n * time travel, persistence, etc. The only store enhancer that ships with Redux\n * is `applyMiddleware()`.\n *\n * @returns {Store} A Redux store that lets you read the state, dispatch actions\n * and subscribe to changes.\n */\nexport function legacy_createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}>(reducer: Reducer<S, A>, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext;\n/**\n * Creates a Redux store that holds the state tree.\n *\n * **We recommend using `configureStore` from the\n * `@reduxjs/toolkit` package**, which replaces `createStore`:\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * The only way to change the data in the store is to call `dispatch()` on it.\n *\n * There should only be a single store in your app. To specify how different\n * parts of the state tree respond to actions, you may combine several reducers\n * into a single reducer function by using `combineReducers`.\n *\n * @param {Function} reducer A function that returns the next state tree, given\n * the current state tree and the action to handle.\n *\n * @param {any} [preloadedState] The initial state. You may optionally specify it\n * to hydrate the state from the server in universal apps, or to restore a\n * previously serialized user session.\n * If you use `combineReducers` to produce the root reducer function, this must be\n * an object with the same shape as `combineReducers` keys.\n *\n * @param {Function} [enhancer] The store enhancer. You may optionally specify it\n * to enhance the store with third-party capabilities such as middleware,\n * time travel, persistence, etc. The only store enhancer that ships with Redux\n * is `applyMiddleware()`.\n *\n * @returns {Store} A Redux store that lets you read the state, dispatch actions\n * and subscribe to changes.\n */\nexport function legacy_createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}, PreloadedState = S>(reducer: Reducer<S, A, PreloadedState>, preloadedState?: PreloadedState | undefined, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext;\nexport function legacy_createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}, PreloadedState = S>(reducer: Reducer<S, A>, preloadedState?: PreloadedState | StoreEnhancer<Ext, StateExt> | undefined, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext {\n  return createStore(reducer, (preloadedState as any), enhancer);\n}", "/**\n * Prints a warning in the console if it exists.\n *\n * @param message The warning message.\n */\nexport default function warning(message: string): void {\n  /* eslint-disable no-console */\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    console.error(message);\n  }\n  /* eslint-enable no-console */\n  try {\n    // This error was thrown as a convenience so that if you enable\n    // \"break on all exceptions\" in your console,\n    // it would pause the execution at this line.\n    throw new Error(message);\n  } catch (e) {} // eslint-disable-line no-empty\n}", "import { formatProdErrorMessage as _formatProdErrorMessage3 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage2 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage } from \"src/utils/formatProdErrorMessage\";\nimport { Action } from './types/actions';\nimport { ActionFromReducersMapObject, PreloadedStateShapeFromReducersMapObject, Reducer, StateFromReducersMapObject } from './types/reducers';\nimport ActionTypes from './utils/actionTypes';\nimport isPlainObject from './utils/isPlainObject';\nimport warning from './utils/warning';\nimport { kindOf } from './utils/kindOf';\nfunction getUnexpectedStateShapeWarningMessage(inputState: object, reducers: {\n  [key: string]: Reducer<any, any, any>;\n}, action: Action, unexpectedKeyCache: {\n  [key: string]: true;\n}) {\n  const reducerKeys = Object.keys(reducers);\n  const argumentName = action && action.type === ActionTypes.INIT ? 'preloadedState argument passed to createStore' : 'previous state received by the reducer';\n  if (reducerKeys.length === 0) {\n    return 'Store does not have a valid reducer. Make sure the argument passed ' + 'to combineReducers is an object whose values are reducers.';\n  }\n  if (!isPlainObject(inputState)) {\n    return `The ${argumentName} has unexpected type of \"${kindOf(inputState)}\". Expected argument to be an object with the following ` + `keys: \"${reducerKeys.join('\", \"')}\"`;\n  }\n  const unexpectedKeys = Object.keys(inputState).filter(key => !reducers.hasOwnProperty(key) && !unexpectedKeyCache[key]);\n  unexpectedKeys.forEach(key => {\n    unexpectedKeyCache[key] = true;\n  });\n  if (action && action.type === ActionTypes.REPLACE) return;\n  if (unexpectedKeys.length > 0) {\n    return `Unexpected ${unexpectedKeys.length > 1 ? 'keys' : 'key'} ` + `\"${unexpectedKeys.join('\", \"')}\" found in ${argumentName}. ` + `Expected to find one of the known reducer keys instead: ` + `\"${reducerKeys.join('\", \"')}\". Unexpected keys will be ignored.`;\n  }\n}\nfunction assertReducerShape(reducers: {\n  [key: string]: Reducer<any, any, any>;\n}) {\n  Object.keys(reducers).forEach(key => {\n    const reducer = reducers[key];\n    const initialState = reducer(undefined, {\n      type: ActionTypes.INIT\n    });\n    if (typeof initialState === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(12) : `The slice reducer for key \"${key}\" returned undefined during initialization. ` + `If the state passed to the reducer is undefined, you must ` + `explicitly return the initial state. The initial state may ` + `not be undefined. If you don't want to set a value for this reducer, ` + `you can use null instead of undefined.`);\n    }\n    if (typeof reducer(undefined, {\n      type: ActionTypes.PROBE_UNKNOWN_ACTION()\n    }) === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(13) : `The slice reducer for key \"${key}\" returned undefined when probed with a random type. ` + `Don't try to handle '${ActionTypes.INIT}' or other actions in \"redux/*\" ` + `namespace. They are considered private. Instead, you must return the ` + `current state for any unknown actions, unless it is undefined, ` + `in which case you must return the initial state, regardless of the ` + `action type. The initial state may not be undefined, but can be null.`);\n    }\n  });\n}\n\n/**\n * Turns an object whose values are different reducer functions, into a single\n * reducer function. It will call every child reducer, and gather their results\n * into a single state object, whose keys correspond to the keys of the passed\n * reducer functions.\n *\n * @template S Combined state object type.\n *\n * @param reducers An object whose values correspond to different reducer\n *   functions that need to be combined into one. One handy way to obtain it\n *   is to use `import * as reducers` syntax. The reducers may never\n *   return undefined for any action. Instead, they should return their\n *   initial state if the state passed to them was undefined, and the current\n *   state for any unrecognized action.\n *\n * @returns A reducer function that invokes every reducer inside the passed\n *   object, and builds a state object with the same shape.\n */\nexport default function combineReducers<M>(reducers: M): M[keyof M] extends Reducer<any, any, any> | undefined ? Reducer<StateFromReducersMapObject<M>, ActionFromReducersMapObject<M>, Partial<PreloadedStateShapeFromReducersMapObject<M>>> : never;\nexport default function combineReducers(reducers: {\n  [key: string]: Reducer<any, any, any>;\n}) {\n  const reducerKeys = Object.keys(reducers);\n  const finalReducers: {\n    [key: string]: Reducer<any, any, any>;\n  } = {};\n  for (let i = 0; i < reducerKeys.length; i++) {\n    const key = reducerKeys[i];\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof reducers[key] === 'undefined') {\n        warning(`No reducer provided for key \"${key}\"`);\n      }\n    }\n    if (typeof reducers[key] === 'function') {\n      finalReducers[key] = reducers[key];\n    }\n  }\n  const finalReducerKeys = Object.keys(finalReducers);\n\n  // This is used to make sure we don't warn about the same\n  // keys multiple times.\n  let unexpectedKeyCache: {\n    [key: string]: true;\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    unexpectedKeyCache = {};\n  }\n  let shapeAssertionError: unknown;\n  try {\n    assertReducerShape(finalReducers);\n  } catch (e) {\n    shapeAssertionError = e;\n  }\n  return function combination(state: StateFromReducersMapObject<typeof reducers> = {}, action: Action) {\n    if (shapeAssertionError) {\n      throw shapeAssertionError;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache);\n      if (warningMessage) {\n        warning(warningMessage);\n      }\n    }\n    let hasChanged = false;\n    const nextState: StateFromReducersMapObject<typeof reducers> = {};\n    for (let i = 0; i < finalReducerKeys.length; i++) {\n      const key = finalReducerKeys[i];\n      const reducer = finalReducers[key];\n      const previousStateForKey = state[key];\n      const nextStateForKey = reducer(previousStateForKey, action);\n      if (typeof nextStateForKey === 'undefined') {\n        const actionType = action && action.type;\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(14) : `When called with an action of type ${actionType ? `\"${String(actionType)}\"` : '(unknown type)'}, the slice reducer for key \"${key}\" returned undefined. ` + `To ignore an action, you must explicitly return the previous state. ` + `If you want this reducer to hold no value, you can return null instead of undefined.`);\n      }\n      nextState[key] = nextStateForKey;\n      hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n    }\n    hasChanged = hasChanged || finalReducerKeys.length !== Object.keys(state).length;\n    return hasChanged ? nextState : state;\n  };\n}", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"src/utils/formatProdErrorMessage\";\nimport { Dispatch } from './types/store';\nimport { ActionCreator, ActionCreatorsMapObject, Action } from './types/actions';\nimport { kindOf } from './utils/kindOf';\nfunction bindActionCreator<A extends Action>(actionCreator: ActionCreator<A>, dispatch: Dispatch<A>) {\n  return function (this: any, ...args: any[]) {\n    return dispatch(actionCreator.apply(this, args));\n  };\n}\n\n/**\n * Turns an object whose values are action creators, into an object with the\n * same keys, but with every function wrapped into a `dispatch` call so they\n * may be invoked directly. This is just a convenience method, as you can call\n * `store.dispatch(MyActionCreators.doSomething())` yourself just fine.\n *\n * For convenience, you can also pass an action creator as the first argument,\n * and get a dispatch wrapped function in return.\n *\n * @param actionCreators An object whose values are action\n * creator functions. One handy way to obtain it is to use `import * as`\n * syntax. You may also pass a single function.\n *\n * @param dispatch The `dispatch` function available on your Redux\n * store.\n *\n * @returns The object mimicking the original object, but with\n * every action creator wrapped into the `dispatch` call. If you passed a\n * function as `actionCreators`, the return value will also be a single\n * function.\n */\nexport default function bindActionCreators<A, C extends ActionCreator<A>>(actionCreator: C, dispatch: Dispatch): C;\nexport default function bindActionCreators<A extends ActionCreator<any>, B extends ActionCreator<any>>(actionCreator: A, dispatch: Dispatch): B;\nexport default function bindActionCreators<A, M extends ActionCreatorsMapObject<A>>(actionCreators: M, dispatch: Dispatch): M;\nexport default function bindActionCreators<M extends ActionCreatorsMapObject, N extends ActionCreatorsMapObject>(actionCreators: M, dispatch: Dispatch): N;\nexport default function bindActionCreators(actionCreators: ActionCreator<any> | ActionCreatorsMapObject, dispatch: Dispatch) {\n  if (typeof actionCreators === 'function') {\n    return bindActionCreator(actionCreators, dispatch);\n  }\n  if (typeof actionCreators !== 'object' || actionCreators === null) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(16) : `bindActionCreators expected an object or a function, but instead received: '${kindOf(actionCreators)}'. ` + `Did you write \"import ActionCreators from\" instead of \"import * as ActionCreators from\"?`);\n  }\n  const boundActionCreators: ActionCreatorsMapObject = {};\n  for (const key in actionCreators) {\n    const actionCreator = actionCreators[key];\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = bindActionCreator(actionCreator, dispatch);\n    }\n  }\n  return boundActionCreators;\n}", "type Func<T extends any[], R> = (...a: T) => R;\n\n/**\n * Composes single-argument functions from right to left. The rightmost\n * function can take multiple arguments as it provides the signature for the\n * resulting composite function.\n *\n * @param funcs The functions to compose.\n * @returns A function obtained by composing the argument functions from right\n *   to left. For example, `compose(f, g, h)` is identical to doing\n *   `(...args) => f(g(h(...args)))`.\n */\nexport default function compose(): <R>(a: R) => R;\nexport default function compose<F extends Function>(f: F): F;\n\n/* two functions */\nexport default function compose<A, T extends any[], R>(f1: (a: A) => R, f2: Func<T, A>): Func<T, R>;\n\n/* three functions */\nexport default function compose<A, B, T extends any[], R>(f1: (b: B) => R, f2: (a: A) => B, f3: Func<T, A>): Func<T, R>;\n\n/* four functions */\nexport default function compose<A, B, C, T extends any[], R>(f1: (c: C) => R, f2: (b: B) => C, f3: (a: A) => B, f4: Func<T, A>): Func<T, R>;\n\n/* rest */\nexport default function compose<R>(f1: (a: any) => R, ...funcs: Function[]): (...args: any[]) => R;\nexport default function compose<R>(...funcs: Function[]): (...args: any[]) => R;\nexport default function compose(...funcs: Function[]) {\n  if (funcs.length === 0) {\n    // infer the argument type so it is usable in inference down the line\n    return <T,>(arg: T) => arg;\n  }\n  if (funcs.length === 1) {\n    return funcs[0];\n  }\n  return funcs.reduce((a, b) => (...args: any) => a(b(...args)));\n}", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"src/utils/formatProdErrorMessage\";\nimport compose from './compose';\nimport { Middleware, MiddlewareAPI } from './types/middleware';\nimport { StoreEnhancer, Dispatch } from './types/store';\n\n/**\n * Creates a store enhancer that applies middleware to the dispatch method\n * of the Redux store. This is handy for a variety of tasks, such as expressing\n * asynchronous actions in a concise manner, or logging every action payload.\n *\n * See `redux-thunk` package as an example of the Redux middleware.\n *\n * Because middleware is potentially asynchronous, this should be the first\n * store enhancer in the composition chain.\n *\n * Note that each middleware will be given the `dispatch` and `getState` functions\n * as named arguments.\n *\n * @param middlewares The middleware chain to be applied.\n * @returns A store enhancer applying the middleware.\n *\n * @template Ext Dispatch signature added by a middleware.\n * @template S The type of the state supported by a middleware.\n */\nexport default function applyMiddleware(): StoreEnhancer;\nexport default function applyMiddleware<Ext1, S>(middleware1: Middleware<Ext1, S, any>): StoreEnhancer<{\n  dispatch: Ext1;\n}>;\nexport default function applyMiddleware<Ext1, Ext2, S>(middleware1: Middleware<Ext1, S, any>, middleware2: Middleware<Ext2, S, any>): StoreEnhancer<{\n  dispatch: Ext1 & Ext2;\n}>;\nexport default function applyMiddleware<Ext1, Ext2, Ext3, S>(middleware1: Middleware<Ext1, S, any>, middleware2: Middleware<Ext2, S, any>, middleware3: Middleware<Ext3, S, any>): StoreEnhancer<{\n  dispatch: Ext1 & Ext2 & Ext3;\n}>;\nexport default function applyMiddleware<Ext1, Ext2, Ext3, Ext4, S>(middleware1: Middleware<Ext1, S, any>, middleware2: Middleware<Ext2, S, any>, middleware3: Middleware<Ext3, S, any>, middleware4: Middleware<Ext4, S, any>): StoreEnhancer<{\n  dispatch: Ext1 & Ext2 & Ext3 & Ext4;\n}>;\nexport default function applyMiddleware<Ext1, Ext2, Ext3, Ext4, Ext5, S>(middleware1: Middleware<Ext1, S, any>, middleware2: Middleware<Ext2, S, any>, middleware3: Middleware<Ext3, S, any>, middleware4: Middleware<Ext4, S, any>, middleware5: Middleware<Ext5, S, any>): StoreEnhancer<{\n  dispatch: Ext1 & Ext2 & Ext3 & Ext4 & Ext5;\n}>;\nexport default function applyMiddleware<Ext, S = any>(...middlewares: Middleware<any, S, any>[]): StoreEnhancer<{\n  dispatch: Ext;\n}>;\nexport default function applyMiddleware(...middlewares: Middleware[]): StoreEnhancer<any> {\n  return createStore => (reducer, preloadedState) => {\n    const store = createStore(reducer, preloadedState);\n    let dispatch: Dispatch = () => {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(15) : 'Dispatching while constructing your middleware is not allowed. ' + 'Other middleware would not be applied to this dispatch.');\n    };\n    const middlewareAPI: MiddlewareAPI = {\n      getState: store.getState,\n      dispatch: (action, ...args) => dispatch(action, ...args)\n    };\n    const chain = middlewares.map(middleware => middleware(middlewareAPI));\n    dispatch = compose<typeof dispatch>(...chain)(store.dispatch);\n    return {\n      ...store,\n      dispatch\n    };\n  };\n}", "import { Action } from '../types/actions';\nimport isPlainObject from './isPlainObject';\nexport default function isAction(action: unknown): action is Action<string> {\n  return isPlainObject(action) && 'type' in action && typeof (action as Record<'type', unknown>).type === 'string';\n}", "// Should be no imports here!\n\n/**\n * The sentinel value returned by producers to replace the draft with undefined.\n */\nexport const NOTHING: unique symbol = Symbol.for(\"immer-nothing\")\n\n/**\n * To let Immer treat your class instances as plain immutable objects\n * (albeit with a custom prototype), you must define either an instance property\n * or a static property on each of your custom classes.\n *\n * Otherwise, your class instance will never be drafted, which means it won't be\n * safe to mutate in a produce callback.\n */\nexport const DRAFTABLE: unique symbol = Symbol.for(\"immer-draftable\")\n\nexport const DRAFT_STATE: unique symbol = Symbol.for(\"immer-state\")\n", "export const errors =\n\tprocess.env.NODE_ENV !== \"production\"\n\t\t? [\n\t\t\t\t// All error codes, starting by 0:\n\t\t\t\tfunction(plugin: string) {\n\t\t\t\t\treturn `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`\n\t\t\t\t},\n\t\t\t\tfunction(thing: string) {\n\t\t\t\t\treturn `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`\n\t\t\t\t},\n\t\t\t\t\"This object has been frozen and should not be mutated\",\n\t\t\t\tfunction(data: any) {\n\t\t\t\t\treturn (\n\t\t\t\t\t\t\"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" +\n\t\t\t\t\t\tdata\n\t\t\t\t\t)\n\t\t\t\t},\n\t\t\t\t\"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n\t\t\t\t\"Immer forbids circular references\",\n\t\t\t\t\"The first or second argument to `produce` must be a function\",\n\t\t\t\t\"The third argument to `produce` must be a function or undefined\",\n\t\t\t\t\"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n\t\t\t\t\"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n\t\t\t\tfunction(thing: string) {\n\t\t\t\t\treturn `'current' expects a draft, got: ${thing}`\n\t\t\t\t},\n\t\t\t\t\"Object.defineProperty() cannot be used on an Immer draft\",\n\t\t\t\t\"Object.setPrototypeOf() cannot be used on an Immer draft\",\n\t\t\t\t\"Immer only supports deleting array indices\",\n\t\t\t\t\"Immer only supports setting array indices and the 'length' property\",\n\t\t\t\tfunction(thing: string) {\n\t\t\t\t\treturn `'original' expects a draft, got: ${thing}`\n\t\t\t\t}\n\t\t\t\t// Note: if more errors are added, the errorOffset in Patches.ts should be increased\n\t\t\t\t// See Patches.ts for additional errors\n\t\t  ]\n\t\t: []\n\nexport function die(error: number, ...args: any[]): never {\n\tif (process.env.NODE_ENV !== \"production\") {\n\t\tconst e = errors[error]\n\t\tconst msg = typeof e === \"function\" ? e.apply(null, args as any) : e\n\t\tthrow new Error(`[Immer] ${msg}`)\n\t}\n\tthrow new Error(\n\t\t`[Immer] minified error nr: ${error}. Full error at: https://bit.ly/3cXEKWf`\n\t)\n}\n", "import {\n\tDRAFT_STATE,\n\tDRAF<PERSON><PERSON><PERSON>,\n\tObjectish,\n\tDrafted,\n\tAnyObject,\n\tAnyMap,\n\tAnySet,\n\tImmerState,\n\tArchType,\n\tdie,\n\tStrictMode\n} from \"../internal\"\n\nexport const getPrototypeOf = Object.getPrototypeOf\n\n/** Returns true if the given value is an Immer draft */\n/*#__PURE__*/\nexport function isDraft(value: any): boolean {\n\treturn !!value && !!value[DRAFT_STATE]\n}\n\n/** Returns true if the given value can be drafted by Immer */\n/*#__PURE__*/\nexport function isDraftable(value: any): boolean {\n\tif (!value) return false\n\treturn (\n\t\tisPlainObject(value) ||\n\t\tArray.isArray(value) ||\n\t\t!!value[DRAFTABLE] ||\n\t\t!!value.constructor?.[DRAFTABLE] ||\n\t\tisMap(value) ||\n\t\tisSet(value)\n\t)\n}\n\nconst objectCtorString = Object.prototype.constructor.toString()\n/*#__PURE__*/\nexport function isPlainObject(value: any): boolean {\n\tif (!value || typeof value !== \"object\") return false\n\tconst proto = getPrototypeOf(value)\n\tif (proto === null) {\n\t\treturn true\n\t}\n\tconst Ctor =\n\t\tObject.hasOwnProperty.call(proto, \"constructor\") && proto.constructor\n\n\tif (Ctor === Object) return true\n\n\treturn (\n\t\ttypeof Ctor == \"function\" &&\n\t\tFunction.toString.call(Ctor) === objectCtorString\n\t)\n}\n\n/** Get the underlying object that is represented by the given draft */\n/*#__PURE__*/\nexport function original<T>(value: T): T | undefined\nexport function original(value: Drafted<any>): any {\n\tif (!isDraft(value)) die(15, value)\n\treturn value[DRAFT_STATE].base_\n}\n\n/**\n * Each iterates a map, set or array.\n * Or, if any other kind of object, all of its own properties.\n * Regardless whether they are enumerable or symbols\n */\nexport function each<T extends Objectish>(\n\tobj: T,\n\titer: (key: string | number, value: any, source: T) => void\n): void\nexport function each(obj: any, iter: any) {\n\tif (getArchtype(obj) === ArchType.Object) {\n\t\tReflect.ownKeys(obj).forEach(key => {\n\t\t\titer(key, obj[key], obj)\n\t\t})\n\t} else {\n\t\tobj.forEach((entry: any, index: any) => iter(index, entry, obj))\n\t}\n}\n\n/*#__PURE__*/\nexport function getArchtype(thing: any): ArchType {\n\tconst state: undefined | ImmerState = thing[DRAFT_STATE]\n\treturn state\n\t\t? state.type_\n\t\t: Array.isArray(thing)\n\t\t? ArchType.Array\n\t\t: isMap(thing)\n\t\t? ArchType.Map\n\t\t: isSet(thing)\n\t\t? ArchType.Set\n\t\t: ArchType.Object\n}\n\n/*#__PURE__*/\nexport function has(thing: any, prop: PropertyKey): boolean {\n\treturn getArchtype(thing) === ArchType.Map\n\t\t? thing.has(prop)\n\t\t: Object.prototype.hasOwnProperty.call(thing, prop)\n}\n\n/*#__PURE__*/\nexport function get(thing: AnyMap | AnyObject, prop: PropertyKey): any {\n\t// @ts-ignore\n\treturn getArchtype(thing) === ArchType.Map ? thing.get(prop) : thing[prop]\n}\n\n/*#__PURE__*/\nexport function set(thing: any, propOrOldValue: PropertyKey, value: any) {\n\tconst t = getArchtype(thing)\n\tif (t === ArchType.Map) thing.set(propOrOldValue, value)\n\telse if (t === ArchType.Set) {\n\t\tthing.add(value)\n\t} else thing[propOrOldValue] = value\n}\n\n/*#__PURE__*/\nexport function is(x: any, y: any): boolean {\n\t// From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n\tif (x === y) {\n\t\treturn x !== 0 || 1 / x === 1 / y\n\t} else {\n\t\treturn x !== x && y !== y\n\t}\n}\n\n/*#__PURE__*/\nexport function isMap(target: any): target is AnyMap {\n\treturn target instanceof Map\n}\n\n/*#__PURE__*/\nexport function isSet(target: any): target is AnySet {\n\treturn target instanceof Set\n}\n/*#__PURE__*/\nexport function latest(state: ImmerState): any {\n\treturn state.copy_ || state.base_\n}\n\n/*#__PURE__*/\nexport function shallowCopy(base: any, strict: StrictMode) {\n\tif (isMap(base)) {\n\t\treturn new Map(base)\n\t}\n\tif (isSet(base)) {\n\t\treturn new Set(base)\n\t}\n\tif (Array.isArray(base)) return Array.prototype.slice.call(base)\n\n\tconst isPlain = isPlainObject(base)\n\n\tif (strict === true || (strict === \"class_only\" && !isPlain)) {\n\t\t// Perform a strict copy\n\t\tconst descriptors = Object.getOwnPropertyDescriptors(base)\n\t\tdelete descriptors[DRAFT_STATE as any]\n\t\tlet keys = Reflect.ownKeys(descriptors)\n\t\tfor (let i = 0; i < keys.length; i++) {\n\t\t\tconst key: any = keys[i]\n\t\t\tconst desc = descriptors[key]\n\t\t\tif (desc.writable === false) {\n\t\t\t\tdesc.writable = true\n\t\t\t\tdesc.configurable = true\n\t\t\t}\n\t\t\t// like object.assign, we will read any _own_, get/set accessors. This helps in dealing\n\t\t\t// with libraries that trap values, like mobx or vue\n\t\t\t// unlike object.assign, non-enumerables will be copied as well\n\t\t\tif (desc.get || desc.set)\n\t\t\t\tdescriptors[key] = {\n\t\t\t\t\tconfigurable: true,\n\t\t\t\t\twritable: true, // could live with !!desc.set as well here...\n\t\t\t\t\tenumerable: desc.enumerable,\n\t\t\t\t\tvalue: base[key]\n\t\t\t\t}\n\t\t}\n\t\treturn Object.create(getPrototypeOf(base), descriptors)\n\t} else {\n\t\t// perform a sloppy copy\n\t\tconst proto = getPrototypeOf(base)\n\t\tif (proto !== null && isPlain) {\n\t\t\treturn {...base} // assumption: better inner class optimization than the assign below\n\t\t}\n\t\tconst obj = Object.create(proto)\n\t\treturn Object.assign(obj, base)\n\t}\n}\n\n/**\n * Freezes draftable objects. Returns the original object.\n * By default freezes shallowly, but if the second argument is `true` it will freeze recursively.\n *\n * @param obj\n * @param deep\n */\nexport function freeze<T>(obj: T, deep?: boolean): T\nexport function freeze<T>(obj: any, deep: boolean = false): T {\n\tif (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj\n\tif (getArchtype(obj) > 1 /* Map or Set */) {\n\t\tobj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections as any\n\t}\n\tObject.freeze(obj)\n\tif (deep)\n\t\t// See #590, don't recurse into non-enumerable / Symbol properties when freezing\n\t\t// So use Object.entries (only string-like, enumerables) instead of each()\n\t\tObject.entries(obj).forEach(([key, value]) => freeze(value, true))\n\treturn obj\n}\n\nfunction dontMutateFrozenCollections() {\n\tdie(2)\n}\n\nexport function isFrozen(obj: any): boolean {\n\treturn Object.isFrozen(obj)\n}\n", "import {\n\tImmerState,\n\t<PERSON>,\n\tDrafted,\n\tImmerB<PERSON>State,\n\tAnyMap,\n\tAnySet,\n\tArchType,\n\tdie\n} from \"../internal\"\n\n/** Plugin utilities */\nconst plugins: {\n\tPatches?: {\n\t\tgeneratePatches_(\n\t\t\tstate: ImmerState,\n\t\t\tbasePath: PatchPath,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tgenerateReplacementPatches_(\n\t\t\tbase: any,\n\t\t\treplacement: any,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tapplyPatches_<T>(draft: T, patches: readonly Patch[]): T\n\t}\n\tMapSet?: {\n\t\tproxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T\n\t\tproxySet_<T extends AnySet>(target: T, parent?: ImmerState): T\n\t}\n} = {}\n\ntype Plugins = typeof plugins\n\nexport function getPlugin<K extends keyof Plugins>(\n\tpluginKey: K\n): Exclude<Plugins[K], undefined> {\n\tconst plugin = plugins[pluginKey]\n\tif (!plugin) {\n\t\tdie(0, pluginKey)\n\t}\n\t// @ts-ignore\n\treturn plugin\n}\n\nexport function loadPlugin<K extends keyof Plugins>(\n\tpluginKey: K,\n\timplementation: Plugins[K]\n): void {\n\tif (!plugins[pluginKey]) plugins[pluginKey] = implementation\n}\n/** Map / Set plugin */\n\nexport interface MapState extends ImmerBaseState {\n\ttype_: ArchType.Map\n\tcopy_: AnyMap | undefined\n\tassigned_: Map<any, boolean> | undefined\n\tbase_: AnyMap\n\trevoked_: boolean\n\tdraft_: Drafted<AnyMap, MapState>\n}\n\nexport interface SetState extends ImmerBaseState {\n\ttype_: ArchType.Set\n\tcopy_: AnySet | undefined\n\tbase_: AnySet\n\tdrafts_: Map<any, Drafted> // maps the original value to the draft value in the new set\n\trevoked_: boolean\n\tdraft_: Drafted<AnySet, SetState>\n}\n\n/** Patches plugin */\n\nexport type PatchPath = (string | number)[]\n", "import {\n\t<PERSON>,\n\t<PERSON>L<PERSON>ener,\n\tDrafted,\n\tImmer,\n\tDRAFT_STATE,\n\tImmerState,\n\tArchType,\n\tgetPlugin\n} from \"../internal\"\n\n/** Each scope represents a `produce` call. */\n\nexport interface ImmerScope {\n\tpatches_?: Patch[]\n\tinversePatches_?: Patch[]\n\tcanAutoFreeze_: boolean\n\tdrafts_: any[]\n\tparent_?: ImmerScope\n\tpatchListener_?: PatchListener\n\timmer_: Immer\n\tunfinalizedDrafts_: number\n}\n\nlet currentScope: ImmerScope | undefined\n\nexport function getCurrentScope() {\n\treturn currentScope!\n}\n\nfunction createScope(\n\tparent_: ImmerScope | undefined,\n\timmer_: Immer\n): ImmerScope {\n\treturn {\n\t\tdrafts_: [],\n\t\tparent_,\n\t\timmer_,\n\t\t// Whenever the modified draft contains a draft from another scope, we\n\t\t// need to prevent auto-freezing so the unowned draft can be finalized.\n\t\tcanAutoFreeze_: true,\n\t\tunfinalizedDrafts_: 0\n\t}\n}\n\nexport function usePatchesInScope(\n\tscope: ImmerScope,\n\tpatchListener?: PatchListener\n) {\n\tif (patchListener) {\n\t\tgetPlugin(\"Patches\") // assert we have the plugin\n\t\tscope.patches_ = []\n\t\tscope.inversePatches_ = []\n\t\tscope.patchListener_ = patchListener\n\t}\n}\n\nexport function revokeScope(scope: ImmerScope) {\n\tleaveScope(scope)\n\tscope.drafts_.forEach(revokeDraft)\n\t// @ts-ignore\n\tscope.drafts_ = null\n}\n\nexport function leaveScope(scope: ImmerScope) {\n\tif (scope === currentScope) {\n\t\tcurrentScope = scope.parent_\n\t}\n}\n\nexport function enterScope(immer: Immer) {\n\treturn (currentScope = createScope(currentScope, immer))\n}\n\nfunction revokeDraft(draft: Drafted) {\n\tconst state: ImmerState = draft[DRAFT_STATE]\n\tif (state.type_ === ArchType.Object || state.type_ === ArchType.Array)\n\t\tstate.revoke_()\n\telse state.revoked_ = true\n}\n", "import {\n\tI<PERSON>Scope,\n\tDRAFT_STATE,\n\tisDraftable,\n\tNOTHING,\n\tPatchP<PERSON>,\n\teach,\n\thas,\n\tfreeze,\n\tImmerState,\n\tisDraft,\n\tSetState,\n\tset,\n\tArchType,\n\tgetPlugin,\n\tdie,\n\trevokeScope,\n\tisFrozen\n} from \"../internal\"\n\nexport function processResult(result: any, scope: ImmerScope) {\n\tscope.unfinalizedDrafts_ = scope.drafts_.length\n\tconst baseDraft = scope.drafts_![0]\n\tconst isReplaced = result !== undefined && result !== baseDraft\n\tif (isReplaced) {\n\t\tif (baseDraft[DRAFT_STATE].modified_) {\n\t\t\trevokeScope(scope)\n\t\t\tdie(4)\n\t\t}\n\t\tif (isDraftable(result)) {\n\t\t\t// Finalize the result in case it contains (or is) a subset of the draft.\n\t\t\tresult = finalize(scope, result)\n\t\t\tif (!scope.parent_) maybeFreeze(scope, result)\n\t\t}\n\t\tif (scope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(\n\t\t\t\tbaseDraft[DRAFT_STATE].base_,\n\t\t\t\tresult,\n\t\t\t\tscope.patches_,\n\t\t\t\tscope.inversePatches_!\n\t\t\t)\n\t\t}\n\t} else {\n\t\t// Finalize the base draft.\n\t\tresult = finalize(scope, baseDraft, [])\n\t}\n\trevokeScope(scope)\n\tif (scope.patches_) {\n\t\tscope.patchListener_!(scope.patches_, scope.inversePatches_!)\n\t}\n\treturn result !== NOTHING ? result : undefined\n}\n\nfunction finalize(rootScope: ImmerScope, value: any, path?: PatchPath) {\n\t// Don't recurse in tho recursive data structures\n\tif (isFrozen(value)) return value\n\n\tconst state: ImmerState = value[DRAFT_STATE]\n\t// A plain object, might need freezing, might contain drafts\n\tif (!state) {\n\t\teach(value, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, value, key, childValue, path)\n\t\t)\n\t\treturn value\n\t}\n\t// Never finalize drafts owned by another scope.\n\tif (state.scope_ !== rootScope) return value\n\t// Unmodified draft, return the (frozen) original\n\tif (!state.modified_) {\n\t\tmaybeFreeze(rootScope, state.base_, true)\n\t\treturn state.base_\n\t}\n\t// Not finalized yet, let's do that now\n\tif (!state.finalized_) {\n\t\tstate.finalized_ = true\n\t\tstate.scope_.unfinalizedDrafts_--\n\t\tconst result = state.copy_\n\t\t// Finalize all children of the copy\n\t\t// For sets we clone before iterating, otherwise we can get in endless loop due to modifying during iteration, see #628\n\t\t// To preserve insertion order in all cases we then clear the set\n\t\t// And we let finalizeProperty know it needs to re-add non-draft children back to the target\n\t\tlet resultEach = result\n\t\tlet isSet = false\n\t\tif (state.type_ === ArchType.Set) {\n\t\t\tresultEach = new Set(result)\n\t\t\tresult.clear()\n\t\t\tisSet = true\n\t\t}\n\t\teach(resultEach, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, result, key, childValue, path, isSet)\n\t\t)\n\t\t// everything inside is frozen, we can freeze here\n\t\tmaybeFreeze(rootScope, result, false)\n\t\t// first time finalizing, let's create those patches\n\t\tif (path && rootScope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generatePatches_(\n\t\t\t\tstate,\n\t\t\t\tpath,\n\t\t\t\trootScope.patches_,\n\t\t\t\trootScope.inversePatches_!\n\t\t\t)\n\t\t}\n\t}\n\treturn state.copy_\n}\n\nfunction finalizeProperty(\n\trootScope: ImmerScope,\n\tparentState: undefined | ImmerState,\n\ttargetObject: any,\n\tprop: string | number,\n\tchildValue: any,\n\trootPath?: PatchPath,\n\ttargetIsSet?: boolean\n) {\n\tif (process.env.NODE_ENV !== \"production\" && childValue === targetObject)\n\t\tdie(5)\n\tif (isDraft(childValue)) {\n\t\tconst path =\n\t\t\trootPath &&\n\t\t\tparentState &&\n\t\t\tparentState!.type_ !== ArchType.Set && // Set objects are atomic since they have no keys.\n\t\t\t!has((parentState as Exclude<ImmerState, SetState>).assigned_!, prop) // Skip deep patches for assigned keys.\n\t\t\t\t? rootPath!.concat(prop)\n\t\t\t\t: undefined\n\t\t// Drafts owned by `scope` are finalized here.\n\t\tconst res = finalize(rootScope, childValue, path)\n\t\tset(targetObject, prop, res)\n\t\t// Drafts from another scope must prevented to be frozen\n\t\t// if we got a draft back from finalize, we're in a nested produce and shouldn't freeze\n\t\tif (isDraft(res)) {\n\t\t\trootScope.canAutoFreeze_ = false\n\t\t} else return\n\t} else if (targetIsSet) {\n\t\ttargetObject.add(childValue)\n\t}\n\t// Search new objects for unfinalized drafts. Frozen objects should never contain drafts.\n\tif (isDraftable(childValue) && !isFrozen(childValue)) {\n\t\tif (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n\t\t\t// optimization: if an object is not a draft, and we don't have to\n\t\t\t// deepfreeze everything, and we are sure that no drafts are left in the remaining object\n\t\t\t// cause we saw and finalized all drafts already; we can stop visiting the rest of the tree.\n\t\t\t// This benefits especially adding large data tree's without further processing.\n\t\t\t// See add-data.js perf test\n\t\t\treturn\n\t\t}\n\t\tfinalize(rootScope, childValue)\n\t\t// Immer deep freezes plain objects, so if there is no parent state, we freeze as well\n\t\t// Per #590, we never freeze symbolic properties. Just to make sure don't accidentally interfere\n\t\t// with other frameworks.\n\t\tif (\n\t\t\t(!parentState || !parentState.scope_.parent_) &&\n\t\t\ttypeof prop !== \"symbol\" &&\n\t\t\tObject.prototype.propertyIsEnumerable.call(targetObject, prop)\n\t\t)\n\t\t\tmaybeFreeze(rootScope, childValue)\n\t}\n}\n\nfunction maybeFreeze(scope: ImmerScope, value: any, deep = false) {\n\t// we never freeze for a non-root scope; as it would prevent pruning for drafts inside wrapping objects\n\tif (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n\t\tfreeze(value, deep)\n\t}\n}\n", "import {\n\teach,\n\thas,\n\tis,\n\tisDraftable,\n\tshallowCopy,\n\tlatest,\n\tImmerBaseState,\n\tImmerState,\n\tDrafted,\n\tAnyObject,\n\tAnyArray,\n\tObjectish,\n\tgetCurrentScope,\n\tgetPrototypeOf,\n\tDRAFT_STATE,\n\tdie,\n\tcreateProxy,\n\tArchType,\n\tImmerScope\n} from \"../internal\"\n\ninterface ProxyBaseState extends ImmerBaseState {\n\tassigned_: {\n\t\t[property: string]: boolean\n\t}\n\tparent_?: ImmerState\n\trevoke_(): void\n}\n\nexport interface ProxyObjectState extends ProxyBaseState {\n\ttype_: ArchType.Object\n\tbase_: any\n\tcopy_: any\n\tdraft_: Drafted<AnyObject, ProxyObjectState>\n}\n\nexport interface ProxyArrayState extends ProxyBaseState {\n\ttype_: ArchType.Array\n\tbase_: AnyArray\n\tcopy_: AnyArray | null\n\tdraft_: Drafted<AnyArray, ProxyArrayState>\n}\n\ntype ProxyState = ProxyObjectState | ProxyArrayState\n\n/**\n * Returns a new draft of the `base` object.\n *\n * The second argument is the parent draft-state (used internally).\n */\nexport function createProxyProxy<T extends Objectish>(\n\tbase: T,\n\tparent?: ImmerState\n): Drafted<T, ProxyState> {\n\tconst isArray = Array.isArray(base)\n\tconst state: ProxyState = {\n\t\ttype_: isArray ? ArchType.Array : (ArchType.Object as any),\n\t\t// Track which produce call this is associated with.\n\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t// True for both shallow and deep changes.\n\t\tmodified_: false,\n\t\t// Used during finalization.\n\t\tfinalized_: false,\n\t\t// Track which properties have been assigned (true) or deleted (false).\n\t\tassigned_: {},\n\t\t// The parent draft state.\n\t\tparent_: parent,\n\t\t// The base state.\n\t\tbase_: base,\n\t\t// The base proxy.\n\t\tdraft_: null as any, // set below\n\t\t// The base copy with any updated values.\n\t\tcopy_: null,\n\t\t// Called by the `produce` function.\n\t\trevoke_: null as any,\n\t\tisManual_: false\n\t}\n\n\t// the traps must target something, a bit like the 'real' base.\n\t// but also, we need to be able to determine from the target what the relevant state is\n\t// (to avoid creating traps per instance to capture the state in closure,\n\t// and to avoid creating weird hidden properties as well)\n\t// So the trick is to use 'state' as the actual 'target'! (and make sure we intercept everything)\n\t// Note that in the case of an array, we put the state in an array to have better Reflect defaults ootb\n\tlet target: T = state as any\n\tlet traps: ProxyHandler<object | Array<any>> = objectTraps\n\tif (isArray) {\n\t\ttarget = [state] as any\n\t\ttraps = arrayTraps\n\t}\n\n\tconst {revoke, proxy} = Proxy.revocable(target, traps)\n\tstate.draft_ = proxy as any\n\tstate.revoke_ = revoke\n\treturn proxy as any\n}\n\n/**\n * Object drafts\n */\nexport const objectTraps: ProxyHandler<ProxyState> = {\n\tget(state, prop) {\n\t\tif (prop === DRAFT_STATE) return state\n\n\t\tconst source = latest(state)\n\t\tif (!has(source, prop)) {\n\t\t\t// non-existing or non-own property...\n\t\t\treturn readPropFromProto(state, source, prop)\n\t\t}\n\t\tconst value = source[prop]\n\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\treturn value\n\t\t}\n\t\t// Check for existing draft in modified state.\n\t\t// Assigned values are never drafted. This catches any drafts we created, too.\n\t\tif (value === peek(state.base_, prop)) {\n\t\t\tprepareCopy(state)\n\t\t\treturn (state.copy_![prop as any] = createProxy(value, state))\n\t\t}\n\t\treturn value\n\t},\n\thas(state, prop) {\n\t\treturn prop in latest(state)\n\t},\n\townKeys(state) {\n\t\treturn Reflect.ownKeys(latest(state))\n\t},\n\tset(\n\t\tstate: ProxyObjectState,\n\t\tprop: string /* strictly not, but helps TS */,\n\t\tvalue\n\t) {\n\t\tconst desc = getDescriptorFromProto(latest(state), prop)\n\t\tif (desc?.set) {\n\t\t\t// special case: if this write is captured by a setter, we have\n\t\t\t// to trigger it with the correct context\n\t\t\tdesc.set.call(state.draft_, value)\n\t\t\treturn true\n\t\t}\n\t\tif (!state.modified_) {\n\t\t\t// the last check is because we need to be able to distinguish setting a non-existing to undefined (which is a change)\n\t\t\t// from setting an existing property with value undefined to undefined (which is not a change)\n\t\t\tconst current = peek(latest(state), prop)\n\t\t\t// special case, if we assigning the original value to a draft, we can ignore the assignment\n\t\t\tconst currentState: ProxyObjectState = current?.[DRAFT_STATE]\n\t\t\tif (currentState && currentState.base_ === value) {\n\t\t\t\tstate.copy_![prop] = value\n\t\t\t\tstate.assigned_[prop] = false\n\t\t\t\treturn true\n\t\t\t}\n\t\t\tif (is(value, current) && (value !== undefined || has(state.base_, prop)))\n\t\t\t\treturn true\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t}\n\n\t\tif (\n\t\t\t(state.copy_![prop] === value &&\n\t\t\t\t// special case: handle new props with value 'undefined'\n\t\t\t\t(value !== undefined || prop in state.copy_)) ||\n\t\t\t// special case: NaN\n\t\t\t(Number.isNaN(value) && Number.isNaN(state.copy_![prop]))\n\t\t)\n\t\t\treturn true\n\n\t\t// @ts-ignore\n\t\tstate.copy_![prop] = value\n\t\tstate.assigned_[prop] = true\n\t\treturn true\n\t},\n\tdeleteProperty(state, prop: string) {\n\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\tif (peek(state.base_, prop) !== undefined || prop in state.base_) {\n\t\t\tstate.assigned_[prop] = false\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t} else {\n\t\t\t// if an originally not assigned property was deleted\n\t\t\tdelete state.assigned_[prop]\n\t\t}\n\t\tif (state.copy_) {\n\t\t\tdelete state.copy_[prop]\n\t\t}\n\t\treturn true\n\t},\n\t// Note: We never coerce `desc.value` into an Immer draft, because we can't make\n\t// the same guarantee in ES5 mode.\n\tgetOwnPropertyDescriptor(state, prop) {\n\t\tconst owner = latest(state)\n\t\tconst desc = Reflect.getOwnPropertyDescriptor(owner, prop)\n\t\tif (!desc) return desc\n\t\treturn {\n\t\t\twritable: true,\n\t\t\tconfigurable: state.type_ !== ArchType.Array || prop !== \"length\",\n\t\t\tenumerable: desc.enumerable,\n\t\t\tvalue: owner[prop]\n\t\t}\n\t},\n\tdefineProperty() {\n\t\tdie(11)\n\t},\n\tgetPrototypeOf(state) {\n\t\treturn getPrototypeOf(state.base_)\n\t},\n\tsetPrototypeOf() {\n\t\tdie(12)\n\t}\n}\n\n/**\n * Array drafts\n */\n\nconst arrayTraps: ProxyHandler<[ProxyArrayState]> = {}\neach(objectTraps, (key, fn) => {\n\t// @ts-ignore\n\tarrayTraps[key] = function() {\n\t\targuments[0] = arguments[0][0]\n\t\treturn fn.apply(this, arguments)\n\t}\n})\narrayTraps.deleteProperty = function(state, prop) {\n\tif (process.env.NODE_ENV !== \"production\" && isNaN(parseInt(prop as any)))\n\t\tdie(13)\n\t// @ts-ignore\n\treturn arrayTraps.set!.call(this, state, prop, undefined)\n}\narrayTraps.set = function(state, prop, value) {\n\tif (\n\t\tprocess.env.NODE_ENV !== \"production\" &&\n\t\tprop !== \"length\" &&\n\t\tisNaN(parseInt(prop as any))\n\t)\n\t\tdie(14)\n\treturn objectTraps.set!.call(this, state[0], prop, value, state[0])\n}\n\n// Access a property without creating an Immer draft.\nfunction peek(draft: Drafted, prop: PropertyKey) {\n\tconst state = draft[DRAFT_STATE]\n\tconst source = state ? latest(state) : draft\n\treturn source[prop]\n}\n\nfunction readPropFromProto(state: ImmerState, source: any, prop: PropertyKey) {\n\tconst desc = getDescriptorFromProto(source, prop)\n\treturn desc\n\t\t? `value` in desc\n\t\t\t? desc.value\n\t\t\t: // This is a very special case, if the prop is a getter defined by the\n\t\t\t  // prototype, we should invoke it with the draft as context!\n\t\t\t  desc.get?.call(state.draft_)\n\t\t: undefined\n}\n\nfunction getDescriptorFromProto(\n\tsource: any,\n\tprop: PropertyKey\n): PropertyDescriptor | undefined {\n\t// 'in' checks proto!\n\tif (!(prop in source)) return undefined\n\tlet proto = getPrototypeOf(source)\n\twhile (proto) {\n\t\tconst desc = Object.getOwnPropertyDescriptor(proto, prop)\n\t\tif (desc) return desc\n\t\tproto = getPrototypeOf(proto)\n\t}\n\treturn undefined\n}\n\nexport function markChanged(state: ImmerState) {\n\tif (!state.modified_) {\n\t\tstate.modified_ = true\n\t\tif (state.parent_) {\n\t\t\tmarkChanged(state.parent_)\n\t\t}\n\t}\n}\n\nexport function prepareCopy(state: {\n\tbase_: any\n\tcopy_: any\n\tscope_: ImmerScope\n}) {\n\tif (!state.copy_) {\n\t\tstate.copy_ = shallowCopy(\n\t\t\tstate.base_,\n\t\t\tstate.scope_.immer_.useStrictShallowCopy_\n\t\t)\n\t}\n}\n", "import {\n\tIProduceWithPatches,\n\tIProduce,\n\tImmerState,\n\tDrafted,\n\tisDraftable,\n\tprocessResult,\n\tPatch,\n\tObjectish,\n\tDRAFT_STATE,\n\tDraft,\n\tPatchListener,\n\tisDraft,\n\tisMap,\n\tisSet,\n\tcreateProxyProxy,\n\tgetPlugin,\n\tdie,\n\tenterScope,\n\trevokeScope,\n\tleaveScope,\n\tusePatchesInScope,\n\tgetCurrentScope,\n\tNOTHING,\n\tfreeze,\n\tcurrent\n} from \"../internal\"\n\ninterface ProducersFns {\n\tproduce: IProduce\n\tproduceWithPatches: IProduceWithPatches\n}\n\nexport type StrictMode = boolean | \"class_only\";\n\nexport class Immer implements ProducersFns {\n\tautoFreeze_: boolean = true\n\tuseStrictShallowCopy_: StrictMode = false\n\n\tconstructor(config?: {\n\t\tautoFreeze?: boolean\n\t\tuseStrictShallowCopy?: StrictMode\n\t}) {\n\t\tif (typeof config?.autoFreeze === \"boolean\")\n\t\t\tthis.setAutoFreeze(config!.autoFreeze)\n\t\tif (typeof config?.useStrictShallowCopy === \"boolean\")\n\t\t\tthis.setUseStrictShallowCopy(config!.useStrictShallowCopy)\n\t}\n\n\t/**\n\t * The `produce` function takes a value and a \"recipe function\" (whose\n\t * return value often depends on the base state). The recipe function is\n\t * free to mutate its first argument however it wants. All mutations are\n\t * only ever applied to a __copy__ of the base state.\n\t *\n\t * Pass only a function to create a \"curried producer\" which relieves you\n\t * from passing the recipe function every time.\n\t *\n\t * Only plain objects and arrays are made mutable. All other objects are\n\t * considered uncopyable.\n\t *\n\t * Note: This function is __bound__ to its `Immer` instance.\n\t *\n\t * @param {any} base - the initial state\n\t * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n\t * @param {Function} patchListener - optional function that will be called with all the patches produced here\n\t * @returns {any} a new state, or the initial state if nothing was modified\n\t */\n\tproduce: IProduce = (base: any, recipe?: any, patchListener?: any) => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\" && typeof recipe !== \"function\") {\n\t\t\tconst defaultBase = recipe\n\t\t\trecipe = base\n\n\t\t\tconst self = this\n\t\t\treturn function curriedProduce(\n\t\t\t\tthis: any,\n\t\t\t\tbase = defaultBase,\n\t\t\t\t...args: any[]\n\t\t\t) {\n\t\t\t\treturn self.produce(base, (draft: Drafted) => recipe.call(this, draft, ...args)) // prettier-ignore\n\t\t\t}\n\t\t}\n\n\t\tif (typeof recipe !== \"function\") die(6)\n\t\tif (patchListener !== undefined && typeof patchListener !== \"function\")\n\t\t\tdie(7)\n\n\t\tlet result\n\n\t\t// Only plain objects, arrays, and \"immerable classes\" are drafted.\n\t\tif (isDraftable(base)) {\n\t\t\tconst scope = enterScope(this)\n\t\t\tconst proxy = createProxy(base, undefined)\n\t\t\tlet hasError = true\n\t\t\ttry {\n\t\t\t\tresult = recipe(proxy)\n\t\t\t\thasError = false\n\t\t\t} finally {\n\t\t\t\t// finally instead of catch + rethrow better preserves original stack\n\t\t\t\tif (hasError) revokeScope(scope)\n\t\t\t\telse leaveScope(scope)\n\t\t\t}\n\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\treturn processResult(result, scope)\n\t\t} else if (!base || typeof base !== \"object\") {\n\t\t\tresult = recipe(base)\n\t\t\tif (result === undefined) result = base\n\t\t\tif (result === NOTHING) result = undefined\n\t\t\tif (this.autoFreeze_) freeze(result, true)\n\t\t\tif (patchListener) {\n\t\t\t\tconst p: Patch[] = []\n\t\t\t\tconst ip: Patch[] = []\n\t\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip)\n\t\t\t\tpatchListener(p, ip)\n\t\t\t}\n\t\t\treturn result\n\t\t} else die(1, base)\n\t}\n\n\tproduceWithPatches: IProduceWithPatches = (base: any, recipe?: any): any => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\") {\n\t\t\treturn (state: any, ...args: any[]) =>\n\t\t\t\tthis.produceWithPatches(state, (draft: any) => base(draft, ...args))\n\t\t}\n\n\t\tlet patches: Patch[], inversePatches: Patch[]\n\t\tconst result = this.produce(base, recipe, (p: Patch[], ip: Patch[]) => {\n\t\t\tpatches = p\n\t\t\tinversePatches = ip\n\t\t})\n\t\treturn [result, patches!, inversePatches!]\n\t}\n\n\tcreateDraft<T extends Objectish>(base: T): Draft<T> {\n\t\tif (!isDraftable(base)) die(8)\n\t\tif (isDraft(base)) base = current(base)\n\t\tconst scope = enterScope(this)\n\t\tconst proxy = createProxy(base, undefined)\n\t\tproxy[DRAFT_STATE].isManual_ = true\n\t\tleaveScope(scope)\n\t\treturn proxy as any\n\t}\n\n\tfinishDraft<D extends Draft<any>>(\n\t\tdraft: D,\n\t\tpatchListener?: PatchListener\n\t): D extends Draft<infer T> ? T : never {\n\t\tconst state: ImmerState = draft && (draft as any)[DRAFT_STATE]\n\t\tif (!state || !state.isManual_) die(9)\n\t\tconst {scope_: scope} = state\n\t\tusePatchesInScope(scope, patchListener)\n\t\treturn processResult(undefined, scope)\n\t}\n\n\t/**\n\t * Pass true to automatically freeze all copies created by Immer.\n\t *\n\t * By default, auto-freezing is enabled.\n\t */\n\tsetAutoFreeze(value: boolean) {\n\t\tthis.autoFreeze_ = value\n\t}\n\n\t/**\n\t * Pass true to enable strict shallow copy.\n\t *\n\t * By default, immer does not copy the object descriptors such as getter, setter and non-enumrable properties.\n\t */\n\tsetUseStrictShallowCopy(value: StrictMode) {\n\t\tthis.useStrictShallowCopy_ = value\n\t}\n\n\tapplyPatches<T extends Objectish>(base: T, patches: readonly Patch[]): T {\n\t\t// If a patch replaces the entire state, take that replacement as base\n\t\t// before applying patches\n\t\tlet i: number\n\t\tfor (i = patches.length - 1; i >= 0; i--) {\n\t\t\tconst patch = patches[i]\n\t\t\tif (patch.path.length === 0 && patch.op === \"replace\") {\n\t\t\t\tbase = patch.value\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\t// If there was a patch that replaced the entire state, start from the\n\t\t// patch after that.\n\t\tif (i > -1) {\n\t\t\tpatches = patches.slice(i + 1)\n\t\t}\n\n\t\tconst applyPatchesImpl = getPlugin(\"Patches\").applyPatches_\n\t\tif (isDraft(base)) {\n\t\t\t// N.B: never hits if some patch a replacement, patches are never drafts\n\t\t\treturn applyPatchesImpl(base, patches)\n\t\t}\n\t\t// Otherwise, produce a copy of the base state.\n\t\treturn this.produce(base, (draft: Drafted) =>\n\t\t\tapplyPatchesImpl(draft, patches)\n\t\t)\n\t}\n}\n\nexport function createProxy<T extends Objectish>(\n\tvalue: T,\n\tparent?: ImmerState\n): Drafted<T, ImmerState> {\n\t// precondition: createProxy should be guarded by isDraftable, so we know we can safely draft\n\tconst draft: Drafted = isMap(value)\n\t\t? getPlugin(\"MapSet\").proxyMap_(value, parent)\n\t\t: isSet(value)\n\t\t? getPlugin(\"MapSet\").proxySet_(value, parent)\n\t\t: createProxyProxy(value, parent)\n\n\tconst scope = parent ? parent.scope_ : getCurrentScope()\n\tscope.drafts_.push(draft)\n\treturn draft\n}\n", "import {\n\tdie,\n\tisDraft,\n\tshallowCopy,\n\teach,\n\tDRAFT_STATE,\n\tset,\n\tImmerState,\n\tisDraftable,\n\tisFrozen\n} from \"../internal\"\n\n/** Takes a snapshot of the current state of a draft and finalizes it (but without freezing). This is a great utility to print the current state during debugging (no Proxies in the way). The output of current can also be safely leaked outside the producer. */\nexport function current<T>(value: T): T\nexport function current(value: any): any {\n\tif (!isDraft(value)) die(10, value)\n\treturn currentImpl(value)\n}\n\nfunction currentImpl(value: any): any {\n\tif (!isDraftable(value) || isFrozen(value)) return value\n\tconst state: ImmerState | undefined = value[DRAFT_STATE]\n\tlet copy: any\n\tif (state) {\n\t\tif (!state.modified_) return state.base_\n\t\t// Optimization: avoid generating new drafts during copying\n\t\tstate.finalized_ = true\n\t\tcopy = shallowCopy(value, state.scope_.immer_.useStrictShallowCopy_)\n\t} else {\n\t\tcopy = shallowCopy(value, true)\n\t}\n\t// recurse\n\teach(copy, (key, childValue) => {\n\t\tset(copy, key, currentImpl(childValue))\n\t})\n\tif (state) {\n\t\tstate.finalized_ = false\n\t}\n\treturn copy\n}\n", "import {immerable} from \"../immer\"\nimport {\n\tImmerState,\n\tPatch,\n\tSetState,\n\tProxyArrayState,\n\tMapState,\n\tProxyObjectState,\n\tPatchPath,\n\tget,\n\teach,\n\thas,\n\tgetArchtype,\n\tgetPrototypeOf,\n\tisSet,\n\tisMap,\n\tloadPlugin,\n\tArchType,\n\tdie,\n\tisDraft,\n\tisDraftable,\n\tNOTHING,\n\terrors\n} from \"../internal\"\n\nexport function enablePatches() {\n\tconst errorOffset = 16\n\tif (process.env.NODE_ENV !== \"production\") {\n\t\terrors.push(\n\t\t\t'Sets cannot have \"replace\" patches.',\n\t\t\tfunction(op: string) {\n\t\t\t\treturn \"Unsupported patch operation: \" + op\n\t\t\t},\n\t\t\tfunction(path: string) {\n\t\t\t\treturn \"Cannot apply patch, path doesn't resolve: \" + path\n\t\t\t},\n\t\t\t\"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n\t\t)\n\t}\n\n\tconst REPLACE = \"replace\"\n\tconst ADD = \"add\"\n\tconst REMOVE = \"remove\"\n\n\tfunction generatePatches_(\n\t\tstate: ImmerState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tswitch (state.type_) {\n\t\t\tcase ArchType.Object:\n\t\t\tcase ArchType.Map:\n\t\t\t\treturn generatePatchesFromAssigned(\n\t\t\t\t\tstate,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t\tcase ArchType.Array:\n\t\t\t\treturn generateArrayPatches(state, basePath, patches, inversePatches)\n\t\t\tcase ArchType.Set:\n\t\t\t\treturn generateSetPatches(\n\t\t\t\t\t(state as any) as SetState,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t}\n\t}\n\n\tfunction generateArrayPatches(\n\t\tstate: ProxyArrayState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, assigned_} = state\n\t\tlet copy_ = state.copy_!\n\n\t\t// Reduce complexity by ensuring `base` is never longer.\n\t\tif (copy_.length < base_.length) {\n\t\t\t// @ts-ignore\n\t\t\t;[base_, copy_] = [copy_, base_]\n\t\t\t;[patches, inversePatches] = [inversePatches, patches]\n\t\t}\n\n\t\t// Process replaced indices.\n\t\tfor (let i = 0; i < base_.length; i++) {\n\t\t\tif (assigned_[i] && copy_[i] !== base_[i]) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t\t})\n\t\t\t\tinversePatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(base_[i])\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\t// Process added indices.\n\t\tfor (let i = base_.length; i < copy_.length; i++) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tpatches.push({\n\t\t\t\top: ADD,\n\t\t\t\tpath,\n\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t})\n\t\t}\n\t\tfor (let i = copy_.length - 1; base_.length <= i; --i) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tinversePatches.push({\n\t\t\t\top: REMOVE,\n\t\t\t\tpath\n\t\t\t})\n\t\t}\n\t}\n\n\t// This is used for both Map objects and normal objects.\n\tfunction generatePatchesFromAssigned(\n\t\tstate: MapState | ProxyObjectState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tconst {base_, copy_} = state\n\t\teach(state.assigned_!, (key, assignedValue) => {\n\t\t\tconst origValue = get(base_, key)\n\t\t\tconst value = get(copy_!, key)\n\t\t\tconst op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD\n\t\t\tif (origValue === value && op === REPLACE) return\n\t\t\tconst path = basePath.concat(key as any)\n\t\t\tpatches.push(op === REMOVE ? {op, path} : {op, path, value})\n\t\t\tinversePatches.push(\n\t\t\t\top === ADD\n\t\t\t\t\t? {op: REMOVE, path}\n\t\t\t\t\t: op === REMOVE\n\t\t\t\t\t? {op: ADD, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t\t\t: {op: REPLACE, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t)\n\t\t})\n\t}\n\n\tfunction generateSetPatches(\n\t\tstate: SetState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, copy_} = state\n\n\t\tlet i = 0\n\t\tbase_.forEach((value: any) => {\n\t\t\tif (!copy_!.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t\ti = 0\n\t\tcopy_!.forEach((value: any) => {\n\t\t\tif (!base_.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t}\n\n\tfunction generateReplacementPatches_(\n\t\tbaseValue: any,\n\t\treplacement: any,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tpatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: replacement === NOTHING ? undefined : replacement\n\t\t})\n\t\tinversePatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: baseValue\n\t\t})\n\t}\n\n\tfunction applyPatches_<T>(draft: T, patches: readonly Patch[]): T {\n\t\tpatches.forEach(patch => {\n\t\t\tconst {path, op} = patch\n\n\t\t\tlet base: any = draft\n\t\t\tfor (let i = 0; i < path.length - 1; i++) {\n\t\t\t\tconst parentType = getArchtype(base)\n\t\t\t\tlet p = path[i]\n\t\t\t\tif (typeof p !== \"string\" && typeof p !== \"number\") {\n\t\t\t\t\tp = \"\" + p\n\t\t\t\t}\n\n\t\t\t\t// See #738, avoid prototype pollution\n\t\t\t\tif (\n\t\t\t\t\t(parentType === ArchType.Object || parentType === ArchType.Array) &&\n\t\t\t\t\t(p === \"__proto__\" || p === \"constructor\")\n\t\t\t\t)\n\t\t\t\t\tdie(errorOffset + 3)\n\t\t\t\tif (typeof base === \"function\" && p === \"prototype\")\n\t\t\t\t\tdie(errorOffset + 3)\n\t\t\t\tbase = get(base, p)\n\t\t\t\tif (typeof base !== \"object\") die(errorOffset + 2, path.join(\"/\"))\n\t\t\t}\n\n\t\t\tconst type = getArchtype(base)\n\t\t\tconst value = deepClonePatchValue(patch.value) // used to clone patch to ensure original patch is not modified, see #411\n\t\t\tconst key = path[path.length - 1]\n\t\t\tswitch (op) {\n\t\t\t\tcase REPLACE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase ArchType.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\t\tcase ArchType.Set:\n\t\t\t\t\t\t\tdie(errorOffset)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t// if value is an object, then it's assigned by reference\n\t\t\t\t\t\t\t// in the following add or remove ops, the value field inside the patch will also be modifyed\n\t\t\t\t\t\t\t// so we use value from the cloned patch\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase ADD:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase ArchType.Array:\n\t\t\t\t\t\t\treturn key === \"-\"\n\t\t\t\t\t\t\t\t? base.push(value)\n\t\t\t\t\t\t\t\t: base.splice(key as any, 0, value)\n\t\t\t\t\t\tcase ArchType.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\tcase ArchType.Set:\n\t\t\t\t\t\t\treturn base.add(value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase REMOVE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase ArchType.Array:\n\t\t\t\t\t\t\treturn base.splice(key as any, 1)\n\t\t\t\t\t\tcase ArchType.Map:\n\t\t\t\t\t\t\treturn base.delete(key)\n\t\t\t\t\t\tcase ArchType.Set:\n\t\t\t\t\t\t\treturn base.delete(patch.value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn delete base[key]\n\t\t\t\t\t}\n\t\t\t\tdefault:\n\t\t\t\t\tdie(errorOffset + 1, op)\n\t\t\t}\n\t\t})\n\n\t\treturn draft\n\t}\n\n\t// optimize: this is quite a performance hit, can we detect intelligently when it is needed?\n\t// E.g. auto-draft when new objects from outside are assigned and modified?\n\t// (See failing test when deepClone just returns obj)\n\tfunction deepClonePatchValue<T>(obj: T): T\n\tfunction deepClonePatchValue(obj: any) {\n\t\tif (!isDraftable(obj)) return obj\n\t\tif (Array.isArray(obj)) return obj.map(deepClonePatchValue)\n\t\tif (isMap(obj))\n\t\t\treturn new Map(\n\t\t\t\tArray.from(obj.entries()).map(([k, v]) => [k, deepClonePatchValue(v)])\n\t\t\t)\n\t\tif (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue))\n\t\tconst cloned = Object.create(getPrototypeOf(obj))\n\t\tfor (const key in obj) cloned[key] = deepClonePatchValue(obj[key])\n\t\tif (has(obj, immerable)) cloned[immerable] = obj[immerable]\n\t\treturn cloned\n\t}\n\n\tfunction clonePatchValueIfNeeded<T>(obj: T): T {\n\t\tif (isDraft(obj)) {\n\t\t\treturn deepClonePatchValue(obj)\n\t\t} else return obj\n\t}\n\n\tloadPlugin(\"Patches\", {\n\t\tapplyPatches_,\n\t\tgeneratePatches_,\n\t\tgenerateReplacementPatches_\n\t})\n}\n", "// types only!\nimport {\n\tImmerState,\n\tAnyMap,\n\tAnySet,\n\tMapState,\n\tSetState,\n\tDRAFT_STATE,\n\tgetCurrentScope,\n\tlatest,\n\tisDraftable,\n\tcreateProxy,\n\tloadPlugin,\n\tmarkChanged,\n\tdie,\n\tArchType,\n\teach\n} from \"../internal\"\n\nexport function enableMapSet() {\n\tclass DraftMap extends Map {\n\t\t[DRAFT_STATE]: MapState\n\n\t\tconstructor(target: AnyMap, parent?: ImmerState) {\n\t\t\tsuper()\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ArchType.Map,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tassigned_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this as any,\n\t\t\t\tisManual_: false,\n\t\t\t\trevoked_: false\n\t\t\t}\n\t\t}\n\n\t\tget size(): number {\n\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t}\n\n\t\thas(key: any): boolean {\n\t\t\treturn latest(this[DRAFT_STATE]).has(key)\n\t\t}\n\n\t\tset(key: any, value: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!latest(state).has(key) || latest(state).get(key) !== value) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t\tstate.copy_!.set(key, value)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tdelete(key: any): boolean {\n\t\t\tif (!this.has(key)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareMapCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\tif (state.base_.has(key)) {\n\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t} else {\n\t\t\t\tstate.assigned_!.delete(key)\n\t\t\t}\n\t\t\tstate.copy_!.delete(key)\n\t\t\treturn true\n\t\t}\n\n\t\tclear() {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_ = new Map()\n\t\t\t\teach(state.base_, key => {\n\t\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t\t})\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tforEach(cb: (value: any, key: any, self: any) => void, thisArg?: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tlatest(state).forEach((_value: any, key: any, _map: any) => {\n\t\t\t\tcb.call(thisArg, this.get(key), key, this)\n\t\t\t})\n\t\t}\n\n\t\tget(key: any): any {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tconst value = latest(state).get(key)\n\t\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\t\treturn value\n\t\t\t}\n\t\t\tif (value !== state.base_.get(key)) {\n\t\t\t\treturn value // either already drafted or reassigned\n\t\t\t}\n\t\t\t// despite what it looks, this creates a draft only once, see above condition\n\t\t\tconst draft = createProxy(value, state)\n\t\t\tprepareMapCopy(state)\n\t\t\tstate.copy_!.set(key, draft)\n\t\t\treturn draft\n\t\t}\n\n\t\tkeys(): IterableIterator<any> {\n\t\t\treturn latest(this[DRAFT_STATE]).keys()\n\t\t}\n\n\t\tvalues(): IterableIterator<any> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[Symbol.iterator]: () => this.values(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tentries(): IterableIterator<[any, any]> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[Symbol.iterator]: () => this.entries(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue: [r.value, value]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\t[Symbol.iterator]() {\n\t\t\treturn this.entries()\n\t\t}\n\t}\n\n\tfunction proxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftMap(target, parent)\n\t}\n\n\tfunction prepareMapCopy(state: MapState) {\n\t\tif (!state.copy_) {\n\t\t\tstate.assigned_ = new Map()\n\t\t\tstate.copy_ = new Map(state.base_)\n\t\t}\n\t}\n\n\tclass DraftSet extends Set {\n\t\t[DRAFT_STATE]: SetState\n\t\tconstructor(target: AnySet, parent?: ImmerState) {\n\t\t\tsuper()\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ArchType.Set,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this,\n\t\t\t\tdrafts_: new Map(),\n\t\t\t\trevoked_: false,\n\t\t\t\tisManual_: false\n\t\t\t}\n\t\t}\n\n\t\tget size(): number {\n\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t}\n\n\t\thas(value: any): boolean {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\t// bit of trickery here, to be able to recognize both the value, and the draft of its value\n\t\t\tif (!state.copy_) {\n\t\t\t\treturn state.base_.has(value)\n\t\t\t}\n\t\t\tif (state.copy_.has(value)) return true\n\t\t\tif (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value)))\n\t\t\t\treturn true\n\t\t\treturn false\n\t\t}\n\n\t\tadd(value: any): any {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!this.has(value)) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.add(value)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tdelete(value: any): any {\n\t\t\tif (!this.has(value)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\treturn (\n\t\t\t\tstate.copy_!.delete(value) ||\n\t\t\t\t(state.drafts_.has(value)\n\t\t\t\t\t? state.copy_!.delete(state.drafts_.get(value))\n\t\t\t\t\t: /* istanbul ignore next */ false)\n\t\t\t)\n\t\t}\n\n\t\tclear() {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tvalues(): IterableIterator<any> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.values()\n\t\t}\n\n\t\tentries(): IterableIterator<[any, any]> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.entries()\n\t\t}\n\n\t\tkeys(): IterableIterator<any> {\n\t\t\treturn this.values()\n\t\t}\n\n\t\t[Symbol.iterator]() {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tforEach(cb: any, thisArg?: any) {\n\t\t\tconst iterator = this.values()\n\t\t\tlet result = iterator.next()\n\t\t\twhile (!result.done) {\n\t\t\t\tcb.call(thisArg, result.value, result.value, this)\n\t\t\t\tresult = iterator.next()\n\t\t\t}\n\t\t}\n\t}\n\tfunction proxySet_<T extends AnySet>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftSet(target, parent)\n\t}\n\n\tfunction prepareSetCopy(state: SetState) {\n\t\tif (!state.copy_) {\n\t\t\t// create drafts for all entries to preserve insertion order\n\t\t\tstate.copy_ = new Set()\n\t\t\tstate.base_.forEach(value => {\n\t\t\t\tif (isDraftable(value)) {\n\t\t\t\t\tconst draft = createProxy(value, state)\n\t\t\t\t\tstate.drafts_.set(value, draft)\n\t\t\t\t\tstate.copy_!.add(draft)\n\t\t\t\t} else {\n\t\t\t\t\tstate.copy_!.add(value)\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"MapSet\", {proxyMap_, proxySet_})\n}\n", "import {\n\tIProduce,\n\tIProduceWithPatches,\n\tImmer,\n\tDraft,\n\tImmutable\n} from \"./internal\"\n\nexport {\n\tDraft,\n\tWritableDraft,\n\tImmutable,\n\tPatch,\n\tPatchListener,\n\tProducer,\n\toriginal,\n\tcurrent,\n\tisDraft,\n\tisDraftable,\n\tNOTHING as nothing,\n\tDRAFTABLE as immerable,\n\tfreeze,\n\tObjectish,\n\tStrictMode\n} from \"./internal\"\n\nconst immer = new Immer()\n\n/**\n * The `produce` function takes a value and a \"recipe function\" (whose\n * return value often depends on the base state). The recipe function is\n * free to mutate its first argument however it wants. All mutations are\n * only ever applied to a __copy__ of the base state.\n *\n * Pass only a function to create a \"curried producer\" which relieves you\n * from passing the recipe function every time.\n *\n * Only plain objects and arrays are made mutable. All other objects are\n * considered uncopyable.\n *\n * Note: This function is __bound__ to its `Immer` instance.\n *\n * @param {any} base - the initial state\n * @param {Function} producer - function that receives a proxy of the base state as first argument and which can be freely modified\n * @param {Function} patchListener - optional function that will be called with all the patches produced here\n * @returns {any} a new state, or the initial state if nothing was modified\n */\nexport const produce: IProduce = immer.produce\n\n/**\n * Like `produce`, but `produceWithPatches` always returns a tuple\n * [nextState, patches, inversePatches] (instead of just the next state)\n */\nexport const produceWithPatches: IProduceWithPatches = immer.produceWithPatches.bind(\n\timmer\n)\n\n/**\n * Pass true to automatically freeze all copies created by Immer.\n *\n * Always freeze by default, even in production mode\n */\nexport const setAutoFreeze = immer.setAutoFreeze.bind(immer)\n\n/**\n * Pass true to enable strict shallow copy.\n *\n * By default, immer does not copy the object descriptors such as getter, setter and non-enumrable properties.\n */\nexport const setUseStrictShallowCopy = immer.setUseStrictShallowCopy.bind(immer)\n\n/**\n * Apply an array of Immer patches to the first argument.\n *\n * This function is a producer, which means copy-on-write is in effect.\n */\nexport const applyPatches = immer.applyPatches.bind(immer)\n\n/**\n * Create an Immer draft from the given base state, which may be a draft itself.\n * The draft can be modified until you finalize it with the `finishDraft` function.\n */\nexport const createDraft = immer.createDraft.bind(immer)\n\n/**\n * Finalize an Immer draft from a `createDraft` call, returning the base state\n * (if no changes were made) or a modified copy. The draft must *not* be\n * mutated afterwards.\n *\n * Pass a function as the 2nd argument to generate Immer patches based on the\n * changes that were made.\n */\nexport const finishDraft = immer.finishDraft.bind(immer)\n\n/**\n * This function is actually a no-op, but can be used to cast an immutable type\n * to an draft type and make TypeScript happy\n *\n * @param value\n */\nexport function castDraft<T>(value: T): Draft<T> {\n\treturn value as any\n}\n\n/**\n * This function is actually a no-op, but can be used to cast a mutable type\n * to an immutable type and make TypeScript happy\n * @param value\n */\nexport function castImmutable<T>(value: T): Immutable<T> {\n\treturn value as any\n}\n\nexport {Immer}\n\nexport {enablePatches} from \"./plugins/patches\"\nexport {enableMapSet} from \"./plugins/mapset\"\n", "import type { AnyFunction } from '../types'\r\n\r\n/**\r\n * Runs a check to determine if the given result function behaves as an\r\n * identity function. An identity function is one that returns its\r\n * input unchanged, for example, `x => x`. This check helps ensure\r\n * efficient memoization and prevent unnecessary re-renders by encouraging\r\n * proper use of transformation logic in result functions and\r\n * extraction logic in input selectors.\r\n *\r\n * @param resultFunc - The result function to be checked.\r\n * @param inputSelectorsResults - The results of the input selectors.\r\n * @param outputSelectorResult - The result of the output selector.\r\n *\r\n * @see {@link https://reselect.js.org/api/development-only-stability-checks#identityfunctioncheck `identityFunctionCheck`}\r\n *\r\n * @since 5.0.0\r\n * @internal\r\n */\r\nexport const runIdentityFunctionCheck = (\r\n  resultFunc: AnyFunction,\r\n  inputSelectorsResults: unknown[],\r\n  outputSelectorResult: unknown\r\n) => {\r\n  if (\r\n    inputSelectorsResults.length === 1 &&\r\n    inputSelectorsResults[0] === outputSelectorResult\r\n  ) {\r\n    let isInputSameAsOutput = false\r\n    try {\r\n      const emptyObject = {}\r\n      if (resultFunc(emptyObject) === emptyObject) isInputSameAsOutput = true\r\n    } catch {\r\n      // Do nothing\r\n    }\r\n    if (isInputSameAsOutput) {\r\n      let stack: string | undefined = undefined\r\n      try {\r\n        throw new Error()\r\n      } catch (e) {\r\n        // eslint-disable-next-line @typescript-eslint/no-extra-semi, no-extra-semi\r\n        ;({ stack } = e as Error)\r\n      }\r\n      console.warn(\r\n        'The result function returned its own inputs without modification. e.g' +\r\n          '\\n`createSelector([state => state.todos], todos => todos)`' +\r\n          '\\nThis could lead to inefficient memoization and unnecessary re-renders.' +\r\n          '\\nEnsure transformation logic is in the result function, and extraction logic is in the input selectors.',\r\n        { stack }\r\n      )\r\n    }\r\n  }\r\n}\r\n", "import type { CreateSelectorOptions, UnknownMemoizer } from '../types'\r\n\r\n/**\r\n * Runs a stability check to ensure the input selector results remain stable\r\n * when provided with the same arguments. This function is designed to detect\r\n * changes in the output of input selectors, which can impact the performance of memoized selectors.\r\n *\r\n * @param inputSelectorResultsObject - An object containing two arrays: `inputSelectorResults` and `inputSelectorResultsCopy`, representing the results of input selectors.\r\n * @param options - Options object consisting of a `memoize` function and a `memoizeOptions` object.\r\n * @param inputSelectorArgs - List of arguments being passed to the input selectors.\r\n *\r\n * @see {@link https://reselect.js.org/api/development-only-stability-checks/#inputstabilitycheck `inputStabilityCheck`}\r\n *\r\n * @since 5.0.0\r\n * @internal\r\n */\r\nexport const runInputStabilityCheck = (\r\n  inputSelectorResultsObject: {\r\n    inputSelectorResults: unknown[]\r\n    inputSelectorResultsCopy: unknown[]\r\n  },\r\n  options: Required<\r\n    Pick<\r\n      CreateSelectorOptions<UnknownMemoizer, UnknownMemoizer>,\r\n      'memoize' | 'memoizeOptions'\r\n    >\r\n  >,\r\n  inputSelectorArgs: unknown[] | IArguments\r\n) => {\r\n  const { memoize, memoizeOptions } = options\r\n  const { inputSelectorResults, inputSelectorResultsCopy } =\r\n    inputSelectorResultsObject\r\n  const createAnEmptyObject = memoize(() => ({}), ...memoizeOptions)\r\n  // if the memoize method thinks the parameters are equal, these *should* be the same reference\r\n  const areInputSelectorResultsEqual =\r\n    createAnEmptyObject.apply(null, inputSelectorResults) ===\r\n    createAnEmptyObject.apply(null, inputSelectorResultsCopy)\r\n  if (!areInputSelectorResultsEqual) {\r\n    let stack: string | undefined = undefined\r\n    try {\r\n      throw new Error()\r\n    } catch (e) {\r\n      // eslint-disable-next-line @typescript-eslint/no-extra-semi, no-extra-semi\r\n      ;({ stack } = e as Error)\r\n    }\r\n    console.warn(\r\n      'An input selector returned a different result when passed same arguments.' +\r\n        '\\nThis means your output selector will likely run more frequently than intended.' +\r\n        '\\nAvoid returning a new reference inside your input selector, e.g.' +\r\n        '\\n`createSelector([state => state.todos.map(todo => todo.id)], todoIds => todoIds.length)`',\r\n      {\r\n        arguments: inputSelectorArgs,\r\n        firstInputs: inputSelectorResults,\r\n        secondInputs: inputSelectorResultsCopy,\r\n        stack\r\n      }\r\n    )\r\n  }\r\n}\r\n", "import type { DevModeChe<PERSON> } from '../types'\r\n\r\n/**\r\n * Global configuration for development mode checks. This specifies the default\r\n * frequency at which each development mode check should be performed.\r\n *\r\n * @since 5.0.0\r\n * @internal\r\n */\r\nexport const globalDevModeChecks: DevModeChecks = {\r\n  inputStabilityCheck: 'once',\r\n  identityFunctionCheck: 'once'\r\n}\r\n\r\n/**\r\n * Overrides the development mode checks settings for all selectors.\r\n *\r\n * Reselect performs additional checks in development mode to help identify and\r\n * warn about potential issues in selector behavior. This function allows you to\r\n * customize the behavior of these checks across all selectors in your application.\r\n *\r\n * **Note**: This setting can still be overridden per selector inside `createSelector`'s `options` object.\r\n * See {@link https://github.com/reduxjs/reselect#2-per-selector-by-passing-an-identityfunctioncheck-option-directly-to-createselector per-selector-configuration}\r\n * and {@linkcode CreateSelectorOptions.identityFunctionCheck identityFunctionCheck} for more details.\r\n *\r\n * _The development mode checks do not run in production builds._\r\n *\r\n * @param devModeChecks - An object specifying the desired settings for development mode checks. You can provide partial overrides. Unspecified settings will retain their current values.\r\n *\r\n * @example\r\n * ```ts\r\n * import { setGlobalDevModeChecks } from 'reselect'\r\n * import { DevModeChecks } from '../types'\r\n *\r\n * // Run only the first time the selector is called. (default)\r\n * setGlobalDevModeChecks({ inputStabilityCheck: 'once' })\r\n *\r\n * // Run every time the selector is called.\r\n * setGlobalDevModeChecks({ inputStabilityCheck: 'always' })\r\n *\r\n * // Never run the input stability check.\r\n * setGlobalDevModeChecks({ inputStabilityCheck: 'never' })\r\n *\r\n * // Run only the first time the selector is called. (default)\r\n * setGlobalDevModeChecks({ identityFunctionCheck: 'once' })\r\n *\r\n * // Run every time the selector is called.\r\n * setGlobalDevModeChecks({ identityFunctionCheck: 'always' })\r\n *\r\n * // Never run the identity function check.\r\n * setGlobalDevModeChecks({ identityFunctionCheck: 'never' })\r\n * ```\r\n * @see {@link https://reselect.js.org/api/development-only-stability-checks Development-Only Stability Checks}\r\n * @see {@link https://reselect.js.org/api/development-only-stability-checks#1-globally-through-setglobaldevmodechecks global-configuration}\r\n *\r\n * @since 5.0.0\r\n * @public\r\n */\r\nexport const setGlobalDevModeChecks = (\r\n  devModeChecks: Partial<DevModeChecks>\r\n) => {\r\n  Object.assign(globalDevModeChecks, devModeChecks)\r\n}\r\n", "import { runIdentityFunctionCheck } from './devModeChecks/identityFunctionCheck'\r\nimport { runInputStabilityCheck } from './devModeChecks/inputStabilityCheck'\r\nimport { globalDevModeChecks } from './devModeChecks/setGlobalDevModeChecks'\r\n// eslint-disable-next-line @typescript-eslint/consistent-type-imports\r\nimport type {\r\n  <PERSON>Mode<PERSON>hecks,\r\n  Selector,\r\n  SelectorArray,\r\n  DevModeChecksExecutionInfo\r\n} from './types'\r\n\r\nexport const NOT_FOUND = /* @__PURE__ */ Symbol('NOT_FOUND')\r\nexport type NOT_FOUND_TYPE = typeof NOT_FOUND\r\n\r\n/**\r\n * Assert that the provided value is a function. If the assertion fails,\r\n * a `TypeError` is thrown with an optional custom error message.\r\n *\r\n * @param func - The value to be checked.\r\n * @param  errorMessage - An optional custom error message to use if the assertion fails.\r\n * @throws A `TypeError` if the assertion fails.\r\n */\r\nexport function assertIsFunction<FunctionType extends Function>(\r\n  func: unknown,\r\n  errorMessage = `expected a function, instead received ${typeof func}`\r\n): asserts func is FunctionType {\r\n  if (typeof func !== 'function') {\r\n    throw new TypeError(errorMessage)\r\n  }\r\n}\r\n\r\n/**\r\n * Assert that the provided value is an object. If the assertion fails,\r\n * a `TypeError` is thrown with an optional custom error message.\r\n *\r\n * @param object - The value to be checked.\r\n * @param  errorMessage - An optional custom error message to use if the assertion fails.\r\n * @throws A `TypeError` if the assertion fails.\r\n */\r\nexport function assertIsObject<ObjectType extends Record<string, unknown>>(\r\n  object: unknown,\r\n  errorMessage = `expected an object, instead received ${typeof object}`\r\n): asserts object is ObjectType {\r\n  if (typeof object !== 'object') {\r\n    throw new TypeError(errorMessage)\r\n  }\r\n}\r\n\r\n/**\r\n * Assert that the provided array is an array of functions. If the assertion fails,\r\n * a `TypeError` is thrown with an optional custom error message.\r\n *\r\n * @param array - The array to be checked.\r\n * @param  errorMessage - An optional custom error message to use if the assertion fails.\r\n * @throws A `TypeError` if the assertion fails.\r\n */\r\nexport function assertIsArrayOfFunctions<FunctionType extends Function>(\r\n  array: unknown[],\r\n  errorMessage = `expected all items to be functions, instead received the following types: `\r\n): asserts array is FunctionType[] {\r\n  if (\r\n    !array.every((item): item is FunctionType => typeof item === 'function')\r\n  ) {\r\n    const itemTypes = array\r\n      .map(item =>\r\n        typeof item === 'function'\r\n          ? `function ${item.name || 'unnamed'}()`\r\n          : typeof item\r\n      )\r\n      .join(', ')\r\n    throw new TypeError(`${errorMessage}[${itemTypes}]`)\r\n  }\r\n}\r\n\r\n/**\r\n * Ensure that the input is an array. If it's already an array, it's returned as is.\r\n * If it's not an array, it will be wrapped in a new array.\r\n *\r\n * @param item - The item to be checked.\r\n * @returns An array containing the input item. If the input is already an array, it's returned without modification.\r\n */\r\nexport const ensureIsArray = (item: unknown) => {\r\n  return Array.isArray(item) ? item : [item]\r\n}\r\n\r\n/**\r\n * Extracts the \"dependencies\" / \"input selectors\" from the arguments of `createSelector`.\r\n *\r\n * @param createSelectorArgs - Arguments passed to `createSelector` as an array.\r\n * @returns An array of \"input selectors\" / \"dependencies\".\r\n * @throws A `TypeError` if any of the input selectors is not function.\r\n */\r\nexport function getDependencies(createSelectorArgs: unknown[]) {\r\n  const dependencies = Array.isArray(createSelectorArgs[0])\r\n    ? createSelectorArgs[0]\r\n    : createSelectorArgs\r\n\r\n  assertIsArrayOfFunctions<Selector>(\r\n    dependencies,\r\n    `createSelector expects all input-selectors to be functions, but received the following types: `\r\n  )\r\n\r\n  return dependencies as SelectorArray\r\n}\r\n\r\n/**\r\n * Runs each input selector and returns their collective results as an array.\r\n *\r\n * @param dependencies - An array of \"dependencies\" or \"input selectors\".\r\n * @param inputSelectorArgs - An array of arguments being passed to the input selectors.\r\n * @returns An array of input selector results.\r\n */\r\nexport function collectInputSelectorResults(\r\n  dependencies: SelectorArray,\r\n  inputSelectorArgs: unknown[] | IArguments\r\n) {\r\n  const inputSelectorResults = []\r\n  const { length } = dependencies\r\n  for (let i = 0; i < length; i++) {\r\n    // @ts-ignore\r\n    // apply arguments instead of spreading and mutate a local list of params for performance.\r\n    inputSelectorResults.push(dependencies[i].apply(null, inputSelectorArgs))\r\n  }\r\n  return inputSelectorResults\r\n}\r\n\r\n/**\r\n * Retrieves execution information for development mode checks.\r\n *\r\n * @param devModeChecks - Custom Settings for development mode checks. These settings will override the global defaults.\r\n * @param firstRun - Indicates whether it is the first time the selector has run.\r\n * @returns  An object containing the execution information for each development mode check.\r\n */\r\nexport const getDevModeChecksExecutionInfo = (\r\n  firstRun: boolean,\r\n  devModeChecks: Partial<DevModeChecks>\r\n) => {\r\n  const { identityFunctionCheck, inputStabilityCheck } = {\r\n    ...globalDevModeChecks,\r\n    ...devModeChecks\r\n  }\r\n  return {\r\n    identityFunctionCheck: {\r\n      shouldRun:\r\n        identityFunctionCheck === 'always' ||\r\n        (identityFunctionCheck === 'once' && firstRun),\r\n      run: runIdentityFunctionCheck\r\n    },\r\n    inputStabilityCheck: {\r\n      shouldRun:\r\n        inputStabilityCheck === 'always' ||\r\n        (inputStabilityCheck === 'once' && firstRun),\r\n      run: runInputStabilityCheck\r\n    }\r\n  } satisfies DevModeChecksExecutionInfo\r\n}\r\n", "// Original autotracking implementation source:\r\n// - https://gist.github.com/pzuraq/79bf862e0f8cd9521b79c4b6eccdc4f9\r\n// Additional references:\r\n// - https://www.pzuraq.com/blog/how-autotracking-works\r\n// - https://v5.chriskrycho.com/journal/autotracking-elegant-dx-via-cutting-edge-cs/\r\nimport type { EqualityFn } from '../types'\r\nimport { assertIsFunction } from '../utils'\r\n\r\n// The global revision clock. Every time state changes, the clock increments.\r\nexport let $REVISION = 0\r\n\r\n// The current dependency tracker. Whenever we compute a cache, we create a Set\r\n// to track any dependencies that are used while computing. If no cache is\r\n// computing, then the tracker is null.\r\nlet CURRENT_TRACKER: Set<Cell<any> | TrackingCache> | null = null\r\n\r\n// Storage represents a root value in the system - the actual state of our app.\r\nexport class Cell<T> {\r\n  revision = $REVISION\r\n\r\n  _value: T\r\n  _lastValue: T\r\n  _isEqual: EqualityFn = tripleEq\r\n\r\n  constructor(initialValue: T, isEqual: EqualityFn = tripleEq) {\r\n    this._value = this._lastValue = initialValue\r\n    this._isEqual = isEqual\r\n  }\r\n\r\n  // Whenever a storage value is read, it'll add itself to the current tracker if\r\n  // one exists, entangling its state with that cache.\r\n  get value() {\r\n    CURRENT_TRACKER?.add(this)\r\n\r\n    return this._value\r\n  }\r\n\r\n  // Whenever a storage value is updated, we bump the global revision clock,\r\n  // assign the revision for this storage to the new value, _and_ we schedule a\r\n  // rerender. This is important, and it's what makes autotracking  _pull_\r\n  // based. We don't actively tell the caches which depend on the storage that\r\n  // anything has happened. Instead, we recompute the caches when needed.\r\n  set value(newValue) {\r\n    if (this.value === newValue) return\r\n\r\n    this._value = newValue\r\n    this.revision = ++$REVISION\r\n  }\r\n}\r\n\r\nfunction tripleEq(a: unknown, b: unknown) {\r\n  return a === b\r\n}\r\n\r\n// Caches represent derived state in the system. They are ultimately functions\r\n// that are memoized based on what state they use to produce their output,\r\n// meaning they will only rerun IFF a storage value that could affect the output\r\n// has changed. Otherwise, they'll return the cached value.\r\nexport class TrackingCache {\r\n  _cachedValue: any\r\n  _cachedRevision = -1\r\n  _deps: any[] = []\r\n  hits = 0\r\n\r\n  fn: () => any\r\n\r\n  constructor(fn: () => any) {\r\n    this.fn = fn\r\n  }\r\n\r\n  clear() {\r\n    this._cachedValue = undefined\r\n    this._cachedRevision = -1\r\n    this._deps = []\r\n    this.hits = 0\r\n  }\r\n\r\n  get value() {\r\n    // When getting the value for a Cache, first we check all the dependencies of\r\n    // the cache to see what their current revision is. If the current revision is\r\n    // greater than the cached revision, then something has changed.\r\n    if (this.revision > this._cachedRevision) {\r\n      const { fn } = this\r\n\r\n      // We create a new dependency tracker for this cache. As the cache runs\r\n      // its function, any Storage or Cache instances which are used while\r\n      // computing will be added to this tracker. In the end, it will be the\r\n      // full list of dependencies that this Cache depends on.\r\n      const currentTracker = new Set<Cell<any>>()\r\n      const prevTracker = CURRENT_TRACKER\r\n\r\n      CURRENT_TRACKER = currentTracker\r\n\r\n      // try {\r\n      this._cachedValue = fn()\r\n      // } finally {\r\n      CURRENT_TRACKER = prevTracker\r\n      this.hits++\r\n      this._deps = Array.from(currentTracker)\r\n\r\n      // Set the cached revision. This is the current clock count of all the\r\n      // dependencies. If any dependency changes, this number will be less\r\n      // than the new revision.\r\n      this._cachedRevision = this.revision\r\n      // }\r\n    }\r\n\r\n    // If there is a current tracker, it means another Cache is computing and\r\n    // using this one, so we add this one to the tracker.\r\n    CURRENT_TRACKER?.add(this)\r\n\r\n    // Always return the cached value.\r\n    return this._cachedValue\r\n  }\r\n\r\n  get revision() {\r\n    // The current revision is the max of all the dependencies' revisions.\r\n    return Math.max(...this._deps.map(d => d.revision), 0)\r\n  }\r\n}\r\n\r\nexport function getValue<T>(cell: Cell<T>): T {\r\n  if (!(cell instanceof Cell)) {\r\n    console.warn('Not a valid cell! ', cell)\r\n  }\r\n\r\n  return cell.value\r\n}\r\n\r\ntype CellValue<T extends Cell<unknown>> = T extends Cell<infer U> ? U : never\r\n\r\nexport function setValue<T extends Cell<unknown>>(\r\n  storage: T,\r\n  value: CellValue<T>\r\n): void {\r\n  if (!(storage instanceof Cell)) {\r\n    throw new TypeError(\r\n      'setValue must be passed a tracked store created with `createStorage`.'\r\n    )\r\n  }\r\n\r\n  storage.value = storage._lastValue = value\r\n}\r\n\r\nexport function createCell<T = unknown>(\r\n  initialValue: T,\r\n  isEqual: EqualityFn = tripleEq\r\n): Cell<T> {\r\n  return new Cell(initialValue, isEqual)\r\n}\r\n\r\nexport function createCache<T = unknown>(fn: () => T): TrackingCache {\r\n  assertIsFunction(\r\n    fn,\r\n    'the first parameter to `createCache` must be a function'\r\n  )\r\n\r\n  return new TrackingCache(fn)\r\n}\r\n", "import type { Cell } from './autotracking'\r\nimport {\r\n  getValue as consumeTag,\r\n  createCell as createStorage,\r\n  setValue\r\n} from './autotracking'\r\n\r\nexport type Tag = Cell<unknown>\r\n\r\nconst neverEq = (a: any, b: any): boolean => false\r\n\r\nexport function createTag(): Tag {\r\n  return createStorage(null, neverEq)\r\n}\r\nexport { consumeTag }\r\nexport function dirtyTag(tag: Tag, value: any): void {\r\n  setValue(tag, value)\r\n}\r\n\r\nexport interface Node<\r\n  T extends Array<unknown> | Record<string, unknown> =\r\n    | Array<unknown>\r\n    | Record<string, unknown>\r\n> {\r\n  collectionTag: Tag | null\r\n  tag: Tag | null\r\n  tags: Record<string, Tag>\r\n  children: Record<string, Node>\r\n  proxy: T\r\n  value: T\r\n  id: number\r\n}\r\n\r\nexport const consumeCollection = (node: Node): void => {\r\n  let tag = node.collectionTag\r\n\r\n  if (tag === null) {\r\n    tag = node.collectionTag = createTag()\r\n  }\r\n\r\n  consumeTag(tag)\r\n}\r\n\r\nexport const dirtyCollection = (node: Node): void => {\r\n  const tag = node.collectionTag\r\n\r\n  if (tag !== null) {\r\n    dirtyTag(tag, null)\r\n  }\r\n}\r\n", "// Original source:\r\n// - https://github.com/simonihmig/tracked-redux/blob/master/packages/tracked-redux/src/-private/proxy.ts\r\n\r\nimport type { Node, Tag } from './tracking'\r\nimport {\r\n  consumeCollection,\r\n  consumeTag,\r\n  createTag,\r\n  dirtyCollection,\r\n  dirtyTag\r\n} from './tracking'\r\n\r\nexport const REDUX_PROXY_LABEL = Symbol()\r\n\r\nlet nextId = 0\r\n\r\nconst proto = Object.getPrototypeOf({})\r\n\r\nclass ObjectTreeNode<T extends Record<string, unknown>> implements Node<T> {\r\n  proxy: T = new Proxy(this, objectProxyHandler) as unknown as T\r\n  tag = createTag()\r\n  tags = {} as Record<string, Tag>\r\n  children = {} as Record<string, Node>\r\n  collectionTag = null\r\n  id = nextId++\r\n\r\n  constructor(public value: T) {\r\n    this.value = value\r\n    this.tag.value = value\r\n  }\r\n}\r\n\r\nconst objectProxyHandler = {\r\n  get(node: Node, key: string | symbol): unknown {\r\n    function calculateResult() {\r\n      const { value } = node\r\n\r\n      const childValue = Reflect.get(value, key)\r\n\r\n      if (typeof key === 'symbol') {\r\n        return childValue\r\n      }\r\n\r\n      if (key in proto) {\r\n        return childValue\r\n      }\r\n\r\n      if (typeof childValue === 'object' && childValue !== null) {\r\n        let childNode = node.children[key]\r\n\r\n        if (childNode === undefined) {\r\n          childNode = node.children[key] = createNode(childValue)\r\n        }\r\n\r\n        if (childNode.tag) {\r\n          consumeTag(childNode.tag)\r\n        }\r\n\r\n        return childNode.proxy\r\n      } else {\r\n        let tag = node.tags[key]\r\n\r\n        if (tag === undefined) {\r\n          tag = node.tags[key] = createTag()\r\n          tag.value = childValue\r\n        }\r\n\r\n        consumeTag(tag)\r\n\r\n        return childValue\r\n      }\r\n    }\r\n    const res = calculateResult()\r\n    return res\r\n  },\r\n\r\n  ownKeys(node: Node): ArrayLike<string | symbol> {\r\n    consumeCollection(node)\r\n    return Reflect.ownKeys(node.value)\r\n  },\r\n\r\n  getOwnPropertyDescriptor(\r\n    node: Node,\r\n    prop: string | symbol\r\n  ): PropertyDescriptor | undefined {\r\n    return Reflect.getOwnPropertyDescriptor(node.value, prop)\r\n  },\r\n\r\n  has(node: Node, prop: string | symbol): boolean {\r\n    return Reflect.has(node.value, prop)\r\n  }\r\n}\r\n\r\nclass ArrayTreeNode<T extends Array<unknown>> implements Node<T> {\r\n  proxy: T = new Proxy([this], arrayProxyHandler) as unknown as T\r\n  tag = createTag()\r\n  tags = {}\r\n  children = {}\r\n  collectionTag = null\r\n  id = nextId++\r\n\r\n  constructor(public value: T) {\r\n    this.value = value\r\n    this.tag.value = value\r\n  }\r\n}\r\n\r\nconst arrayProxyHandler = {\r\n  get([node]: [Node], key: string | symbol): unknown {\r\n    if (key === 'length') {\r\n      consumeCollection(node)\r\n    }\r\n\r\n    return objectProxyHandler.get(node, key)\r\n  },\r\n\r\n  ownKeys([node]: [Node]): ArrayLike<string | symbol> {\r\n    return objectProxyHandler.ownKeys(node)\r\n  },\r\n\r\n  getOwnPropertyDescriptor(\r\n    [node]: [Node],\r\n    prop: string | symbol\r\n  ): PropertyDescriptor | undefined {\r\n    return objectProxyHandler.getOwnPropertyDescriptor(node, prop)\r\n  },\r\n\r\n  has([node]: [Node], prop: string | symbol): boolean {\r\n    return objectProxyHandler.has(node, prop)\r\n  }\r\n}\r\n\r\nexport function createNode<T extends Array<unknown> | Record<string, unknown>>(\r\n  value: T\r\n): Node<T> {\r\n  if (Array.isArray(value)) {\r\n    return new ArrayTreeNode(value)\r\n  }\r\n\r\n  return new ObjectTreeNode(value) as Node<T>\r\n}\r\n\r\nconst keysMap = new WeakMap<\r\n  Array<unknown> | Record<string, unknown>,\r\n  Set<string>\r\n>()\r\n\r\nexport function updateNode<T extends Array<unknown> | Record<string, unknown>>(\r\n  node: Node<T>,\r\n  newValue: T\r\n): void {\r\n  const { value, tags, children } = node\r\n\r\n  node.value = newValue\r\n\r\n  if (\r\n    Array.isArray(value) &&\r\n    Array.isArray(newValue) &&\r\n    value.length !== newValue.length\r\n  ) {\r\n    dirtyCollection(node)\r\n  } else {\r\n    if (value !== newValue) {\r\n      let oldKeysSize = 0\r\n      let newKeysSize = 0\r\n      let anyKeysAdded = false\r\n\r\n      for (const _key in value) {\r\n        oldKeysSize++\r\n      }\r\n\r\n      for (const key in newValue) {\r\n        newKeysSize++\r\n        if (!(key in value)) {\r\n          anyKeysAdded = true\r\n          break\r\n        }\r\n      }\r\n\r\n      const isDifferent = anyKeysAdded || oldKeysSize !== newKeysSize\r\n\r\n      if (isDifferent) {\r\n        dirtyCollection(node)\r\n      }\r\n    }\r\n  }\r\n\r\n  for (const key in tags) {\r\n    const childValue = (value as Record<string, unknown>)[key]\r\n    const newChildValue = (newValue as Record<string, unknown>)[key]\r\n\r\n    if (childValue !== newChildValue) {\r\n      dirtyCollection(node)\r\n      dirtyTag(tags[key], newChildValue)\r\n    }\r\n\r\n    if (typeof newChildValue === 'object' && newChildValue !== null) {\r\n      delete tags[key]\r\n    }\r\n  }\r\n\r\n  for (const key in children) {\r\n    const childNode = children[key]\r\n    const newChildValue = (newValue as Record<string, unknown>)[key]\r\n\r\n    const childValue = childNode.value\r\n\r\n    if (childValue === newChildValue) {\r\n      continue\r\n    } else if (typeof newChildValue === 'object' && newChildValue !== null) {\r\n      updateNode(childNode, newChildValue as Record<string, unknown>)\r\n    } else {\r\n      deleteNode(childNode)\r\n      delete children[key]\r\n    }\r\n  }\r\n}\r\n\r\nfunction deleteNode(node: Node): void {\r\n  if (node.tag) {\r\n    dirtyTag(node.tag, null)\r\n  }\r\n  dirtyCollection(node)\r\n  for (const key in node.tags) {\r\n    dirtyTag(node.tags[key], null)\r\n  }\r\n  for (const key in node.children) {\r\n    deleteNode(node.children[key])\r\n  }\r\n}\r\n", "import type {\r\n  AnyFunction,\r\n  DefaultMemoize<PERSON><PERSON>s,\r\n  EqualityFn,\r\n  Simplify\r\n} from './types'\r\n\r\nimport type { NOT_FOUND_TYPE } from './utils'\r\nimport { NOT_FOUND } from './utils'\r\n\r\n// Cache implementation based on <PERSON>'s `lru-memoize`:\r\n// https://github.com/erikras/lru-memoize\r\n\r\ninterface Entry {\r\n  key: unknown\r\n  value: unknown\r\n}\r\n\r\ninterface Cache {\r\n  get(key: unknown): unknown | NOT_FOUND_TYPE\r\n  put(key: unknown, value: unknown): void\r\n  getEntries(): Entry[]\r\n  clear(): void\r\n}\r\n\r\nfunction createSingletonCache(equals: EqualityFn): Cache {\r\n  let entry: Entry | undefined\r\n  return {\r\n    get(key: unknown) {\r\n      if (entry && equals(entry.key, key)) {\r\n        return entry.value\r\n      }\r\n\r\n      return NOT_FOUND\r\n    },\r\n\r\n    put(key: unknown, value: unknown) {\r\n      entry = { key, value }\r\n    },\r\n\r\n    getEntries() {\r\n      return entry ? [entry] : []\r\n    },\r\n\r\n    clear() {\r\n      entry = undefined\r\n    }\r\n  }\r\n}\r\n\r\nfunction createLruCache(maxSize: number, equals: EqualityFn): Cache {\r\n  let entries: Entry[] = []\r\n\r\n  function get(key: unknown) {\r\n    const cacheIndex = entries.findIndex(entry => equals(key, entry.key))\r\n\r\n    // We found a cached entry\r\n    if (cacheIndex > -1) {\r\n      const entry = entries[cacheIndex]\r\n\r\n      // Cached entry not at top of cache, move it to the top\r\n      if (cacheIndex > 0) {\r\n        entries.splice(cacheIndex, 1)\r\n        entries.unshift(entry)\r\n      }\r\n\r\n      return entry.value\r\n    }\r\n\r\n    // No entry found in cache, return sentinel\r\n    return NOT_FOUND\r\n  }\r\n\r\n  function put(key: unknown, value: unknown) {\r\n    if (get(key) === NOT_FOUND) {\r\n      // TODO Is unshift slow?\r\n      entries.unshift({ key, value })\r\n      if (entries.length > maxSize) {\r\n        entries.pop()\r\n      }\r\n    }\r\n  }\r\n\r\n  function getEntries() {\r\n    return entries\r\n  }\r\n\r\n  function clear() {\r\n    entries = []\r\n  }\r\n\r\n  return { get, put, getEntries, clear }\r\n}\r\n\r\n/**\r\n * Runs a simple reference equality check.\r\n * What {@linkcode lruMemoize lruMemoize} uses by default.\r\n *\r\n * **Note**: This function was previously known as `defaultEqualityCheck`.\r\n *\r\n * @public\r\n */\r\nexport const referenceEqualityCheck: EqualityFn = (a, b) => a === b\r\n\r\nexport function createCacheKeyComparator(equalityCheck: EqualityFn) {\r\n  return function areArgumentsShallowlyEqual(\r\n    prev: unknown[] | IArguments | null,\r\n    next: unknown[] | IArguments | null\r\n  ): boolean {\r\n    if (prev === null || next === null || prev.length !== next.length) {\r\n      return false\r\n    }\r\n\r\n    // Do this in a for loop (and not a `forEach` or an `every`) so we can determine equality as fast as possible.\r\n    const { length } = prev\r\n    for (let i = 0; i < length; i++) {\r\n      if (!equalityCheck(prev[i], next[i])) {\r\n        return false\r\n      }\r\n    }\r\n\r\n    return true\r\n  }\r\n}\r\n\r\n/**\r\n * Options for configuring the behavior of a function memoized with\r\n * LRU (Least Recently Used) caching.\r\n *\r\n * @template Result - The type of the return value of the memoized function.\r\n *\r\n * @public\r\n */\r\nexport interface LruMemoizeOptions<Result = any> {\r\n  /**\r\n   * Function used to compare the individual arguments of the\r\n   * provided calculation function.\r\n   *\r\n   * @default referenceEqualityCheck\r\n   */\r\n  equalityCheck?: EqualityFn\r\n\r\n  /**\r\n   * If provided, used to compare a newly generated output value against\r\n   * previous values in the cache. If a match is found,\r\n   * the old value is returned. This addresses the common\r\n   * ```ts\r\n   * todos.map(todo => todo.id)\r\n   * ```\r\n   * use case, where an update to another field in the original data causes\r\n   * a recalculation due to changed references, but the output is still\r\n   * effectively the same.\r\n   *\r\n   * @since 4.1.0\r\n   */\r\n  resultEqualityCheck?: EqualityFn<Result>\r\n\r\n  /**\r\n   * The maximum size of the cache used by the selector.\r\n   * A size greater than 1 means the selector will use an\r\n   * LRU (Least Recently Used) cache, allowing for the caching of multiple\r\n   * results based on different sets of arguments.\r\n   *\r\n   * @default 1\r\n   */\r\n  maxSize?: number\r\n}\r\n\r\n/**\r\n * Creates a memoized version of a function with an optional\r\n * LRU (Least Recently Used) cache. The memoized function uses a cache to\r\n * store computed values. Depending on the `maxSize` option, it will use\r\n * either a singleton cache (for a single entry) or an\r\n * LRU cache (for multiple entries).\r\n *\r\n * **Note**: This function was previously known as `defaultMemoize`.\r\n *\r\n * @param func - The function to be memoized.\r\n * @param equalityCheckOrOptions - Either an equality check function or an options object.\r\n * @returns A memoized function with a `.clearCache()` method attached.\r\n *\r\n * @template Func - The type of the function that is memoized.\r\n *\r\n * @see {@link https://reselect.js.org/api/lruMemoize `lruMemoize`}\r\n *\r\n * @public\r\n */\r\nexport function lruMemoize<Func extends AnyFunction>(\r\n  func: Func,\r\n  equalityCheckOrOptions?: EqualityFn | LruMemoizeOptions<ReturnType<Func>>\r\n) {\r\n  const providedOptions =\r\n    typeof equalityCheckOrOptions === 'object'\r\n      ? equalityCheckOrOptions\r\n      : { equalityCheck: equalityCheckOrOptions }\r\n\r\n  const {\r\n    equalityCheck = referenceEqualityCheck,\r\n    maxSize = 1,\r\n    resultEqualityCheck\r\n  } = providedOptions\r\n\r\n  const comparator = createCacheKeyComparator(equalityCheck)\r\n\r\n  let resultsCount = 0\r\n\r\n  const cache =\r\n    maxSize <= 1\r\n      ? createSingletonCache(comparator)\r\n      : createLruCache(maxSize, comparator)\r\n\r\n  function memoized() {\r\n    let value = cache.get(arguments) as ReturnType<Func>\r\n    if (value === NOT_FOUND) {\r\n      // apply arguments instead of spreading for performance.\r\n      // @ts-ignore\r\n      value = func.apply(null, arguments) as ReturnType<Func>\r\n      resultsCount++\r\n\r\n      if (resultEqualityCheck) {\r\n        const entries = cache.getEntries()\r\n        const matchingEntry = entries.find(entry =>\r\n          resultEqualityCheck(entry.value as ReturnType<Func>, value)\r\n        )\r\n\r\n        if (matchingEntry) {\r\n          value = matchingEntry.value as ReturnType<Func>\r\n          resultsCount !== 0 && resultsCount--\r\n        }\r\n      }\r\n\r\n      cache.put(arguments, value)\r\n    }\r\n    return value\r\n  }\r\n\r\n  memoized.clearCache = () => {\r\n    cache.clear()\r\n    memoized.resetResultsCount()\r\n  }\r\n\r\n  memoized.resultsCount = () => resultsCount\r\n\r\n  memoized.resetResultsCount = () => {\r\n    resultsCount = 0\r\n  }\r\n\r\n  return memoized as Func & Simplify<DefaultMemoizeFields>\r\n}\r\n", "import { createNode, updateNode } from './proxy'\r\nimport type { Node } from './tracking'\r\n\r\nimport { createCacheKeyComparator, referenceEqualityCheck } from '../lruMemoize'\r\nimport type { AnyFunction, DefaultMemoizeFields, Simplify } from '../types'\r\nimport { createCache } from './autotracking'\r\n\r\n/**\r\n * Uses an \"auto-tracking\" approach inspired by the work of the Ember Glimmer team.\r\n * It uses a Proxy to wrap arguments and track accesses to nested fields\r\n * in your selector on first read. Later, when the selector is called with\r\n * new arguments, it identifies which accessed fields have changed and\r\n * only recalculates the result if one or more of those accessed fields have changed.\r\n * This allows it to be more precise than the shallow equality checks in `lruMemoize`.\r\n *\r\n * __Design Tradeoffs for `autotrackMemoize`:__\r\n * - Pros:\r\n *    - It is likely to avoid excess calculations and recalculate fewer times than `lruMemoize` will,\r\n *    which may also result in fewer component re-renders.\r\n * - Cons:\r\n *    - It only has a cache size of 1.\r\n *    - It is slower than `lruMemoize`, because it has to do more work. (How much slower is dependent on the number of accessed fields in a selector, number of calls, frequency of input changes, etc)\r\n *    - It can have some unexpected behavior. Because it tracks nested field accesses,\r\n *    cases where you don't access a field will not recalculate properly.\r\n *    For example, a badly-written selector like:\r\n *      ```ts\r\n *      createSelector([state => state.todos], todos => todos)\r\n *      ```\r\n *      that just immediately returns the extracted value will never update, because it doesn't see any field accesses to check.\r\n *\r\n * __Use Cases for `autotrackMemoize`:__\r\n * - It is likely best used for cases where you need to access specific nested fields\r\n * in data, and avoid recalculating if other fields in the same data objects are immutably updated.\r\n *\r\n * @param func - The function to be memoized.\r\n * @returns A memoized function with a `.clearCache()` method attached.\r\n *\r\n * @example\r\n * <caption>Using `createSelector`</caption>\r\n * ```ts\r\n * import { unstable_autotrackMemoize as autotrackMemoize, createSelector } from 'reselect'\r\n *\r\n * const selectTodoIds = createSelector(\r\n *   [(state: RootState) => state.todos],\r\n *   (todos) => todos.map(todo => todo.id),\r\n *   { memoize: autotrackMemoize }\r\n * )\r\n * ```\r\n *\r\n * @example\r\n * <caption>Using `createSelectorCreator`</caption>\r\n * ```ts\r\n * import { unstable_autotrackMemoize as autotrackMemoize, createSelectorCreator } from 'reselect'\r\n *\r\n * const createSelectorAutotrack = createSelectorCreator({ memoize: autotrackMemoize })\r\n *\r\n * const selectTodoIds = createSelectorAutotrack(\r\n *   [(state: RootState) => state.todos],\r\n *   (todos) => todos.map(todo => todo.id)\r\n * )\r\n * ```\r\n *\r\n * @template Func - The type of the function that is memoized.\r\n *\r\n * @see {@link https://reselect.js.org/api/unstable_autotrackMemoize autotrackMemoize}\r\n *\r\n * @since 5.0.0\r\n * @public\r\n * @experimental\r\n */\r\nexport function autotrackMemoize<Func extends AnyFunction>(func: Func) {\r\n  // we reference arguments instead of spreading them for performance reasons\r\n\r\n  const node: Node<Record<string, unknown>> = createNode(\r\n    [] as unknown as Record<string, unknown>\r\n  )\r\n\r\n  let lastArgs: IArguments | null = null\r\n\r\n  const shallowEqual = createCacheKeyComparator(referenceEqualityCheck)\r\n\r\n  const cache = createCache(() => {\r\n    const res = func.apply(null, node.proxy as unknown as any[])\r\n    return res\r\n  })\r\n\r\n  function memoized() {\r\n    if (!shallowEqual(lastArgs, arguments)) {\r\n      updateNode(node, arguments as unknown as Record<string, unknown>)\r\n      lastArgs = arguments\r\n    }\r\n    return cache.value\r\n  }\r\n\r\n  memoized.clearCache = () => {\r\n    return cache.clear()\r\n  }\r\n\r\n  return memoized as Func & Simplify<DefaultMemoizeFields>\r\n}\r\n", "// Original source:\r\n// - https://github.com/facebook/react/blob/0b974418c9a56f6c560298560265dcf4b65784bc/packages/react/src/ReactCache.js\r\n\r\nimport type {\r\n  AnyFunction,\r\n  DefaultMemoizeFields,\r\n  EqualityFn,\r\n  Simplify\r\n} from './types'\r\n\r\nclass StrongRef<T> {\r\n  constructor(private value: T) {}\r\n  deref() {\r\n    return this.value\r\n  }\r\n}\r\n\r\nconst Ref =\r\n  typeof WeakRef !== 'undefined'\r\n    ? WeakRef\r\n    : (StrongRef as unknown as typeof WeakRef)\r\n\r\nconst UNTERMINATED = 0\r\nconst TERMINATED = 1\r\n\r\ninterface UnterminatedCacheNode<T> {\r\n  /**\r\n   * Status, represents whether the cached computation returned a value or threw an error.\r\n   */\r\n  s: 0\r\n  /**\r\n   * Value, either the cached result or an error, depending on status.\r\n   */\r\n  v: void\r\n  /**\r\n   * Object cache, a `WeakMap` where non-primitive arguments are stored.\r\n   */\r\n  o: null | WeakMap<Function | Object, CacheNode<T>>\r\n  /**\r\n   * Primitive cache, a regular Map where primitive arguments are stored.\r\n   */\r\n  p: null | Map<string | number | null | void | symbol | boolean, CacheNode<T>>\r\n}\r\n\r\ninterface TerminatedCacheNode<T> {\r\n  /**\r\n   * Status, represents whether the cached computation returned a value or threw an error.\r\n   */\r\n  s: 1\r\n  /**\r\n   * Value, either the cached result or an error, depending on status.\r\n   */\r\n  v: T\r\n  /**\r\n   * Object cache, a `WeakMap` where non-primitive arguments are stored.\r\n   */\r\n  o: null | WeakMap<Function | Object, CacheNode<T>>\r\n  /**\r\n   * Primitive cache, a regular `Map` where primitive arguments are stored.\r\n   */\r\n  p: null | Map<string | number | null | void | symbol | boolean, CacheNode<T>>\r\n}\r\n\r\ntype CacheNode<T> = TerminatedCacheNode<T> | UnterminatedCacheNode<T>\r\n\r\nfunction createCacheNode<T>(): CacheNode<T> {\r\n  return {\r\n    s: UNTERMINATED,\r\n    v: undefined,\r\n    o: null,\r\n    p: null\r\n  }\r\n}\r\n\r\n/**\r\n * Configuration options for a memoization function utilizing `WeakMap` for\r\n * its caching mechanism.\r\n *\r\n * @template Result - The type of the return value of the memoized function.\r\n *\r\n * @since 5.0.0\r\n * @public\r\n */\r\nexport interface WeakMapMemoizeOptions<Result = any> {\r\n  /**\r\n   * If provided, used to compare a newly generated output value against previous values in the cache.\r\n   * If a match is found, the old value is returned. This addresses the common\r\n   * ```ts\r\n   * todos.map(todo => todo.id)\r\n   * ```\r\n   * use case, where an update to another field in the original data causes a recalculation\r\n   * due to changed references, but the output is still effectively the same.\r\n   *\r\n   * @since 5.0.0\r\n   */\r\n  resultEqualityCheck?: EqualityFn<Result>\r\n}\r\n\r\n/**\r\n * Creates a tree of `WeakMap`-based cache nodes based on the identity of the\r\n * arguments it's been called with (in this case, the extracted values from your input selectors).\r\n * This allows `weakMapMemoize` to have an effectively infinite cache size.\r\n * Cache results will be kept in memory as long as references to the arguments still exist,\r\n * and then cleared out as the arguments are garbage-collected.\r\n *\r\n * __Design Tradeoffs for `weakMapMemoize`:__\r\n * - Pros:\r\n *   - It has an effectively infinite cache size, but you have no control over\r\n *   how long values are kept in cache as it's based on garbage collection and `WeakMap`s.\r\n * - Cons:\r\n *   - There's currently no way to alter the argument comparisons.\r\n *   They're based on strict reference equality.\r\n *   - It's roughly the same speed as `lruMemoize`, although likely a fraction slower.\r\n *\r\n * __Use Cases for `weakMapMemoize`:__\r\n * - This memoizer is likely best used for cases where you need to call the\r\n * same selector instance with many different arguments, such as a single\r\n * selector instance that is used in a list item component and called with\r\n * item IDs like:\r\n *   ```ts\r\n *   useSelector(state => selectSomeData(state, props.category))\r\n *   ```\r\n * @param func - The function to be memoized.\r\n * @returns A memoized function with a `.clearCache()` method attached.\r\n *\r\n * @example\r\n * <caption>Using `createSelector`</caption>\r\n * ```ts\r\n * import { createSelector, weakMapMemoize } from 'reselect'\r\n *\r\n * interface RootState {\r\n *   items: { id: number; category: string; name: string }[]\r\n * }\r\n *\r\n * const selectItemsByCategory = createSelector(\r\n *   [\r\n *     (state: RootState) => state.items,\r\n *     (state: RootState, category: string) => category\r\n *   ],\r\n *   (items, category) => items.filter(item => item.category === category),\r\n *   {\r\n *     memoize: weakMapMemoize,\r\n *     argsMemoize: weakMapMemoize\r\n *   }\r\n * )\r\n * ```\r\n *\r\n * @example\r\n * <caption>Using `createSelectorCreator`</caption>\r\n * ```ts\r\n * import { createSelectorCreator, weakMapMemoize } from 'reselect'\r\n *\r\n * const createSelectorWeakMap = createSelectorCreator({ memoize: weakMapMemoize, argsMemoize: weakMapMemoize })\r\n *\r\n * const selectItemsByCategory = createSelectorWeakMap(\r\n *   [\r\n *     (state: RootState) => state.items,\r\n *     (state: RootState, category: string) => category\r\n *   ],\r\n *   (items, category) => items.filter(item => item.category === category)\r\n * )\r\n * ```\r\n *\r\n * @template Func - The type of the function that is memoized.\r\n *\r\n * @see {@link https://reselect.js.org/api/weakMapMemoize `weakMapMemoize`}\r\n *\r\n * @since 5.0.0\r\n * @public\r\n * @experimental\r\n */\r\nexport function weakMapMemoize<Func extends AnyFunction>(\r\n  func: Func,\r\n  options: WeakMapMemoizeOptions<ReturnType<Func>> = {}\r\n) {\r\n  let fnNode = createCacheNode()\r\n  const { resultEqualityCheck } = options\r\n\r\n  let lastResult: WeakRef<object> | undefined\r\n\r\n  let resultsCount = 0\r\n\r\n  function memoized() {\r\n    let cacheNode = fnNode\r\n    const { length } = arguments\r\n    for (let i = 0, l = length; i < l; i++) {\r\n      const arg = arguments[i]\r\n      if (\r\n        typeof arg === 'function' ||\r\n        (typeof arg === 'object' && arg !== null)\r\n      ) {\r\n        // Objects go into a WeakMap\r\n        let objectCache = cacheNode.o\r\n        if (objectCache === null) {\r\n          cacheNode.o = objectCache = new WeakMap()\r\n        }\r\n        const objectNode = objectCache.get(arg)\r\n        if (objectNode === undefined) {\r\n          cacheNode = createCacheNode()\r\n          objectCache.set(arg, cacheNode)\r\n        } else {\r\n          cacheNode = objectNode\r\n        }\r\n      } else {\r\n        // Primitives go into a regular Map\r\n        let primitiveCache = cacheNode.p\r\n        if (primitiveCache === null) {\r\n          cacheNode.p = primitiveCache = new Map()\r\n        }\r\n        const primitiveNode = primitiveCache.get(arg)\r\n        if (primitiveNode === undefined) {\r\n          cacheNode = createCacheNode()\r\n          primitiveCache.set(arg, cacheNode)\r\n        } else {\r\n          cacheNode = primitiveNode\r\n        }\r\n      }\r\n    }\r\n\r\n    const terminatedNode = cacheNode as unknown as TerminatedCacheNode<any>\r\n\r\n    let result\r\n\r\n    if (cacheNode.s === TERMINATED) {\r\n      result = cacheNode.v\r\n    } else {\r\n      // Allow errors to propagate\r\n      result = func.apply(null, arguments as unknown as any[])\r\n      resultsCount++\r\n\r\n      if (resultEqualityCheck) {\r\n        const lastResultValue = lastResult?.deref?.() ?? lastResult\r\n\r\n        if (\r\n          lastResultValue != null &&\r\n          resultEqualityCheck(lastResultValue as ReturnType<Func>, result)\r\n        ) {\r\n          result = lastResultValue\r\n\r\n          resultsCount !== 0 && resultsCount--\r\n        }\r\n\r\n        const needsWeakRef =\r\n          (typeof result === 'object' && result !== null) ||\r\n          typeof result === 'function'\r\n\r\n        lastResult = needsWeakRef ? new Ref(result) : result\r\n      }\r\n    }\r\n\r\n    terminatedNode.s = TERMINATED\r\n\r\n    terminatedNode.v = result\r\n    return result\r\n  }\r\n\r\n  memoized.clearCache = () => {\r\n    fnNode = createCacheNode()\r\n    memoized.resetResultsCount()\r\n  }\r\n\r\n  memoized.resultsCount = () => resultsCount\r\n\r\n  memoized.resetResultsCount = () => {\r\n    resultsCount = 0\r\n  }\r\n\r\n  return memoized as Func & Simplify<DefaultMemoizeFields>\r\n}\r\n", "import { weakMapMemoize } from './weakMapMemoize'\r\n\r\nimport type {\r\n  <PERSON><PERSON><PERSON>,\r\n  CreateSelectorOptions,\r\n  DropFirstParameter,\r\n  ExtractMemoizerFields,\r\n  GetParamsFromSelectors,\r\n  GetStateFromSelectors,\r\n  InterruptRecursion,\r\n  OutputSelector,\r\n  Selector,\r\n  SelectorArray,\r\n  SetRequired,\r\n  Simplify,\r\n  UnknownMemoizer\r\n} from './types'\r\n\r\nimport {\r\n  assertIsFunction,\r\n  collectInputSelectorResults,\r\n  ensureIsArray,\r\n  getDependencies,\r\n  getDevModeChecksExecutionInfo\r\n} from './utils'\r\n\r\n/**\r\n * An instance of `createSelector`, customized with a given memoize implementation.\r\n *\r\n * @template MemoizeFunction - The type of the memoize function that is used to memoize the `resultFunc` inside `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`).\r\n * @template ArgsMemoizeFunction - The type of the optional memoize function that is used to memoize the arguments passed into the output selector generated by `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`). If none is explicitly provided, `weakMapMemoize` will be used.\r\n * @template StateType - The type of state that the selectors created with this selector creator will operate on.\r\n *\r\n * @public\r\n */\r\nexport interface CreateSelectorFunction<\r\n  MemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize,\r\n  ArgsMemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize,\r\n  StateType = any\r\n> {\r\n  /**\r\n   * Creates a memoized selector function.\r\n   *\r\n   * @param createSelectorArgs - An arbitrary number of input selectors as separate inline arguments and a `combiner` function.\r\n   * @returns A memoized output selector.\r\n   *\r\n   * @template InputSelectors - The type of the input selectors as an array.\r\n   * @template Result - The return type of the `combiner` as well as the output selector.\r\n   * @template OverrideMemoizeFunction - The type of the optional `memoize` function that could be passed into the options object to override the original `memoize` function that was initially passed into `createSelectorCreator`.\r\n   * @template OverrideArgsMemoizeFunction - The type of the optional `argsMemoize` function that could be passed into the options object to override the original `argsMemoize` function that was initially passed into `createSelectorCreator`.\r\n   *\r\n   * @see {@link https://reselect.js.org/api/createselector `createSelector`}\r\n   */\r\n  <InputSelectors extends SelectorArray<StateType>, Result>(\r\n    ...createSelectorArgs: [\r\n      ...inputSelectors: InputSelectors,\r\n      combiner: Combiner<InputSelectors, Result>\r\n    ]\r\n  ): OutputSelector<\r\n    InputSelectors,\r\n    Result,\r\n    MemoizeFunction,\r\n    ArgsMemoizeFunction\r\n  > &\r\n    InterruptRecursion\r\n\r\n  /**\r\n   * Creates a memoized selector function.\r\n   *\r\n   * @param createSelectorArgs - An arbitrary number of input selectors as separate inline arguments, a `combiner` function and an `options` object.\r\n   * @returns A memoized output selector.\r\n   *\r\n   * @template InputSelectors - The type of the input selectors as an array.\r\n   * @template Result - The return type of the `combiner` as well as the output selector.\r\n   * @template OverrideMemoizeFunction - The type of the optional `memoize` function that could be passed into the options object to override the original `memoize` function that was initially passed into `createSelectorCreator`.\r\n   * @template OverrideArgsMemoizeFunction - The type of the optional `argsMemoize` function that could be passed into the options object to override the original `argsMemoize` function that was initially passed into `createSelectorCreator`.\r\n   *\r\n   * @see {@link https://reselect.js.org/api/createselector `createSelector`}\r\n   */\r\n  <\r\n    InputSelectors extends SelectorArray<StateType>,\r\n    Result,\r\n    OverrideMemoizeFunction extends UnknownMemoizer = MemoizeFunction,\r\n    OverrideArgsMemoizeFunction extends UnknownMemoizer = ArgsMemoizeFunction\r\n  >(\r\n    ...createSelectorArgs: [\r\n      ...inputSelectors: InputSelectors,\r\n      combiner: Combiner<InputSelectors, Result>,\r\n      createSelectorOptions: Simplify<\r\n        CreateSelectorOptions<\r\n          MemoizeFunction,\r\n          ArgsMemoizeFunction,\r\n          OverrideMemoizeFunction,\r\n          OverrideArgsMemoizeFunction\r\n        >\r\n      >\r\n    ]\r\n  ): OutputSelector<\r\n    InputSelectors,\r\n    Result,\r\n    OverrideMemoizeFunction,\r\n    OverrideArgsMemoizeFunction\r\n  > &\r\n    InterruptRecursion\r\n\r\n  /**\r\n   * Creates a memoized selector function.\r\n   *\r\n   * @param inputSelectors - An array of input selectors.\r\n   * @param combiner - A function that Combines the input selectors and returns an output selector. Otherwise known as the result function.\r\n   * @param createSelectorOptions - An optional options object that allows for further customization per selector.\r\n   * @returns A memoized output selector.\r\n   *\r\n   * @template InputSelectors - The type of the input selectors array.\r\n   * @template Result - The return type of the `combiner` as well as the output selector.\r\n   * @template OverrideMemoizeFunction - The type of the optional `memoize` function that could be passed into the options object to override the original `memoize` function that was initially passed into `createSelectorCreator`.\r\n   * @template OverrideArgsMemoizeFunction - The type of the optional `argsMemoize` function that could be passed into the options object to override the original `argsMemoize` function that was initially passed into `createSelectorCreator`.\r\n   *\r\n   * @see {@link https://reselect.js.org/api/createselector `createSelector`}\r\n   */\r\n  <\r\n    InputSelectors extends SelectorArray<StateType>,\r\n    Result,\r\n    OverrideMemoizeFunction extends UnknownMemoizer = MemoizeFunction,\r\n    OverrideArgsMemoizeFunction extends UnknownMemoizer = ArgsMemoizeFunction\r\n  >(\r\n    inputSelectors: [...InputSelectors],\r\n    combiner: Combiner<InputSelectors, Result>,\r\n    createSelectorOptions?: Simplify<\r\n      CreateSelectorOptions<\r\n        MemoizeFunction,\r\n        ArgsMemoizeFunction,\r\n        OverrideMemoizeFunction,\r\n        OverrideArgsMemoizeFunction\r\n      >\r\n    >\r\n  ): OutputSelector<\r\n    InputSelectors,\r\n    Result,\r\n    OverrideMemoizeFunction,\r\n    OverrideArgsMemoizeFunction\r\n  > &\r\n    InterruptRecursion\r\n\r\n  /**\r\n   * Creates a \"pre-typed\" version of {@linkcode createSelector createSelector}\r\n   * where the `state` type is predefined.\r\n   *\r\n   * This allows you to set the `state` type once, eliminating the need to\r\n   * specify it with every {@linkcode createSelector createSelector} call.\r\n   *\r\n   * @returns A pre-typed `createSelector` with the state type already defined.\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * import { createSelector } from 'reselect'\r\n   *\r\n   * export interface RootState {\r\n   *   todos: { id: number; completed: boolean }[]\r\n   *   alerts: { id: number; read: boolean }[]\r\n   * }\r\n   *\r\n   * export const createAppSelector = createSelector.withTypes<RootState>()\r\n   *\r\n   * const selectTodoIds = createAppSelector(\r\n   *   [\r\n   *     // Type of `state` is set to `RootState`, no need to manually set the type\r\n   *     state => state.todos\r\n   *   ],\r\n   *   todos => todos.map(({ id }) => id)\r\n   * )\r\n   * ```\r\n   * @template OverrideStateType - The specific type of state used by all selectors created with this selector creator.\r\n   *\r\n   * @see {@link https://reselect.js.org/api/createselector#defining-a-pre-typed-createselector `createSelector.withTypes`}\r\n   *\r\n   * @since 5.1.0\r\n   */\r\n  withTypes: <OverrideStateType extends StateType>() => CreateSelectorFunction<\r\n    MemoizeFunction,\r\n    ArgsMemoizeFunction,\r\n    OverrideStateType\r\n  >\r\n}\r\n\r\n/**\r\n * Creates a selector creator function with the specified memoization function\r\n * and options for customizing memoization behavior.\r\n *\r\n * @param options - An options object containing the `memoize` function responsible for memoizing the `resultFunc` inside `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`). It also provides additional options for customizing memoization. While the `memoize` property is mandatory, the rest are optional.\r\n * @returns A customized `createSelector` function.\r\n *\r\n * @example\r\n * ```ts\r\n * const customCreateSelector = createSelectorCreator({\r\n *   memoize: customMemoize, // Function to be used to memoize `resultFunc`\r\n *   memoizeOptions: [memoizeOption1, memoizeOption2], // Options passed to `customMemoize` as the second argument onwards\r\n *   argsMemoize: customArgsMemoize, // Function to be used to memoize the selector's arguments\r\n *   argsMemoizeOptions: [argsMemoizeOption1, argsMemoizeOption2] // Options passed to `customArgsMemoize` as the second argument onwards\r\n * })\r\n *\r\n * const customSelector = customCreateSelector(\r\n *   [inputSelector1, inputSelector2],\r\n *   resultFunc // `resultFunc` will be passed as the first argument to `customMemoize`\r\n * )\r\n *\r\n * customSelector(\r\n *   ...selectorArgs // Will be memoized by `customArgsMemoize`\r\n * )\r\n * ```\r\n *\r\n * @template MemoizeFunction - The type of the memoize function that is used to memoize the `resultFunc` inside `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`).\r\n * @template ArgsMemoizeFunction - The type of the optional memoize function that is used to memoize the arguments passed into the output selector generated by `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`). If none is explicitly provided, `weakMapMemoize` will be used.\r\n *\r\n * @see {@link https://reselect.js.org/api/createSelectorCreator#using-options-since-500 `createSelectorCreator`}\r\n *\r\n * @since 5.0.0\r\n * @public\r\n */\r\nexport function createSelectorCreator<\r\n  MemoizeFunction extends UnknownMemoizer,\r\n  ArgsMemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize\r\n>(\r\n  options: Simplify<\r\n    SetRequired<\r\n      CreateSelectorOptions<\r\n        typeof weakMapMemoize,\r\n        typeof weakMapMemoize,\r\n        MemoizeFunction,\r\n        ArgsMemoizeFunction\r\n      >,\r\n      'memoize'\r\n    >\r\n  >\r\n): CreateSelectorFunction<MemoizeFunction, ArgsMemoizeFunction>\r\n\r\n/**\r\n * Creates a selector creator function with the specified memoization function\r\n * and options for customizing memoization behavior.\r\n *\r\n * @param memoize - The `memoize` function responsible for memoizing the `resultFunc` inside `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`).\r\n * @param memoizeOptionsFromArgs - Optional configuration options for the memoization function. These options are then passed to the memoize function as the second argument onwards.\r\n * @returns A customized `createSelector` function.\r\n *\r\n * @example\r\n * ```ts\r\n * const customCreateSelector = createSelectorCreator(customMemoize, // Function to be used to memoize `resultFunc`\r\n *   option1, // Will be passed as second argument to `customMemoize`\r\n *   option2, // Will be passed as third argument to `customMemoize`\r\n *   option3 // Will be passed as fourth argument to `customMemoize`\r\n * )\r\n *\r\n * const customSelector = customCreateSelector(\r\n *   [inputSelector1, inputSelector2],\r\n *   resultFunc // `resultFunc` will be passed as the first argument to `customMemoize`\r\n * )\r\n * ```\r\n *\r\n * @template MemoizeFunction - The type of the memoize function that is used to memoize the `resultFunc` inside `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`).\r\n *\r\n * @see {@link https://reselect.js.org/api/createSelectorCreator#using-memoize-and-memoizeoptions `createSelectorCreator`}\r\n *\r\n * @public\r\n */\r\nexport function createSelectorCreator<MemoizeFunction extends UnknownMemoizer>(\r\n  memoize: MemoizeFunction,\r\n  ...memoizeOptionsFromArgs: DropFirstParameter<MemoizeFunction>\r\n): CreateSelectorFunction<MemoizeFunction>\r\n\r\n/**\r\n * Creates a selector creator function with the specified memoization\r\n * function and options for customizing memoization behavior.\r\n *\r\n * @param memoizeOrOptions - Either A `memoize` function or an `options` object containing the `memoize` function.\r\n * @param memoizeOptionsFromArgs - Optional configuration options for the memoization function. These options are then passed to the memoize function as the second argument onwards.\r\n * @returns A customized `createSelector` function.\r\n *\r\n * @template MemoizeFunction - The type of the memoize function that is used to memoize the `resultFunc` inside `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`).\r\n * @template ArgsMemoizeFunction - The type of the optional memoize function that is used to memoize the arguments passed into the output selector generated by `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`). If none is explicitly provided, `weakMapMemoize` will be used.\r\n * @template MemoizeOrOptions - The type of the first argument. It can either be a `memoize` function or an `options` object containing the `memoize` function.\r\n */\r\nexport function createSelectorCreator<\r\n  MemoizeFunction extends UnknownMemoizer,\r\n  ArgsMemoizeFunction extends UnknownMemoizer,\r\n  MemoizeOrOptions extends\r\n    | MemoizeFunction\r\n    | SetRequired<\r\n        CreateSelectorOptions<MemoizeFunction, ArgsMemoizeFunction>,\r\n        'memoize'\r\n      >\r\n>(\r\n  memoizeOrOptions: MemoizeOrOptions,\r\n  ...memoizeOptionsFromArgs: MemoizeOrOptions extends SetRequired<\r\n    CreateSelectorOptions<MemoizeFunction, ArgsMemoizeFunction>,\r\n    'memoize'\r\n  >\r\n    ? never\r\n    : DropFirstParameter<MemoizeFunction>\r\n) {\r\n  /** options initially passed into `createSelectorCreator`. */\r\n  const createSelectorCreatorOptions: SetRequired<\r\n    CreateSelectorOptions<MemoizeFunction, ArgsMemoizeFunction>,\r\n    'memoize'\r\n  > = typeof memoizeOrOptions === 'function'\r\n    ? {\r\n        memoize: memoizeOrOptions as MemoizeFunction,\r\n        memoizeOptions: memoizeOptionsFromArgs\r\n      }\r\n    : memoizeOrOptions\r\n\r\n  const createSelector = <\r\n    InputSelectors extends SelectorArray,\r\n    Result,\r\n    OverrideMemoizeFunction extends UnknownMemoizer = MemoizeFunction,\r\n    OverrideArgsMemoizeFunction extends UnknownMemoizer = ArgsMemoizeFunction\r\n  >(\r\n    ...createSelectorArgs: [\r\n      ...inputSelectors: [...InputSelectors],\r\n      combiner: Combiner<InputSelectors, Result>,\r\n      createSelectorOptions?: CreateSelectorOptions<\r\n        MemoizeFunction,\r\n        ArgsMemoizeFunction,\r\n        OverrideMemoizeFunction,\r\n        OverrideArgsMemoizeFunction\r\n      >\r\n    ]\r\n  ) => {\r\n    let recomputations = 0\r\n    let dependencyRecomputations = 0\r\n    let lastResult: Result\r\n\r\n    // Due to the intricacies of rest params, we can't do an optional arg after `...createSelectorArgs`.\r\n    // So, start by declaring the default value here.\r\n    // (And yes, the words 'memoize' and 'options' appear too many times in this next sequence.)\r\n    let directlyPassedOptions: CreateSelectorOptions<\r\n      MemoizeFunction,\r\n      ArgsMemoizeFunction,\r\n      OverrideMemoizeFunction,\r\n      OverrideArgsMemoizeFunction\r\n    > = {}\r\n\r\n    // Normally, the result func or \"combiner\" is the last arg\r\n    let resultFunc = createSelectorArgs.pop() as\r\n      | Combiner<InputSelectors, Result>\r\n      | CreateSelectorOptions<\r\n          MemoizeFunction,\r\n          ArgsMemoizeFunction,\r\n          OverrideMemoizeFunction,\r\n          OverrideArgsMemoizeFunction\r\n        >\r\n\r\n    // If the result func is actually an _object_, assume it's our options object\r\n    if (typeof resultFunc === 'object') {\r\n      directlyPassedOptions = resultFunc\r\n      // and pop the real result func off\r\n      resultFunc = createSelectorArgs.pop() as Combiner<InputSelectors, Result>\r\n    }\r\n\r\n    assertIsFunction(\r\n      resultFunc,\r\n      `createSelector expects an output function after the inputs, but received: [${typeof resultFunc}]`\r\n    )\r\n\r\n    // Determine which set of options we're using. Prefer options passed directly,\r\n    // but fall back to options given to `createSelectorCreator`.\r\n    const combinedOptions = {\r\n      ...createSelectorCreatorOptions,\r\n      ...directlyPassedOptions\r\n    }\r\n\r\n    const {\r\n      memoize,\r\n      memoizeOptions = [],\r\n      argsMemoize = weakMapMemoize,\r\n      argsMemoizeOptions = [],\r\n      devModeChecks = {}\r\n    } = combinedOptions\r\n\r\n    // Simplifying assumption: it's unlikely that the first options arg of the provided memoizer\r\n    // is an array. In most libs I've looked at, it's an equality function or options object.\r\n    // Based on that, if `memoizeOptions` _is_ an array, we assume it's a full\r\n    // user-provided array of options. Otherwise, it must be just the _first_ arg, and so\r\n    // we wrap it in an array so we can apply it.\r\n    const finalMemoizeOptions = ensureIsArray(memoizeOptions)\r\n    const finalArgsMemoizeOptions = ensureIsArray(argsMemoizeOptions)\r\n    const dependencies = getDependencies(createSelectorArgs) as InputSelectors\r\n\r\n    const memoizedResultFunc = memoize(function recomputationWrapper() {\r\n      recomputations++\r\n      // apply arguments instead of spreading for performance.\r\n      // @ts-ignore\r\n      return (resultFunc as Combiner<InputSelectors, Result>).apply(\r\n        null,\r\n        arguments as unknown as Parameters<Combiner<InputSelectors, Result>>\r\n      )\r\n    }, ...finalMemoizeOptions) as Combiner<InputSelectors, Result> &\r\n      ExtractMemoizerFields<OverrideMemoizeFunction>\r\n\r\n    let firstRun = true\r\n\r\n    // If a selector is called with the exact same arguments we don't need to traverse our dependencies again.\r\n    const selector = argsMemoize(function dependenciesChecker() {\r\n      dependencyRecomputations++\r\n      /** Return values of input selectors which the `resultFunc` takes as arguments. */\r\n      const inputSelectorResults = collectInputSelectorResults(\r\n        dependencies,\r\n        arguments\r\n      )\r\n\r\n      // apply arguments instead of spreading for performance.\r\n      // @ts-ignore\r\n      lastResult = memoizedResultFunc.apply(null, inputSelectorResults)\r\n\r\n      if (process.env.NODE_ENV !== 'production') {\r\n        const { identityFunctionCheck, inputStabilityCheck } =\r\n          getDevModeChecksExecutionInfo(firstRun, devModeChecks)\r\n        if (identityFunctionCheck.shouldRun) {\r\n          identityFunctionCheck.run(\r\n            resultFunc as Combiner<InputSelectors, Result>,\r\n            inputSelectorResults,\r\n            lastResult\r\n          )\r\n        }\r\n\r\n        if (inputStabilityCheck.shouldRun) {\r\n          // make a second copy of the params, to check if we got the same results\r\n          const inputSelectorResultsCopy = collectInputSelectorResults(\r\n            dependencies,\r\n            arguments\r\n          )\r\n\r\n          inputStabilityCheck.run(\r\n            { inputSelectorResults, inputSelectorResultsCopy },\r\n            { memoize, memoizeOptions: finalMemoizeOptions },\r\n            arguments\r\n          )\r\n        }\r\n\r\n        if (firstRun) firstRun = false\r\n      }\r\n\r\n      return lastResult\r\n    }, ...finalArgsMemoizeOptions) as unknown as Selector<\r\n      GetStateFromSelectors<InputSelectors>,\r\n      Result,\r\n      GetParamsFromSelectors<InputSelectors>\r\n    > &\r\n      ExtractMemoizerFields<OverrideArgsMemoizeFunction>\r\n\r\n    return Object.assign(selector, {\r\n      resultFunc,\r\n      memoizedResultFunc,\r\n      dependencies,\r\n      dependencyRecomputations: () => dependencyRecomputations,\r\n      resetDependencyRecomputations: () => {\r\n        dependencyRecomputations = 0\r\n      },\r\n      lastResult: () => lastResult,\r\n      recomputations: () => recomputations,\r\n      resetRecomputations: () => {\r\n        recomputations = 0\r\n      },\r\n      memoize,\r\n      argsMemoize\r\n    }) as OutputSelector<\r\n      InputSelectors,\r\n      Result,\r\n      OverrideMemoizeFunction,\r\n      OverrideArgsMemoizeFunction\r\n    >\r\n  }\r\n\r\n  Object.assign(createSelector, {\r\n    withTypes: () => createSelector\r\n  })\r\n\r\n  return createSelector as CreateSelectorFunction<\r\n    MemoizeFunction,\r\n    ArgsMemoizeFunction\r\n  >\r\n}\r\n\r\n/**\r\n * Accepts one or more \"input selectors\" (either as separate arguments or a single array),\r\n * a single \"result function\" / \"combiner\", and an optional options object, and\r\n * generates a memoized selector function.\r\n *\r\n * @see {@link https://reselect.js.org/api/createSelector `createSelector`}\r\n *\r\n * @public\r\n */\r\nexport const createSelector =\r\n  /* #__PURE__ */ createSelectorCreator(weakMapMemoize)\r\n", "import { createSelector } from './createSelectorCreator'\r\n\r\nimport type { CreateSelectorFunction } from './createSelectorCreator'\r\nimport type {\r\n  InterruptRecursion,\r\n  ObjectValuesToTuple,\r\n  OutputSelector,\r\n  Selector,\r\n  Simplify,\r\n  UnknownMemoizer\r\n} from './types'\r\nimport { assertIsObject } from './utils'\r\nimport type { weakMapMemoize } from './weakMapMemoize'\r\n\r\n/**\r\n * Represents a mapping of selectors to their return types.\r\n *\r\n * @template TObject - An object type where each property is a selector function.\r\n *\r\n * @public\r\n */\r\nexport type SelectorResultsMap<TObject extends SelectorsObject> = {\r\n  [Key in keyof TObject]: ReturnType<TObject[Key]>\r\n}\r\n\r\n/**\r\n * Represents a mapping of selectors for each key in a given root state.\r\n *\r\n * This type is a utility that takes a root state object type and\r\n * generates a corresponding set of selectors. Each selector is associated\r\n * with a key in the root state, allowing for the selection\r\n * of specific parts of the state.\r\n *\r\n * @template RootState - The type of the root state object.\r\n *\r\n * @since 5.0.0\r\n * @public\r\n */\r\nexport type RootStateSelectors<RootState = any> = {\r\n  [Key in keyof RootState]: Selector<RootState, RootState[Key], []>\r\n}\r\n\r\n/**\r\n * @deprecated Please use {@linkcode StructuredSelectorCreator.withTypes createStructuredSelector.withTypes<RootState>()} instead. This type will be removed in the future.\r\n * @template RootState - The type of the root state object.\r\n *\r\n * @since 5.0.0\r\n * @public\r\n */\r\nexport type TypedStructuredSelectorCreator<RootState = any> =\r\n  /**\r\n   * A convenience function that simplifies returning an object\r\n   * made up of selector results.\r\n   *\r\n   * @param inputSelectorsObject - A key value pair consisting of input selectors.\r\n   * @param selectorCreator - A custom selector creator function. It defaults to `createSelector`.\r\n   * @returns A memoized structured selector.\r\n   *\r\n   * @example\r\n   * <caption>Modern Use Case</caption>\r\n   * ```ts\r\n   * import { createSelector, createStructuredSelector } from 'reselect'\r\n   *\r\n   * interface RootState {\r\n   *   todos: {\r\n   *     id: number\r\n   *     completed: boolean\r\n   *     title: string\r\n   *     description: string\r\n   *   }[]\r\n   *   alerts: { id: number; read: boolean }[]\r\n   * }\r\n   *\r\n   * // This:\r\n   * const structuredSelector = createStructuredSelector(\r\n   *   {\r\n   *     todos: (state: RootState) => state.todos,\r\n   *     alerts: (state: RootState) => state.alerts,\r\n   *     todoById: (state: RootState, id: number) => state.todos[id]\r\n   *   },\r\n   *   createSelector\r\n   * )\r\n   *\r\n   * // Is essentially the same as this:\r\n   * const selector = createSelector(\r\n   *   [\r\n   *     (state: RootState) => state.todos,\r\n   *     (state: RootState) => state.alerts,\r\n   *     (state: RootState, id: number) => state.todos[id]\r\n   *   ],\r\n   *   (todos, alerts, todoById) => {\r\n   *     return {\r\n   *       todos,\r\n   *       alerts,\r\n   *       todoById\r\n   *     }\r\n   *   }\r\n   * )\r\n   * ```\r\n   *\r\n   * @example\r\n   * <caption>In your component:</caption>\r\n   * ```tsx\r\n   * import type { RootState } from 'createStructuredSelector/modernUseCase'\r\n   * import { structuredSelector } from 'createStructuredSelector/modernUseCase'\r\n   * import type { FC } from 'react'\r\n   * import { useSelector } from 'react-redux'\r\n   *\r\n   * interface Props {\r\n   *   id: number\r\n   * }\r\n   *\r\n   * const MyComponent: FC<Props> = ({ id }) => {\r\n   *   const { todos, alerts, todoById } = useSelector((state: RootState) =>\r\n   *     structuredSelector(state, id)\r\n   *   )\r\n   *\r\n   *   return (\r\n   *     <div>\r\n   *       Next to do is:\r\n   *       <h2>{todoById.title}</h2>\r\n   *       <p>Description: {todoById.description}</p>\r\n   *       <ul>\r\n   *         <h3>All other to dos:</h3>\r\n   *         {todos.map(todo => (\r\n   *           <li key={todo.id}>{todo.title}</li>\r\n   *         ))}\r\n   *       </ul>\r\n   *     </div>\r\n   *   )\r\n   * }\r\n   * ```\r\n   *\r\n   * @example\r\n   * <caption>Simple Use Case</caption>\r\n   * ```ts\r\n   * const selectA = state => state.a\r\n   * const selectB = state => state.b\r\n   *\r\n   * // The result function in the following selector\r\n   * // is simply building an object from the input selectors\r\n   * const structuredSelector = createSelector(selectA, selectB, (a, b) => ({\r\n   *   a,\r\n   *   b\r\n   * }))\r\n   *\r\n   * const result = structuredSelector({ a: 1, b: 2 }) // will produce { x: 1, y: 2 }\r\n   * ```\r\n   *\r\n   * @template InputSelectorsObject - The shape of the input selectors object.\r\n   * @template MemoizeFunction - The type of the memoize function that is used to create the structured selector. It defaults to `weakMapMemoize`.\r\n   * @template ArgsMemoizeFunction - The type of the of the memoize function that is used to memoize the arguments passed into the generated structured selector. It defaults to `weakMapMemoize`.\r\n   *\r\n   * @see {@link https://reselect.js.org/api/createStructuredSelector `createStructuredSelector`}\r\n   */\r\n  <\r\n    InputSelectorsObject extends RootStateSelectors<RootState> = RootStateSelectors<RootState>,\r\n    MemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize,\r\n    ArgsMemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize\r\n  >(\r\n    inputSelectorsObject: InputSelectorsObject,\r\n    selectorCreator?: CreateSelectorFunction<\r\n      MemoizeFunction,\r\n      ArgsMemoizeFunction\r\n    >\r\n  ) => OutputSelector<\r\n    ObjectValuesToTuple<InputSelectorsObject>,\r\n    Simplify<SelectorResultsMap<InputSelectorsObject>>,\r\n    MemoizeFunction,\r\n    ArgsMemoizeFunction\r\n  > &\r\n    InterruptRecursion\r\n\r\n/**\r\n * Represents an object where each property is a selector function.\r\n *\r\n * @template StateType - The type of state that all the selectors operate on.\r\n *\r\n * @public\r\n */\r\nexport type SelectorsObject<StateType = any> = Record<\r\n  string,\r\n  Selector<StateType>\r\n>\r\n\r\n/**\r\n * It provides a way to create structured selectors.\r\n * The structured selector can take multiple input selectors\r\n * and map their output to an object with specific keys.\r\n *\r\n * @template StateType - The type of state that the structured selectors created with this structured selector creator will operate on.\r\n *\r\n * @see {@link https://reselect.js.org/api/createStructuredSelector `createStructuredSelector`}\r\n *\r\n * @public\r\n */\r\nexport interface StructuredSelectorCreator<StateType = any> {\r\n  /**\r\n   * A convenience function that simplifies returning an object\r\n   * made up of selector results.\r\n   *\r\n   * @param inputSelectorsObject - A key value pair consisting of input selectors.\r\n   * @param selectorCreator - A custom selector creator function. It defaults to `createSelector`.\r\n   * @returns A memoized structured selector.\r\n   *\r\n   * @example\r\n   * <caption>Modern Use Case</caption>\r\n   * ```ts\r\n   * import { createSelector, createStructuredSelector } from 'reselect'\r\n   *\r\n   * interface RootState {\r\n   *   todos: {\r\n   *     id: number\r\n   *     completed: boolean\r\n   *     title: string\r\n   *     description: string\r\n   *   }[]\r\n   *   alerts: { id: number; read: boolean }[]\r\n   * }\r\n   *\r\n   * // This:\r\n   * const structuredSelector = createStructuredSelector(\r\n   *   {\r\n   *     todos: (state: RootState) => state.todos,\r\n   *     alerts: (state: RootState) => state.alerts,\r\n   *     todoById: (state: RootState, id: number) => state.todos[id]\r\n   *   },\r\n   *   createSelector\r\n   * )\r\n   *\r\n   * // Is essentially the same as this:\r\n   * const selector = createSelector(\r\n   *   [\r\n   *     (state: RootState) => state.todos,\r\n   *     (state: RootState) => state.alerts,\r\n   *     (state: RootState, id: number) => state.todos[id]\r\n   *   ],\r\n   *   (todos, alerts, todoById) => {\r\n   *     return {\r\n   *       todos,\r\n   *       alerts,\r\n   *       todoById\r\n   *     }\r\n   *   }\r\n   * )\r\n   * ```\r\n   *\r\n   * @example\r\n   * <caption>In your component:</caption>\r\n   * ```tsx\r\n   * import type { RootState } from 'createStructuredSelector/modernUseCase'\r\n   * import { structuredSelector } from 'createStructuredSelector/modernUseCase'\r\n   * import type { FC } from 'react'\r\n   * import { useSelector } from 'react-redux'\r\n   *\r\n   * interface Props {\r\n   *   id: number\r\n   * }\r\n   *\r\n   * const MyComponent: FC<Props> = ({ id }) => {\r\n   *   const { todos, alerts, todoById } = useSelector((state: RootState) =>\r\n   *     structuredSelector(state, id)\r\n   *   )\r\n   *\r\n   *   return (\r\n   *     <div>\r\n   *       Next to do is:\r\n   *       <h2>{todoById.title}</h2>\r\n   *       <p>Description: {todoById.description}</p>\r\n   *       <ul>\r\n   *         <h3>All other to dos:</h3>\r\n   *         {todos.map(todo => (\r\n   *           <li key={todo.id}>{todo.title}</li>\r\n   *         ))}\r\n   *       </ul>\r\n   *     </div>\r\n   *   )\r\n   * }\r\n   * ```\r\n   *\r\n   * @example\r\n   * <caption>Simple Use Case</caption>\r\n   * ```ts\r\n   * const selectA = state => state.a\r\n   * const selectB = state => state.b\r\n   *\r\n   * // The result function in the following selector\r\n   * // is simply building an object from the input selectors\r\n   * const structuredSelector = createSelector(selectA, selectB, (a, b) => ({\r\n   *   a,\r\n   *   b\r\n   * }))\r\n   *\r\n   * const result = structuredSelector({ a: 1, b: 2 }) // will produce { x: 1, y: 2 }\r\n   * ```\r\n   *\r\n   * @template InputSelectorsObject - The shape of the input selectors object.\r\n   * @template MemoizeFunction - The type of the memoize function that is used to create the structured selector. It defaults to `weakMapMemoize`.\r\n   * @template ArgsMemoizeFunction - The type of the of the memoize function that is used to memoize the arguments passed into the generated structured selector. It defaults to `weakMapMemoize`.\r\n   *\r\n   * @see {@link https://reselect.js.org/api/createStructuredSelector `createStructuredSelector`}\r\n   */\r\n  <\r\n    InputSelectorsObject extends SelectorsObject<StateType>,\r\n    MemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize,\r\n    ArgsMemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize\r\n  >(\r\n    inputSelectorsObject: InputSelectorsObject,\r\n    selectorCreator?: CreateSelectorFunction<\r\n      MemoizeFunction,\r\n      ArgsMemoizeFunction\r\n    >\r\n  ): OutputSelector<\r\n    ObjectValuesToTuple<InputSelectorsObject>,\r\n    Simplify<SelectorResultsMap<InputSelectorsObject>>,\r\n    MemoizeFunction,\r\n    ArgsMemoizeFunction\r\n  > &\r\n    InterruptRecursion\r\n\r\n  /**\r\n   * Creates a \"pre-typed\" version of\r\n   * {@linkcode createStructuredSelector createStructuredSelector}\r\n   * where the `state` type is predefined.\r\n   *\r\n   * This allows you to set the `state` type once, eliminating the need to\r\n   * specify it with every\r\n   * {@linkcode createStructuredSelector createStructuredSelector} call.\r\n   *\r\n   * @returns A pre-typed `createStructuredSelector` with the state type already defined.\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * import { createStructuredSelector } from 'reselect'\r\n   *\r\n   * export interface RootState {\r\n   *   todos: { id: number; completed: boolean }[]\r\n   *   alerts: { id: number; read: boolean }[]\r\n   * }\r\n   *\r\n   * export const createStructuredAppSelector =\r\n   *   createStructuredSelector.withTypes<RootState>()\r\n   *\r\n   * const structuredAppSelector = createStructuredAppSelector({\r\n   *   // Type of `state` is set to `RootState`, no need to manually set the type\r\n   *   todos: state => state.todos,\r\n   *   alerts: state => state.alerts,\r\n   *   todoById: (state, id: number) => state.todos[id]\r\n   * })\r\n   *\r\n   * ```\r\n   * @template OverrideStateType - The specific type of state used by all structured selectors created with this structured selector creator.\r\n   *\r\n   * @see {@link https://reselect.js.org/api/createstructuredselector#defining-a-pre-typed-createstructuredselector `createSelector.withTypes`}\r\n   *\r\n   * @since 5.1.0\r\n   */\r\n  withTypes: <\r\n    OverrideStateType extends StateType\r\n  >() => StructuredSelectorCreator<OverrideStateType>\r\n}\r\n\r\n/**\r\n * A convenience function that simplifies returning an object\r\n * made up of selector results.\r\n *\r\n * @param inputSelectorsObject - A key value pair consisting of input selectors.\r\n * @param selectorCreator - A custom selector creator function. It defaults to `createSelector`.\r\n * @returns A memoized structured selector.\r\n *\r\n * @example\r\n * <caption>Modern Use Case</caption>\r\n * ```ts\r\n * import { createSelector, createStructuredSelector } from 'reselect'\r\n *\r\n * interface RootState {\r\n *   todos: {\r\n *     id: number\r\n *     completed: boolean\r\n *     title: string\r\n *     description: string\r\n *   }[]\r\n *   alerts: { id: number; read: boolean }[]\r\n * }\r\n *\r\n * // This:\r\n * const structuredSelector = createStructuredSelector(\r\n *   {\r\n *     todos: (state: RootState) => state.todos,\r\n *     alerts: (state: RootState) => state.alerts,\r\n *     todoById: (state: RootState, id: number) => state.todos[id]\r\n *   },\r\n *   createSelector\r\n * )\r\n *\r\n * // Is essentially the same as this:\r\n * const selector = createSelector(\r\n *   [\r\n *     (state: RootState) => state.todos,\r\n *     (state: RootState) => state.alerts,\r\n *     (state: RootState, id: number) => state.todos[id]\r\n *   ],\r\n *   (todos, alerts, todoById) => {\r\n *     return {\r\n *       todos,\r\n *       alerts,\r\n *       todoById\r\n *     }\r\n *   }\r\n * )\r\n * ```\r\n *\r\n * @see {@link https://reselect.js.org/api/createStructuredSelector `createStructuredSelector`}\r\n *\r\n * @public\r\n */\r\nexport const createStructuredSelector: StructuredSelectorCreator =\r\n  Object.assign(\r\n    <\r\n      InputSelectorsObject extends SelectorsObject,\r\n      MemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize,\r\n      ArgsMemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize\r\n    >(\r\n      inputSelectorsObject: InputSelectorsObject,\r\n      selectorCreator: CreateSelectorFunction<\r\n        MemoizeFunction,\r\n        ArgsMemoizeFunction\r\n      > = createSelector as CreateSelectorFunction<\r\n        MemoizeFunction,\r\n        ArgsMemoizeFunction\r\n      >\r\n    ) => {\r\n      assertIsObject(\r\n        inputSelectorsObject,\r\n        'createStructuredSelector expects first argument to be an object ' +\r\n          `where each property is a selector, instead received a ${typeof inputSelectorsObject}`\r\n      )\r\n      const inputSelectorKeys = Object.keys(inputSelectorsObject)\r\n      const dependencies = inputSelectorKeys.map(\r\n        key => inputSelectorsObject[key]\r\n      )\r\n      const structuredSelector = selectorCreator(\r\n        dependencies,\r\n        (...inputSelectorResults: any[]) => {\r\n          return inputSelectorResults.reduce((composition, value, index) => {\r\n            composition[inputSelectorKeys[index]] = value\r\n            return composition\r\n          }, {})\r\n        }\r\n      )\r\n      return structuredSelector\r\n    },\r\n    { withTypes: () => createStructuredSelector }\r\n  ) as StructuredSelectorCreator\r\n", "// src/index.ts\nfunction createThunkMiddleware(extraArgument) {\n  const middleware = ({ dispatch, getState }) => (next) => (action) => {\n    if (typeof action === \"function\") {\n      return action(dispatch, getState, extraArgument);\n    }\n    return next(action);\n  };\n  return middleware;\n}\nvar thunk = createThunkMiddleware();\nvar withExtraArgument = createThunkMiddleware;\nexport {\n  thunk,\n  withExtraArgument\n};\n", "// This must remain here so that the `mangleErrors.cjs` build script\n// does not have to import this into each source file it rewrites.\nimport { formatProdErrorMessage } from './formatProdErrorMessage';\nexport * from 'redux';\nexport { produce as createNextState, current, freeze, original, isDraft } from 'immer';\nexport type { Draft } from 'immer';\nexport { createSelector, createSelectorCreator, lruMemoize, weakMapMemoize } from 'reselect';\nexport type { Selector, OutputSelector } from 'reselect';\nexport { createDraftSafeSelector, createDraftSafeSelectorCreator } from './createDraftSafeSelector';\nexport type { ThunkAction, ThunkDispatch, ThunkMiddleware } from 'redux-thunk';\nexport {\n// js\nconfigureStore } from './configureStore';\nexport type {\n// types\nConfigureStoreOptions, EnhancedStore } from './configureStore';\nexport type { DevToolsEnhancerOptions } from './devtoolsExtension';\nexport {\n// js\ncreateAction, isActionCreator, isFSA as isFluxStandardAction } from './createAction';\nexport type {\n// types\nPayloadAction, PayloadActionCreator, ActionCreatorWithNonInferrablePayload, ActionCreatorWithOptionalPayload, ActionCreatorWithPayload, ActionCreatorWithoutPayload, ActionCreatorWithPreparedPayload, PrepareAction } from './createAction';\nexport {\n// js\ncreateReducer } from './createReducer';\nexport type {\n// types\nActions, CaseReducer, CaseReducers } from './createReducer';\nexport {\n// js\ncreateSlice, buildCreateSlice, asyncThunkCreator, ReducerType } from './createSlice';\nexport type {\n// types\nCreateSliceOptions, Slice, CaseReducerActions, SliceCaseReducers, ValidateSliceCaseReducers, CaseReducerWithPrepare, ReducerCreators, SliceSelectors } from './createSlice';\nexport type { ActionCreatorInvariantMiddlewareOptions } from './actionCreatorInvariantMiddleware';\nexport { createActionCreatorInvariantMiddleware } from './actionCreatorInvariantMiddleware';\nexport {\n// js\ncreateImmutableStateInvariantMiddleware, isImmutableDefault } from './immutableStateInvariantMiddleware';\nexport type {\n// types\nImmutableStateInvariantMiddlewareOptions } from './immutableStateInvariantMiddleware';\nexport {\n// js\ncreateSerializableStateInvariantMiddleware, findNonSerializableValue, isPlain } from './serializableStateInvariantMiddleware';\nexport type {\n// types\nSerializableStateInvariantMiddlewareOptions } from './serializableStateInvariantMiddleware';\nexport type {\n// types\nActionReducerMapBuilder } from './mapBuilders';\nexport { Tuple } from './utils';\nexport { createEntityAdapter } from './entities/create_adapter';\nexport type { EntityState, EntityAdapter, EntitySelectors, EntityStateAdapter, EntityId, Update, IdSelector, Comparer } from './entities/models';\nexport { createAsyncThunk, unwrapResult, miniSerializeError } from './createAsyncThunk';\nexport type { AsyncThunk, AsyncThunkOptions, AsyncThunkAction, AsyncThunkPayloadCreatorReturnValue, AsyncThunkPayloadCreator, GetState, GetThunkAPI, SerializedError, CreateAsyncThunkFunction } from './createAsyncThunk';\nexport {\n// js\nisAllOf, isAnyOf, isPending, isRejected, isFulfilled, isAsyncThunkAction, isRejectedWithValue } from './matchers';\nexport type {\n// types\nActionMatchingAllOf, ActionMatchingAnyOf } from './matchers';\nexport { nanoid } from './nanoid';\nexport type { ListenerEffect, ListenerMiddleware, ListenerEffectAPI, ListenerMiddlewareInstance, CreateListenerMiddlewareOptions, ListenerErrorHandler, TypedStartListening, TypedAddListener, TypedStopListening, TypedRemoveListener, UnsubscribeListener, UnsubscribeListenerOptions, ForkedTaskExecutor, ForkedTask, ForkedTaskAPI, AsyncTaskExecutor, SyncTaskExecutor, TaskCancelled, TaskRejected, TaskResolved, TaskResult } from './listenerMiddleware/index';\nexport type { AnyListenerPredicate } from './listenerMiddleware/types';\nexport { createListenerMiddleware, addListener, removeListener, clearAllListeners, TaskAbortError } from './listenerMiddleware/index';\nexport type { AddMiddleware, DynamicDispatch, DynamicMiddlewareInstance, GetDispatchType as GetDispatch, MiddlewareApiConfig } from './dynamicMiddleware/types';\nexport { createDynamicMiddleware } from './dynamicMiddleware/index';\nexport { SHOULD_AUTOBATCH, prepareAutoBatched, autoBatchEnhancer } from './autoBatchEnhancer';\nexport type { AutoBatchOptions } from './autoBatchEnhancer';\nexport { combineSlices } from './combineSlices';\nexport type { CombinedSliceReducer, WithSlice } from './combineSlices';\nexport type { ExtractDispatchExtensions as TSHelpersExtractDispatchExtensions, SafePromise } from './tsHelpers';\nexport { formatProdErrorMessage } from './formatProdErrorMessage';", "import { current, isDraft } from 'immer';\nimport { createSelectorCreator, weakMapMemoize } from 'reselect';\nexport const createDraftSafeSelectorCreator: typeof createSelectorCreator = (...args: unknown[]) => {\n  const createSelector = (createSelectorCreator as any)(...args);\n  const createDraftSafeSelector = Object.assign((...args: unknown[]) => {\n    const selector = createSelector(...args);\n    const wrappedSelector = (value: unknown, ...rest: unknown[]) => selector(isDraft(value) ? current(value) : value, ...rest);\n    Object.assign(wrappedSelector, selector);\n    return wrappedSelector as any;\n  }, {\n    withTypes: () => createDraftSafeSelector\n  });\n  return createDraftSafeSelector;\n};\n\n/**\n * \"Draft-Safe\" version of `reselect`'s `createSelector`:\n * If an `immer`-drafted object is passed into the resulting selector's first argument,\n * the selector will act on the current draft value, instead of returning a cached value\n * that might be possibly outdated if the draft has been modified since.\n * @public\n */\nexport const createDraftSafeSelector = /* @__PURE__ */\ncreateDraftSafeSelectorCreator(weakMapMemoize);", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2, formatProdErrorMessage as _formatProdErrorMessage3, formatProdErrorMessage as _formatProdErrorMessage4, formatProdErrorMessage as _formatProdErrorMessage5, formatProdErrorMessage as _formatProdErrorMessage6, formatProdErrorMessage as _formatProdErrorMessage7, formatProdErrorMessage as _formatProdErrorMessage8 } from \"@reduxjs/toolkit\";\nimport type { Reducer, ReducersMapObject, Middleware, Action, StoreEnhancer, Store, UnknownAction } from 'redux';\nimport { applyMiddleware, createStore, compose, combineReducers, isPlainObject } from 'redux';\nimport type { DevToolsEnhancerOptions as DevToolsOptions } from './devtoolsExtension';\nimport { composeWithDevTools } from './devtoolsExtension';\nimport type { ThunkMiddlewareFor, GetDefaultMiddleware } from './getDefaultMiddleware';\nimport { buildGetDefaultMiddleware } from './getDefaultMiddleware';\nimport type { ExtractDispatchExtensions, ExtractStoreExtensions, ExtractStateExtensions, UnknownIfNonSpecific } from './tsHelpers';\nimport type { Tuple } from './utils';\nimport type { GetDefaultEnhancers } from './getDefaultEnhancers';\nimport { buildGetDefaultEnhancers } from './getDefaultEnhancers';\n\n/**\n * Options for `configureStore()`.\n *\n * @public\n */\nexport interface ConfigureStoreOptions<S = any, A extends Action = UnknownAction, M extends Tuple<Middlewares<S>> = Tuple<Middlewares<S>>, E extends Tuple<Enhancers> = Tuple<Enhancers>, P = S> {\n  /**\n   * A single reducer function that will be used as the root reducer, or an\n   * object of slice reducers that will be passed to `combineReducers()`.\n   */\n  reducer: Reducer<S, A, P> | ReducersMapObject<S, A, P>;\n\n  /**\n   * An array of Redux middleware to install, or a callback receiving `getDefaultMiddleware` and returning a Tuple of middleware.\n   * If not supplied, defaults to the set of middleware returned by `getDefaultMiddleware()`.\n   *\n   * @example `middleware: (gDM) => gDM().concat(logger, apiMiddleware, yourCustomMiddleware)`\n   * @see https://redux-toolkit.js.org/api/getDefaultMiddleware#intended-usage\n   */\n  middleware?: (getDefaultMiddleware: GetDefaultMiddleware<S>) => M;\n\n  /**\n   * Whether to enable Redux DevTools integration. Defaults to `true`.\n   *\n   * Additional configuration can be done by passing Redux DevTools options\n   */\n  devTools?: boolean | DevToolsOptions;\n\n  /**\n   * Whether to check for duplicate middleware instances. Defaults to `true`.\n   */\n  duplicateMiddlewareCheck?: boolean;\n\n  /**\n   * The initial state, same as Redux's createStore.\n   * You may optionally specify it to hydrate the state\n   * from the server in universal apps, or to restore a previously serialized\n   * user session. If you use `combineReducers()` to produce the root reducer\n   * function (either directly or indirectly by passing an object as `reducer`),\n   * this must be an object with the same shape as the reducer map keys.\n   */\n  // we infer here, and instead complain if the reducer doesn't match\n  preloadedState?: P;\n\n  /**\n   * The store enhancers to apply. See Redux's `createStore()`.\n   * All enhancers will be included before the DevTools Extension enhancer.\n   * If you need to customize the order of enhancers, supply a callback\n   * function that will receive a `getDefaultEnhancers` function that returns a Tuple,\n   * and should return a Tuple of enhancers (such as `getDefaultEnhancers().concat(offline)`).\n   * If you only need to add middleware, you can use the `middleware` parameter instead.\n   */\n  enhancers?: (getDefaultEnhancers: GetDefaultEnhancers<M>) => E;\n}\nexport type Middlewares<S> = ReadonlyArray<Middleware<{}, S>>;\ntype Enhancers = ReadonlyArray<StoreEnhancer>;\n\n/**\n * A Redux store returned by `configureStore()`. Supports dispatching\n * side-effectful _thunks_ in addition to plain actions.\n *\n * @public\n */\nexport type EnhancedStore<S = any, A extends Action = UnknownAction, E extends Enhancers = Enhancers> = ExtractStoreExtensions<E> & Store<S, A, UnknownIfNonSpecific<ExtractStateExtensions<E>>>;\n\n/**\n * A friendly abstraction over the standard Redux `createStore()` function.\n *\n * @param options The store configuration.\n * @returns A configured Redux store.\n *\n * @public\n */\nexport function configureStore<S = any, A extends Action = UnknownAction, M extends Tuple<Middlewares<S>> = Tuple<[ThunkMiddlewareFor<S>]>, E extends Tuple<Enhancers> = Tuple<[StoreEnhancer<{\n  dispatch: ExtractDispatchExtensions<M>;\n}>, StoreEnhancer]>, P = S>(options: ConfigureStoreOptions<S, A, M, E, P>): EnhancedStore<S, A, E> {\n  const getDefaultMiddleware = buildGetDefaultMiddleware<S>();\n  const {\n    reducer = undefined,\n    middleware,\n    devTools = true,\n    duplicateMiddlewareCheck = true,\n    preloadedState = undefined,\n    enhancers = undefined\n  } = options || {};\n  let rootReducer: Reducer<S, A, P>;\n  if (typeof reducer === 'function') {\n    rootReducer = reducer;\n  } else if (isPlainObject(reducer)) {\n    rootReducer = combineReducers(reducer) as unknown as Reducer<S, A, P>;\n  } else {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(1) : '`reducer` is a required argument, and must be a function or an object of functions that can be passed to combineReducers');\n  }\n  if (process.env.NODE_ENV !== 'production' && middleware && typeof middleware !== 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(2) : '`middleware` field must be a callback');\n  }\n  let finalMiddleware: Tuple<Middlewares<S>>;\n  if (typeof middleware === 'function') {\n    finalMiddleware = middleware(getDefaultMiddleware);\n    if (process.env.NODE_ENV !== 'production' && !Array.isArray(finalMiddleware)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(3) : 'when using a middleware builder function, an array of middleware must be returned');\n    }\n  } else {\n    finalMiddleware = getDefaultMiddleware();\n  }\n  if (process.env.NODE_ENV !== 'production' && finalMiddleware.some((item: any) => typeof item !== 'function')) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage4(4) : 'each middleware provided to configureStore must be a function');\n  }\n  if (process.env.NODE_ENV !== 'production' && duplicateMiddlewareCheck) {\n    let middlewareReferences = new Set<Middleware<any, S>>();\n    finalMiddleware.forEach(middleware => {\n      if (middlewareReferences.has(middleware)) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage5(42) : 'Duplicate middleware references found when creating the store. Ensure that each middleware is only included once.');\n      }\n      middlewareReferences.add(middleware);\n    });\n  }\n  let finalCompose = compose;\n  if (devTools) {\n    finalCompose = composeWithDevTools({\n      // Enable capture of stack traces for dispatched Redux actions\n      trace: process.env.NODE_ENV !== 'production',\n      ...(typeof devTools === 'object' && devTools)\n    });\n  }\n  const middlewareEnhancer = applyMiddleware(...finalMiddleware);\n  const getDefaultEnhancers = buildGetDefaultEnhancers<M>(middlewareEnhancer);\n  if (process.env.NODE_ENV !== 'production' && enhancers && typeof enhancers !== 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage6(5) : '`enhancers` field must be a callback');\n  }\n  let storeEnhancers = typeof enhancers === 'function' ? enhancers(getDefaultEnhancers) : getDefaultEnhancers();\n  if (process.env.NODE_ENV !== 'production' && !Array.isArray(storeEnhancers)) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage7(6) : '`enhancers` callback must return an array');\n  }\n  if (process.env.NODE_ENV !== 'production' && storeEnhancers.some((item: any) => typeof item !== 'function')) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage8(7) : 'each enhancer provided to configureStore must be a function');\n  }\n  if (process.env.NODE_ENV !== 'production' && finalMiddleware.length && !storeEnhancers.includes(middlewareEnhancer)) {\n    console.error('middlewares were provided, but middleware enhancer was not included in final enhancers - make sure to call `getDefaultEnhancers`');\n  }\n  const composedEnhancer: StoreEnhancer<any> = finalCompose(...storeEnhancers);\n  return createStore(rootReducer, preloadedState as P, composedEnhancer);\n}", "import type { Action, ActionCreator, StoreEnhancer } from 'redux';\nimport { compose } from 'redux';\n\n/**\r\n * @public\r\n */\nexport interface DevToolsEnhancerOptions {\n  /**\r\n   * the instance name to be showed on the monitor page. Default value is `document.title`.\r\n   * If not specified and there's no document title, it will consist of `tabId` and `instanceId`.\r\n   */\n  name?: string;\n  /**\r\n   * action creators functions to be available in the Dispatcher.\r\n   */\n  actionCreators?: ActionCreator<any>[] | {\n    [key: string]: ActionCreator<any>;\n  };\n  /**\r\n   * if more than one action is dispatched in the indicated interval, all new actions will be collected and sent at once.\r\n   * It is the joint between performance and speed. When set to `0`, all actions will be sent instantly.\r\n   * Set it to a higher value when experiencing perf issues (also `maxAge` to a lower value).\r\n   *\r\n   * @default 500 ms.\r\n   */\n  latency?: number;\n  /**\r\n   * (> 1) - maximum allowed actions to be stored in the history tree. The oldest actions are removed once maxAge is reached. It's critical for performance.\r\n   *\r\n   * @default 50\r\n   */\n  maxAge?: number;\n  /**\r\n   * Customizes how actions and state are serialized and deserialized. Can be a boolean or object. If given a boolean, the behavior is the same as if you\r\n   * were to pass an object and specify `options` as a boolean. Giving an object allows fine-grained customization using the `replacer` and `reviver`\r\n   * functions.\r\n   */\n  serialize?: boolean | {\n    /**\r\n     * - `undefined` - will use regular `JSON.stringify` to send data (it's the fast mode).\r\n     * - `false` - will handle also circular references.\r\n     * - `true` - will handle also date, regex, undefined, error objects, symbols, maps, sets and functions.\r\n     * - object, which contains `date`, `regex`, `undefined`, `error`, `symbol`, `map`, `set` and `function` keys.\r\n     *   For each of them you can indicate if to include (by setting as `true`).\r\n     *   For `function` key you can also specify a custom function which handles serialization.\r\n     *   See [`jsan`](https://github.com/kolodny/jsan) for more details.\r\n     */\n    options?: undefined | boolean | {\n      date?: true;\n      regex?: true;\n      undefined?: true;\n      error?: true;\n      symbol?: true;\n      map?: true;\n      set?: true;\n      function?: true | ((fn: (...args: any[]) => any) => string);\n    };\n    /**\r\n     * [JSON replacer function](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#The_replacer_parameter) used for both actions and states stringify.\r\n     * In addition, you can specify a data type by adding a [`__serializedType__`](https://github.com/zalmoxisus/remotedev-serialize/blob/master/helpers/index.js#L4)\r\n     * key. So you can deserialize it back while importing or persisting data.\r\n     * Moreover, it will also [show a nice preview showing the provided custom type](https://cloud.githubusercontent.com/assets/7957859/21814330/a17d556a-d761-11e6-85ef-159dd12f36c5.png):\r\n     */\n    replacer?: (key: string, value: unknown) => any;\n    /**\r\n     * [JSON `reviver` function](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse#Using_the_reviver_parameter)\r\n     * used for parsing the imported actions and states. See [`remotedev-serialize`](https://github.com/zalmoxisus/remotedev-serialize/blob/master/immutable/serialize.js#L8-L41)\r\n     * as an example on how to serialize special data types and get them back.\r\n     */\n    reviver?: (key: string, value: unknown) => any;\n    /**\r\n     * Automatically serialize/deserialize immutablejs via [remotedev-serialize](https://github.com/zalmoxisus/remotedev-serialize).\r\n     * Just pass the Immutable library. It will support all ImmutableJS structures. You can even export them into a file and get them back.\r\n     * The only exception is `Record` class, for which you should pass this in addition the references to your classes in `refs`.\r\n     */\n    immutable?: any;\n    /**\r\n     * ImmutableJS `Record` classes used to make possible restore its instances back when importing, persisting...\r\n     */\n    refs?: any;\n  };\n  /**\r\n   * function which takes `action` object and id number as arguments, and should return `action` object back.\r\n   */\n  actionSanitizer?: <A extends Action>(action: A, id: number) => A;\n  /**\r\n   * function which takes `state` object and index as arguments, and should return `state` object back.\r\n   */\n  stateSanitizer?: <S>(state: S, index: number) => S;\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsAllowlist` specified, `actionsDenylist` is ignored.\r\n   */\n  actionsDenylist?: string | string[];\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsAllowlist` specified, `actionsDenylist` is ignored.\r\n   */\n  actionsAllowlist?: string | string[];\n  /**\r\n   * called for every action before sending, takes `state` and `action` object, and returns `true` in case it allows sending the current data to the monitor.\r\n   * Use it as a more advanced version of `actionsDenylist`/`actionsAllowlist` parameters.\r\n   */\n  predicate?: <S, A extends Action>(state: S, action: A) => boolean;\n  /**\r\n   * if specified as `false`, it will not record the changes till clicking on `Start recording` button.\r\n   * Available only for Redux enhancer, for others use `autoPause`.\r\n   *\r\n   * @default true\r\n   */\n  shouldRecordChanges?: boolean;\n  /**\r\n   * if specified, whenever clicking on `Pause recording` button and there are actions in the history log, will add this action type.\r\n   * If not specified, will commit when paused. Available only for Redux enhancer.\r\n   *\r\n   * @default \"@@PAUSED\"\"\r\n   */\n  pauseActionType?: string;\n  /**\r\n   * auto pauses when the extension’s window is not opened, and so has zero impact on your app when not in use.\r\n   * Not available for Redux enhancer (as it already does it but storing the data to be sent).\r\n   *\r\n   * @default false\r\n   */\n  autoPause?: boolean;\n  /**\r\n   * if specified as `true`, it will not allow any non-monitor actions to be dispatched till clicking on `Unlock changes` button.\r\n   * Available only for Redux enhancer.\r\n   *\r\n   * @default false\r\n   */\n  shouldStartLocked?: boolean;\n  /**\r\n   * if set to `false`, will not recompute the states on hot reloading (or on replacing the reducers). Available only for Redux enhancer.\r\n   *\r\n   * @default true\r\n   */\n  shouldHotReload?: boolean;\n  /**\r\n   * if specified as `true`, whenever there's an exception in reducers, the monitors will show the error message, and next actions will not be dispatched.\r\n   *\r\n   * @default false\r\n   */\n  shouldCatchErrors?: boolean;\n  /**\r\n   * If you want to restrict the extension, specify the features you allow.\r\n   * If not specified, all of the features are enabled. When set as an object, only those included as `true` will be allowed.\r\n   * Note that except `true`/`false`, `import` and `export` can be set as `custom` (which is by default for Redux enhancer), meaning that the importing/exporting occurs on the client side.\r\n   * Otherwise, you'll get/set the data right from the monitor part.\r\n   */\n  features?: {\n    /**\r\n     * start/pause recording of dispatched actions\r\n     */\n    pause?: boolean;\n    /**\r\n     * lock/unlock dispatching actions and side effects\r\n     */\n    lock?: boolean;\n    /**\r\n     * persist states on page reloading\r\n     */\n    persist?: boolean;\n    /**\r\n     * export history of actions in a file\r\n     */\n    export?: boolean | 'custom';\n    /**\r\n     * import history of actions from a file\r\n     */\n    import?: boolean | 'custom';\n    /**\r\n     * jump back and forth (time travelling)\r\n     */\n    jump?: boolean;\n    /**\r\n     * skip (cancel) actions\r\n     */\n    skip?: boolean;\n    /**\r\n     * drag and drop actions in the history list\r\n     */\n    reorder?: boolean;\n    /**\r\n     * dispatch custom actions or action creators\r\n     */\n    dispatch?: boolean;\n    /**\r\n     * generate tests for the selected actions\r\n     */\n    test?: boolean;\n  };\n  /**\r\n   * Set to true or a stacktrace-returning function to record call stack traces for dispatched actions.\r\n   * Defaults to false.\r\n   */\n  trace?: boolean | (<A extends Action>(action: A) => string);\n  /**\r\n   * The maximum number of stack trace entries to record per action. Defaults to 10.\r\n   */\n  traceLimit?: number;\n}\ntype Compose = typeof compose;\ninterface ComposeWithDevTools {\n  (options: DevToolsEnhancerOptions): Compose;\n  <StoreExt extends {}>(...funcs: StoreEnhancer<StoreExt>[]): StoreEnhancer<StoreExt>;\n}\n\n/**\r\n * @public\r\n */\nexport const composeWithDevTools: ComposeWithDevTools = typeof window !== 'undefined' && (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ : function () {\n  if (arguments.length === 0) return undefined;\n  if (typeof arguments[0] === 'object') return compose;\n  return compose.apply(null, arguments as any as Function[]);\n};\n\n/**\r\n * @public\r\n */\nexport const devToolsEnhancer: {\n  (options: DevToolsEnhancerOptions): StoreEnhancer<any>;\n} = typeof window !== 'undefined' && (window as any).__REDUX_DEVTOOLS_EXTENSION__ ? (window as any).__REDUX_DEVTOOLS_EXTENSION__ : function () {\n  return function (noop) {\n    return noop;\n  };\n};", "import type { Middleware, UnknownAction } from 'redux';\nimport type { ThunkMiddleware } from 'redux-thunk';\nimport { thunk as thunkMiddleware, withExtraArgument } from 'redux-thunk';\nimport type { ActionCreatorInvariantMiddlewareOptions } from './actionCreatorInvariantMiddleware';\nimport { createActionCreatorInvariantMiddleware } from './actionCreatorInvariantMiddleware';\nimport type { ImmutableStateInvariantMiddlewareOptions } from './immutableStateInvariantMiddleware';\n/* PROD_START_REMOVE_UMD */\nimport { createImmutableStateInvariantMiddleware } from './immutableStateInvariantMiddleware';\n/* PROD_STOP_REMOVE_UMD */\n\nimport type { SerializableStateInvariantMiddlewareOptions } from './serializableStateInvariantMiddleware';\nimport { createSerializableStateInvariantMiddleware } from './serializableStateInvariantMiddleware';\nimport type { ExcludeFromTuple } from './tsHelpers';\nimport { Tuple } from './utils';\nfunction isBoolean(x: any): x is boolean {\n  return typeof x === 'boolean';\n}\ninterface ThunkOptions<E = any> {\n  extraArgument: E;\n}\ninterface GetDefaultMiddlewareOptions {\n  thunk?: boolean | ThunkOptions;\n  immutableCheck?: boolean | ImmutableStateInvariantMiddlewareOptions;\n  serializableCheck?: boolean | SerializableStateInvariantMiddlewareOptions;\n  actionCreatorCheck?: boolean | ActionCreatorInvariantMiddlewareOptions;\n}\nexport type ThunkMiddlewareFor<S, O extends GetDefaultMiddlewareOptions = {}> = O extends {\n  thunk: false;\n} ? never : O extends {\n  thunk: {\n    extraArgument: infer E;\n  };\n} ? ThunkMiddleware<S, UnknownAction, E> : ThunkMiddleware<S, UnknownAction>;\nexport type GetDefaultMiddleware<S = any> = <O extends GetDefaultMiddlewareOptions = {\n  thunk: true;\n  immutableCheck: true;\n  serializableCheck: true;\n  actionCreatorCheck: true;\n}>(options?: O) => Tuple<ExcludeFromTuple<[ThunkMiddlewareFor<S, O>], never>>;\nexport const buildGetDefaultMiddleware = <S = any,>(): GetDefaultMiddleware<S> => function getDefaultMiddleware(options) {\n  const {\n    thunk = true,\n    immutableCheck = true,\n    serializableCheck = true,\n    actionCreatorCheck = true\n  } = options ?? {};\n  let middlewareArray = new Tuple<Middleware[]>();\n  if (thunk) {\n    if (isBoolean(thunk)) {\n      middlewareArray.push(thunkMiddleware);\n    } else {\n      middlewareArray.push(withExtraArgument(thunk.extraArgument));\n    }\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (immutableCheck) {\n      /* PROD_START_REMOVE_UMD */\n      let immutableOptions: ImmutableStateInvariantMiddlewareOptions = {};\n      if (!isBoolean(immutableCheck)) {\n        immutableOptions = immutableCheck;\n      }\n      middlewareArray.unshift(createImmutableStateInvariantMiddleware(immutableOptions));\n      /* PROD_STOP_REMOVE_UMD */\n    }\n    if (serializableCheck) {\n      let serializableOptions: SerializableStateInvariantMiddlewareOptions = {};\n      if (!isBoolean(serializableCheck)) {\n        serializableOptions = serializableCheck;\n      }\n      middlewareArray.push(createSerializableStateInvariantMiddleware(serializableOptions));\n    }\n    if (actionCreatorCheck) {\n      let actionCreatorOptions: ActionCreatorInvariantMiddlewareOptions = {};\n      if (!isBoolean(actionCreatorCheck)) {\n        actionCreatorOptions = actionCreatorCheck;\n      }\n      middlewareArray.unshift(createActionCreatorInvariantMiddleware(actionCreatorOptions));\n    }\n  }\n  return middlewareArray as any;\n};", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport { isAction } from 'redux';\nimport type { IsUnknownOrNonInferrable, IfMaybeUndefined, IfVoid, IsAny } from './tsHelpers';\nimport { hasMatchFunction } from './tsHelpers';\n\n/**\n * An action with a string type and an associated payload. This is the\n * type of action returned by `createAction()` action creators.\n *\n * @template P The type of the action's payload.\n * @template T the type used for the action type.\n * @template M The type of the action's meta (optional)\n * @template E The type of the action's error (optional)\n *\n * @public\n */\nexport type PayloadAction<P = void, T extends string = string, M = never, E = never> = {\n  payload: P;\n  type: T;\n} & ([M] extends [never] ? {} : {\n  meta: M;\n}) & ([E] extends [never] ? {} : {\n  error: E;\n});\n\n/**\n * A \"prepare\" method to be used as the second parameter of `createAction`.\n * Takes any number of arguments and returns a Flux Standard Action without\n * type (will be added later) that *must* contain a payload (might be undefined).\n *\n * @public\n */\nexport type PrepareAction<P> = ((...args: any[]) => {\n  payload: P;\n}) | ((...args: any[]) => {\n  payload: P;\n  meta: any;\n}) | ((...args: any[]) => {\n  payload: P;\n  error: any;\n}) | ((...args: any[]) => {\n  payload: P;\n  meta: any;\n  error: any;\n});\n\n/**\n * Internal version of `ActionCreatorWithPreparedPayload`. Not to be used externally.\n *\n * @internal\n */\nexport type _ActionCreatorWithPreparedPayload<PA extends PrepareAction<any> | void, T extends string = string> = PA extends PrepareAction<infer P> ? ActionCreatorWithPreparedPayload<Parameters<PA>, P, T, ReturnType<PA> extends {\n  error: infer E;\n} ? E : never, ReturnType<PA> extends {\n  meta: infer M;\n} ? M : never> : void;\n\n/**\n * Basic type for all action creators.\n *\n * @inheritdoc {redux#ActionCreator}\n */\nexport type BaseActionCreator<P, T extends string, M = never, E = never> = {\n  type: T;\n  match: (action: unknown) => action is PayloadAction<P, T, M, E>;\n};\n\n/**\n * An action creator that takes multiple arguments that are passed\n * to a `PrepareAction` method to create the final Action.\n * @typeParam Args arguments for the action creator function\n * @typeParam P `payload` type\n * @typeParam T `type` name\n * @typeParam E optional `error` type\n * @typeParam M optional `meta` type\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithPreparedPayload<Args extends unknown[], P, T extends string = string, E = never, M = never> extends BaseActionCreator<P, T, M, E> {\n  /**\n   * Calling this {@link redux#ActionCreator} with `Args` will return\n   * an Action with a payload of type `P` and (depending on the `PrepareAction`\n   * method used) a `meta`- and `error` property of types `M` and `E` respectively.\n   */\n  (...args: Args): PayloadAction<P, T, M, E>;\n}\n\n/**\n * An action creator of type `T` that takes an optional payload of type `P`.\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithOptionalPayload<P, T extends string = string> extends BaseActionCreator<P, T> {\n  /**\n   * Calling this {@link redux#ActionCreator} with an argument will\n   * return a {@link PayloadAction} of type `T` with a payload of `P`.\n   * Calling it without an argument will return a PayloadAction with a payload of `undefined`.\n   */\n  (payload?: P): PayloadAction<P, T>;\n}\n\n/**\n * An action creator of type `T` that takes no payload.\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithoutPayload<T extends string = string> extends BaseActionCreator<undefined, T> {\n  /**\n   * Calling this {@link redux#ActionCreator} will\n   * return a {@link PayloadAction} of type `T` with a payload of `undefined`\n   */\n  (noArgument: void): PayloadAction<undefined, T>;\n}\n\n/**\n * An action creator of type `T` that requires a payload of type P.\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithPayload<P, T extends string = string> extends BaseActionCreator<P, T> {\n  /**\n   * Calling this {@link redux#ActionCreator} with an argument will\n   * return a {@link PayloadAction} of type `T` with a payload of `P`\n   */\n  (payload: P): PayloadAction<P, T>;\n}\n\n/**\n * An action creator of type `T` whose `payload` type could not be inferred. Accepts everything as `payload`.\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithNonInferrablePayload<T extends string = string> extends BaseActionCreator<unknown, T> {\n  /**\n   * Calling this {@link redux#ActionCreator} with an argument will\n   * return a {@link PayloadAction} of type `T` with a payload\n   * of exactly the type of the argument.\n   */\n  <PT extends unknown>(payload: PT): PayloadAction<PT, T>;\n}\n\n/**\n * An action creator that produces actions with a `payload` attribute.\n *\n * @typeParam P the `payload` type\n * @typeParam T the `type` of the resulting action\n * @typeParam PA if the resulting action is preprocessed by a `prepare` method, the signature of said method.\n *\n * @public\n */\nexport type PayloadActionCreator<P = void, T extends string = string, PA extends PrepareAction<P> | void = void> = IfPrepareActionMethodProvided<PA, _ActionCreatorWithPreparedPayload<PA, T>,\n// else\nIsAny<P, ActionCreatorWithPayload<any, T>, IsUnknownOrNonInferrable<P, ActionCreatorWithNonInferrablePayload<T>,\n// else\nIfVoid<P, ActionCreatorWithoutPayload<T>,\n// else\nIfMaybeUndefined<P, ActionCreatorWithOptionalPayload<P, T>,\n// else\nActionCreatorWithPayload<P, T>>>>>>;\n\n/**\n * A utility function to create an action creator for the given action type\n * string. The action creator accepts a single argument, which will be included\n * in the action object as a field called payload. The action creator function\n * will also have its toString() overridden so that it returns the action type.\n *\n * @param type The action type to use for created actions.\n * @param prepare (optional) a method that takes any number of arguments and returns { payload } or { payload, meta }.\n *                If this is given, the resulting action creator will pass its arguments to this method to calculate payload & meta.\n *\n * @public\n */\nexport function createAction<P = void, T extends string = string>(type: T): PayloadActionCreator<P, T>;\n\n/**\n * A utility function to create an action creator for the given action type\n * string. The action creator accepts a single argument, which will be included\n * in the action object as a field called payload. The action creator function\n * will also have its toString() overridden so that it returns the action type.\n *\n * @param type The action type to use for created actions.\n * @param prepare (optional) a method that takes any number of arguments and returns { payload } or { payload, meta }.\n *                If this is given, the resulting action creator will pass its arguments to this method to calculate payload & meta.\n *\n * @public\n */\nexport function createAction<PA extends PrepareAction<any>, T extends string = string>(type: T, prepareAction: PA): PayloadActionCreator<ReturnType<PA>['payload'], T, PA>;\nexport function createAction(type: string, prepareAction?: Function): any {\n  function actionCreator(...args: any[]) {\n    if (prepareAction) {\n      let prepared = prepareAction(...args);\n      if (!prepared) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(0) : 'prepareAction did not return an object');\n      }\n      return {\n        type,\n        payload: prepared.payload,\n        ...('meta' in prepared && {\n          meta: prepared.meta\n        }),\n        ...('error' in prepared && {\n          error: prepared.error\n        })\n      };\n    }\n    return {\n      type,\n      payload: args[0]\n    };\n  }\n  actionCreator.toString = () => `${type}`;\n  actionCreator.type = type;\n  actionCreator.match = (action: unknown): action is PayloadAction => isAction(action) && action.type === type;\n  return actionCreator;\n}\n\n/**\n * Returns true if value is an RTK-like action creator, with a static type property and match method.\n */\nexport function isActionCreator(action: unknown): action is BaseActionCreator<unknown, string> & Function {\n  return typeof action === 'function' && 'type' in action &&\n  // hasMatchFunction only wants Matchers but I don't see the point in rewriting it\n  hasMatchFunction(action as any);\n}\n\n/**\n * Returns true if value is an action with a string type and valid Flux Standard Action keys.\n */\nexport function isFSA(action: unknown): action is {\n  type: string;\n  payload?: unknown;\n  error?: unknown;\n  meta?: unknown;\n} {\n  return isAction(action) && Object.keys(action).every(isValidKey);\n}\nfunction isValidKey(key: string) {\n  return ['type', 'payload', 'error', 'meta'].indexOf(key) > -1;\n}\n\n// helper types for more readable typings\n\ntype IfPrepareActionMethodProvided<PA extends PrepareAction<any> | void, True, False> = PA extends ((...args: any[]) => any) ? True : False;", "import type { Middleware, StoreEnhancer } from 'redux';\nimport type { Tuple } from './utils';\nexport function safeAssign<T extends object>(target: T, ...args: Array<Partial<NoInfer<T>>>) {\n  Object.assign(target, ...args);\n}\n\n/**\n * return True if T is `any`, otherwise return False\n * taken from https://github.com/joonhocho/tsdef\n *\n * @internal\n */\nexport type IsAny<T, True, False = never> =\n// test if we are going the left AND right path in the condition\ntrue | false extends (T extends never ? true : false) ? True : False;\nexport type CastAny<T, CastTo> = IsAny<T, CastTo, T>;\n\n/**\n * return True if T is `unknown`, otherwise return False\n * taken from https://github.com/joonhocho/tsdef\n *\n * @internal\n */\nexport type IsUnknown<T, True, False = never> = unknown extends T ? IsAny<T, False, True> : False;\nexport type FallbackIfUnknown<T, Fallback> = IsUnknown<T, Fallback, T>;\n\n/**\n * @internal\n */\nexport type IfMaybeUndefined<P, True, False> = [undefined] extends [P] ? True : False;\n\n/**\n * @internal\n */\nexport type IfVoid<P, True, False> = [void] extends [P] ? True : False;\n\n/**\n * @internal\n */\nexport type IsEmptyObj<T, True, False = never> = T extends any ? keyof T extends never ? IsUnknown<T, False, IfMaybeUndefined<T, False, IfVoid<T, False, True>>> : False : never;\n\n/**\n * returns True if TS version is above 3.5, False if below.\n * uses feature detection to detect TS version >= 3.5\n * * versions below 3.5 will return `{}` for unresolvable interference\n * * versions above will return `unknown`\n *\n * @internal\n */\nexport type AtLeastTS35<True, False> = [True, False][IsUnknown<ReturnType<<T>() => T>, 0, 1>];\n\n/**\n * @internal\n */\nexport type IsUnknownOrNonInferrable<T, True, False> = AtLeastTS35<IsUnknown<T, True, False>, IsEmptyObj<T, True, IsUnknown<T, True, False>>>;\n\n/**\n * Convert a Union type `(A|B)` to an intersection type `(A&B)`\n */\nexport type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never;\n\n// Appears to have a convenient side effect of ignoring `never` even if that's not what you specified\nexport type ExcludeFromTuple<T, E, Acc extends unknown[] = []> = T extends [infer Head, ...infer Tail] ? ExcludeFromTuple<Tail, E, [...Acc, ...([Head] extends [E] ? [] : [Head])]> : Acc;\ntype ExtractDispatchFromMiddlewareTuple<MiddlewareTuple extends readonly any[], Acc extends {}> = MiddlewareTuple extends [infer Head, ...infer Tail] ? ExtractDispatchFromMiddlewareTuple<Tail, Acc & (Head extends Middleware<infer D> ? IsAny<D, {}, D> : {})> : Acc;\nexport type ExtractDispatchExtensions<M> = M extends Tuple<infer MiddlewareTuple> ? ExtractDispatchFromMiddlewareTuple<MiddlewareTuple, {}> : M extends ReadonlyArray<Middleware> ? ExtractDispatchFromMiddlewareTuple<[...M], {}> : never;\ntype ExtractStoreExtensionsFromEnhancerTuple<EnhancerTuple extends readonly any[], Acc extends {}> = EnhancerTuple extends [infer Head, ...infer Tail] ? ExtractStoreExtensionsFromEnhancerTuple<Tail, Acc & (Head extends StoreEnhancer<infer Ext> ? IsAny<Ext, {}, Ext> : {})> : Acc;\nexport type ExtractStoreExtensions<E> = E extends Tuple<infer EnhancerTuple> ? ExtractStoreExtensionsFromEnhancerTuple<EnhancerTuple, {}> : E extends ReadonlyArray<StoreEnhancer> ? UnionToIntersection<E[number] extends StoreEnhancer<infer Ext> ? Ext extends {} ? IsAny<Ext, {}, Ext> : {} : {}> : never;\ntype ExtractStateExtensionsFromEnhancerTuple<EnhancerTuple extends readonly any[], Acc extends {}> = EnhancerTuple extends [infer Head, ...infer Tail] ? ExtractStateExtensionsFromEnhancerTuple<Tail, Acc & (Head extends StoreEnhancer<any, infer StateExt> ? IsAny<StateExt, {}, StateExt> : {})> : Acc;\nexport type ExtractStateExtensions<E> = E extends Tuple<infer EnhancerTuple> ? ExtractStateExtensionsFromEnhancerTuple<EnhancerTuple, {}> : E extends ReadonlyArray<StoreEnhancer> ? UnionToIntersection<E[number] extends StoreEnhancer<any, infer StateExt> ? StateExt extends {} ? IsAny<StateExt, {}, StateExt> : {} : {}> : never;\n\n/**\n * Helper type. Passes T out again, but boxes it in a way that it cannot\n * \"widen\" the type by accident if it is a generic that should be inferred\n * from elsewhere.\n *\n * @internal\n */\nexport type NoInfer<T> = [T][T extends any ? 0 : never];\nexport type NonUndefined<T> = T extends undefined ? never : T;\nexport type WithRequiredProp<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;\nexport type WithOptionalProp<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;\nexport interface TypeGuard<T> {\n  (value: any): value is T;\n}\nexport interface HasMatchFunction<T> {\n  match: TypeGuard<T>;\n}\nexport const hasMatchFunction = <T,>(v: Matcher<T>): v is HasMatchFunction<T> => {\n  return v && typeof (v as HasMatchFunction<T>).match === 'function';\n};\n\n/** @public */\nexport type Matcher<T> = HasMatchFunction<T> | TypeGuard<T>;\n\n/** @public */\nexport type ActionFromMatcher<M extends Matcher<any>> = M extends Matcher<infer T> ? T : never;\nexport type Id<T> = { [K in keyof T]: T[K] } & {};\nexport type Tail<T extends any[]> = T extends [any, ...infer Tail] ? Tail : never;\nexport type UnknownIfNonSpecific<T> = {} extends T ? unknown : T;\n\n/**\n * A Promise that will never reject.\n * @see https://github.com/reduxjs/redux-toolkit/issues/4101\n */\nexport type SafePromise<T> = Promise<T> & {\n  __linterBrands: 'SafePromise';\n};\n\n/**\n * Properly wraps a Promise as a {@link SafePromise} with .catch(fallback).\n */\nexport function asSafePromise<Resolved, Rejected>(promise: Promise<Resolved>, fallback: (error: unknown) => Rejected) {\n  return promise.catch(fallback) as SafePromise<Resolved | Rejected>;\n}", "import type { Middleware } from 'redux';\nimport { isActionCreator as isRTKAction } from './createAction';\nexport interface ActionCreatorInvariantMiddlewareOptions {\n  /**\n   * The function to identify whether a value is an action creator.\n   * The default checks for a function with a static type property and match method.\n   */\n  isActionCreator?: (action: unknown) => action is Function & {\n    type?: unknown;\n  };\n}\nexport function getMessage(type?: unknown) {\n  const splitType = type ? `${type}`.split('/') : [];\n  const actionName = splitType[splitType.length - 1] || 'actionCreator';\n  return `Detected an action creator with type \"${type || 'unknown'}\" being dispatched. \nMake sure you're calling the action creator before dispatching, i.e. \\`dispatch(${actionName}())\\` instead of \\`dispatch(${actionName})\\`. This is necessary even if the action has no payload.`;\n}\nexport function createActionCreatorInvariantMiddleware(options: ActionCreatorInvariantMiddlewareOptions = {}): Middleware {\n  if (process.env.NODE_ENV === 'production') {\n    return () => next => action => next(action);\n  }\n  const {\n    isActionCreator = isRTKAction\n  } = options;\n  return () => next => action => {\n    if (isActionCreator(action)) {\n      console.warn(getMessage(action.type));\n    }\n    return next(action);\n  };\n}", "import { produce as createNextState, isDraftable } from 'immer';\nexport function getTimeMeasureUtils(maxDelay: number, fnName: string) {\n  let elapsed = 0;\n  return {\n    measureTime<T>(fn: () => T): T {\n      const started = Date.now();\n      try {\n        return fn();\n      } finally {\n        const finished = Date.now();\n        elapsed += finished - started;\n      }\n    },\n    warnIfExceeded() {\n      if (elapsed > maxDelay) {\n        console.warn(`${fnName} took ${elapsed}ms, which is more than the warning threshold of ${maxDelay}ms. \nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\nIt is disabled in production builds, so you don't need to worry about that.`);\n      }\n    }\n  };\n}\nexport function delay(ms: number) {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\nexport class Tuple<Items extends ReadonlyArray<unknown> = []> extends Array<Items[number]> {\n  constructor(length: number);\n  constructor(...items: Items);\n  constructor(...items: any[]) {\n    super(...items);\n    Object.setPrototypeOf(this, Tuple.prototype);\n  }\n  static override get [Symbol.species]() {\n    return Tuple as any;\n  }\n  override concat<AdditionalItems extends ReadonlyArray<unknown>>(items: Tuple<AdditionalItems>): Tuple<[...Items, ...AdditionalItems]>;\n  override concat<AdditionalItems extends ReadonlyArray<unknown>>(items: AdditionalItems): Tuple<[...Items, ...AdditionalItems]>;\n  override concat<AdditionalItems extends ReadonlyArray<unknown>>(...items: AdditionalItems): Tuple<[...Items, ...AdditionalItems]>;\n  override concat(...arr: any[]) {\n    return super.concat.apply(this, arr);\n  }\n  prepend<AdditionalItems extends ReadonlyArray<unknown>>(items: Tuple<AdditionalItems>): Tuple<[...AdditionalItems, ...Items]>;\n  prepend<AdditionalItems extends ReadonlyArray<unknown>>(items: AdditionalItems): Tuple<[...AdditionalItems, ...Items]>;\n  prepend<AdditionalItems extends ReadonlyArray<unknown>>(...items: AdditionalItems): Tuple<[...AdditionalItems, ...Items]>;\n  prepend(...arr: any[]) {\n    if (arr.length === 1 && Array.isArray(arr[0])) {\n      return new Tuple(...arr[0].concat(this));\n    }\n    return new Tuple(...arr.concat(this));\n  }\n}\nexport function freezeDraftable<T>(val: T) {\n  return isDraftable(val) ? createNextState(val, () => {}) : val;\n}\nexport function getOrInsert<K extends object, V>(map: WeakMap<K, V>, key: K, value: V): V;\nexport function getOrInsert<K, V>(map: Map<K, V>, key: K, value: V): V;\nexport function getOrInsert<K extends object, V>(map: Map<K, V> | WeakMap<K, V>, key: K, value: V): V {\n  if (map.has(key)) return map.get(key) as V;\n  return map.set(key, value).get(key) as V;\n}\nexport function getOrInsertComputed<K extends object, V>(map: WeakMap<K, V>, key: K, compute: (key: K) => V): V;\nexport function getOrInsertComputed<K, V>(map: Map<K, V>, key: K, compute: (key: K) => V): V;\nexport function getOrInsertComputed<K extends object, V>(map: Map<K, V> | WeakMap<K, V>, key: K, compute: (key: K) => V): V {\n  if (map.has(key)) return map.get(key) as V;\n  return map.set(key, compute(key)).get(key) as V;\n}\nexport function promiseWithResolvers<T>(): {\n  promise: Promise<T>;\n  resolve: (value: T | PromiseLike<T>) => void;\n  reject: (reason?: any) => void;\n} {\n  let resolve: any;\n  let reject: any;\n  const promise = new Promise<T>((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return {\n    promise,\n    resolve,\n    reject\n  };\n}", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2 } from \"@reduxjs/toolkit\";\nimport type { Middleware } from 'redux';\nimport type { IgnorePaths } from './serializableStateInvariantMiddleware';\nimport { getTimeMeasureUtils } from './utils';\ntype EntryProcessor = (key: string, value: any) => any;\n\n/**\n * The default `isImmutable` function.\n *\n * @public\n */\nexport function isImmutableDefault(value: unknown): boolean {\n  return typeof value !== 'object' || value == null || Object.isFrozen(value);\n}\nexport function trackForMutations(isImmutable: IsImmutableFunc, ignorePaths: IgnorePaths | undefined, obj: any) {\n  const trackedProperties = trackProperties(isImmutable, ignorePaths, obj);\n  return {\n    detectMutations() {\n      return detectMutations(isImmutable, ignorePaths, trackedProperties, obj);\n    }\n  };\n}\ninterface TrackedProperty {\n  value: any;\n  children: Record<string, any>;\n}\nfunction trackProperties(isImmutable: IsImmutableFunc, ignorePaths: IgnorePaths = [], obj: Record<string, any>, path: string = '', checkedObjects: Set<Record<string, any>> = new Set()) {\n  const tracked: Partial<TrackedProperty> = {\n    value: obj\n  };\n  if (!isImmutable(obj) && !checkedObjects.has(obj)) {\n    checkedObjects.add(obj);\n    tracked.children = {};\n    for (const key in obj) {\n      const childPath = path ? path + '.' + key : key;\n      if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\n        continue;\n      }\n      tracked.children[key] = trackProperties(isImmutable, ignorePaths, obj[key], childPath);\n    }\n  }\n  return tracked as TrackedProperty;\n}\nfunction detectMutations(isImmutable: IsImmutableFunc, ignoredPaths: IgnorePaths = [], trackedProperty: TrackedProperty, obj: any, sameParentRef: boolean = false, path: string = ''): {\n  wasMutated: boolean;\n  path?: string;\n} {\n  const prevObj = trackedProperty ? trackedProperty.value : undefined;\n  const sameRef = prevObj === obj;\n  if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\n    return {\n      wasMutated: true,\n      path\n    };\n  }\n  if (isImmutable(prevObj) || isImmutable(obj)) {\n    return {\n      wasMutated: false\n    };\n  }\n\n  // Gather all keys from prev (tracked) and after objs\n  const keysToDetect: Record<string, boolean> = {};\n  for (let key in trackedProperty.children) {\n    keysToDetect[key] = true;\n  }\n  for (let key in obj) {\n    keysToDetect[key] = true;\n  }\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (let key in keysToDetect) {\n    const nestedPath = path ? path + '.' + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some(ignored => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    const result = detectMutations(isImmutable, ignoredPaths, trackedProperty.children[key], obj[key], sameRef, nestedPath);\n    if (result.wasMutated) {\n      return result;\n    }\n  }\n  return {\n    wasMutated: false\n  };\n}\ntype IsImmutableFunc = (value: any) => boolean;\n\n/**\n * Options for `createImmutableStateInvariantMiddleware()`.\n *\n * @public\n */\nexport interface ImmutableStateInvariantMiddlewareOptions {\n  /**\n    Callback function to check if a value is considered to be immutable.\n    This function is applied recursively to every value contained in the state.\n    The default implementation will return true for primitive types\n    (like numbers, strings, booleans, null and undefined).\n   */\n  isImmutable?: IsImmutableFunc;\n  /**\n    An array of dot-separated path strings that match named nodes from\n    the root state to ignore when checking for immutability.\n    Defaults to undefined\n   */\n  ignoredPaths?: IgnorePaths;\n  /** Print a warning if checks take longer than N ms. Default: 32ms */\n  warnAfter?: number;\n}\n\n/**\n * Creates a middleware that checks whether any state was mutated in between\n * dispatches or during a dispatch. If any mutations are detected, an error is\n * thrown.\n *\n * @param options Middleware options.\n *\n * @public\n */\nexport function createImmutableStateInvariantMiddleware(options: ImmutableStateInvariantMiddlewareOptions = {}): Middleware {\n  if (process.env.NODE_ENV === 'production') {\n    return () => next => action => next(action);\n  } else {\n    function stringify(obj: any, serializer?: EntryProcessor, indent?: string | number, decycler?: EntryProcessor): string {\n      return JSON.stringify(obj, getSerialize(serializer, decycler), indent);\n    }\n    function getSerialize(serializer?: EntryProcessor, decycler?: EntryProcessor): EntryProcessor {\n      let stack: any[] = [],\n        keys: any[] = [];\n      if (!decycler) decycler = function (_: string, value: any) {\n        if (stack[0] === value) return '[Circular ~]';\n        return '[Circular ~.' + keys.slice(0, stack.indexOf(value)).join('.') + ']';\n      };\n      return function (this: any, key: string, value: any) {\n        if (stack.length > 0) {\n          var thisPos = stack.indexOf(this);\n          ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\n          ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\n          if (~stack.indexOf(value)) value = decycler!.call(this, key, value);\n        } else stack.push(value);\n        return serializer == null ? value : serializer.call(this, key, value);\n      };\n    }\n    let {\n      isImmutable = isImmutableDefault,\n      ignoredPaths,\n      warnAfter = 32\n    } = options;\n    const track = trackForMutations.bind(null, isImmutable, ignoredPaths);\n    return ({\n      getState\n    }) => {\n      let state = getState();\n      let tracker = track(state);\n      let result;\n      return next => action => {\n        const measureUtils = getTimeMeasureUtils(warnAfter, 'ImmutableStateInvariantMiddleware');\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          // Track before potentially not meeting the invariant\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(19) : `A state mutation was detected between dispatches, in the path '${result.path || ''}'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        const dispatchedAction = next(action);\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          // Track before potentially not meeting the invariant\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(20) : `A state mutation was detected inside a dispatch, in the path: ${result.path || ''}. Take a look at the reducer(s) handling the action ${stringify(action)}. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n        return dispatchedAction;\n      };\n    };\n  }\n}", "import type { Middleware } from 'redux';\nimport { isAction, isPlainObject } from 'redux';\nimport { getTimeMeasureUtils } from './utils';\n\n/**\n * Returns true if the passed value is \"plain\", i.e. a value that is either\n * directly JSON-serializable (boolean, number, string, array, plain object)\n * or `undefined`.\n *\n * @param val The value to check.\n *\n * @public\n */\nexport function isPlain(val: any) {\n  const type = typeof val;\n  return val == null || type === 'string' || type === 'boolean' || type === 'number' || Array.isArray(val) || isPlainObject(val);\n}\ninterface NonSerializableValue {\n  keyPath: string;\n  value: unknown;\n}\nexport type IgnorePaths = readonly (string | RegExp)[];\n\n/**\n * @public\n */\nexport function findNonSerializableValue(value: unknown, path: string = '', isSerializable: (value: unknown) => boolean = isPlain, getEntries?: (value: unknown) => [string, any][], ignoredPaths: IgnorePaths = [], cache?: WeakSet<object>): NonSerializableValue | false {\n  let foundNestedSerializable: NonSerializableValue | false;\n  if (!isSerializable(value)) {\n    return {\n      keyPath: path || '<root>',\n      value: value\n    };\n  }\n  if (typeof value !== 'object' || value === null) {\n    return false;\n  }\n  if (cache?.has(value)) return false;\n  const entries = getEntries != null ? getEntries(value) : Object.entries(value);\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (const [key, nestedValue] of entries) {\n    const nestedPath = path ? path + '.' + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some(ignored => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    if (!isSerializable(nestedValue)) {\n      return {\n        keyPath: nestedPath,\n        value: nestedValue\n      };\n    }\n    if (typeof nestedValue === 'object') {\n      foundNestedSerializable = findNonSerializableValue(nestedValue, nestedPath, isSerializable, getEntries, ignoredPaths, cache);\n      if (foundNestedSerializable) {\n        return foundNestedSerializable;\n      }\n    }\n  }\n  if (cache && isNestedFrozen(value)) cache.add(value);\n  return false;\n}\nexport function isNestedFrozen(value: object) {\n  if (!Object.isFrozen(value)) return false;\n  for (const nestedValue of Object.values(value)) {\n    if (typeof nestedValue !== 'object' || nestedValue === null) continue;\n    if (!isNestedFrozen(nestedValue)) return false;\n  }\n  return true;\n}\n\n/**\n * Options for `createSerializableStateInvariantMiddleware()`.\n *\n * @public\n */\nexport interface SerializableStateInvariantMiddlewareOptions {\n  /**\n   * The function to check if a value is considered serializable. This\n   * function is applied recursively to every value contained in the\n   * state. Defaults to `isPlain()`.\n   */\n  isSerializable?: (value: any) => boolean;\n  /**\n   * The function that will be used to retrieve entries from each\n   * value.  If unspecified, `Object.entries` will be used. Defaults\n   * to `undefined`.\n   */\n  getEntries?: (value: any) => [string, any][];\n\n  /**\n   * An array of action types to ignore when checking for serializability.\n   * Defaults to []\n   */\n  ignoredActions?: string[];\n\n  /**\n   * An array of dot-separated path strings or regular expressions to ignore\n   * when checking for serializability, Defaults to\n   * ['meta.arg', 'meta.baseQueryMeta']\n   */\n  ignoredActionPaths?: (string | RegExp)[];\n\n  /**\n   * An array of dot-separated path strings or regular expressions to ignore\n   * when checking for serializability, Defaults to []\n   */\n  ignoredPaths?: (string | RegExp)[];\n  /**\n   * Execution time warning threshold. If the middleware takes longer\n   * than `warnAfter` ms, a warning will be displayed in the console.\n   * Defaults to 32ms.\n   */\n  warnAfter?: number;\n\n  /**\n   * Opt out of checking state. When set to `true`, other state-related params will be ignored.\n   */\n  ignoreState?: boolean;\n\n  /**\n   * Opt out of checking actions. When set to `true`, other action-related params will be ignored.\n   */\n  ignoreActions?: boolean;\n\n  /**\n   * Opt out of caching the results. The cache uses a WeakSet and speeds up repeated checking processes.\n   * The cache is automatically disabled if no browser support for WeakSet is present.\n   */\n  disableCache?: boolean;\n}\n\n/**\n * Creates a middleware that, after every state change, checks if the new\n * state is serializable. If a non-serializable value is found within the\n * state, an error is printed to the console.\n *\n * @param options Middleware options.\n *\n * @public\n */\nexport function createSerializableStateInvariantMiddleware(options: SerializableStateInvariantMiddlewareOptions = {}): Middleware {\n  if (process.env.NODE_ENV === 'production') {\n    return () => next => action => next(action);\n  } else {\n    const {\n      isSerializable = isPlain,\n      getEntries,\n      ignoredActions = [],\n      ignoredActionPaths = ['meta.arg', 'meta.baseQueryMeta'],\n      ignoredPaths = [],\n      warnAfter = 32,\n      ignoreState = false,\n      ignoreActions = false,\n      disableCache = false\n    } = options;\n    const cache: WeakSet<object> | undefined = !disableCache && WeakSet ? new WeakSet() : undefined;\n    return storeAPI => next => action => {\n      if (!isAction(action)) {\n        return next(action);\n      }\n      const result = next(action);\n      const measureUtils = getTimeMeasureUtils(warnAfter, 'SerializableStateInvariantMiddleware');\n      if (!ignoreActions && !(ignoredActions.length && ignoredActions.indexOf(action.type as any) !== -1)) {\n        measureUtils.measureTime(() => {\n          const foundActionNonSerializableValue = findNonSerializableValue(action, '', isSerializable, getEntries, ignoredActionPaths, cache);\n          if (foundActionNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundActionNonSerializableValue;\n            console.error(`A non-serializable value was detected in an action, in the path: \\`${keyPath}\\`. Value:`, value, '\\nTake a look at the logic that dispatched this action: ', action, '\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)', '\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)');\n          }\n        });\n      }\n      if (!ignoreState) {\n        measureUtils.measureTime(() => {\n          const state = storeAPI.getState();\n          const foundStateNonSerializableValue = findNonSerializableValue(state, '', isSerializable, getEntries, ignoredPaths, cache);\n          if (foundStateNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundStateNonSerializableValue;\n            console.error(`A non-serializable value was detected in the state, in the path: \\`${keyPath}\\`. Value:`, value, `\nTake a look at the reducer(s) handling this action type: ${action.type}.\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n      }\n      return result;\n    };\n  }\n}", "import type { StoreEnhancer } from 'redux';\nexport const SHOULD_AUTOBATCH = 'RTK_autoBatch';\nexport const prepareAutoBatched = <T,>() => (payload: T): {\n  payload: T;\n  meta: unknown;\n} => ({\n  payload,\n  meta: {\n    [SHOULD_AUTOBATCH]: true\n  }\n});\nconst createQueueWithTimer = (timeout: number) => {\n  return (notify: () => void) => {\n    setTimeout(notify, timeout);\n  };\n};\nexport type AutoBatchOptions = {\n  type: 'tick';\n} | {\n  type: 'timer';\n  timeout: number;\n} | {\n  type: 'raf';\n} | {\n  type: 'callback';\n  queueNotification: (notify: () => void) => void;\n};\n\n/**\n * A Redux store enhancer that watches for \"low-priority\" actions, and delays\n * notifying subscribers until either the queued callback executes or the\n * next \"standard-priority\" action is dispatched.\n *\n * This allows dispatching multiple \"low-priority\" actions in a row with only\n * a single subscriber notification to the UI after the sequence of actions\n * is finished, thus improving UI re-render performance.\n *\n * Watches for actions with the `action.meta[SHOULD_AUTOBATCH]` attribute.\n * This can be added to `action.meta` manually, or by using the\n * `prepareAutoBatched` helper.\n *\n * By default, it will queue a notification for the end of the event loop tick.\n * However, you can pass several other options to configure the behavior:\n * - `{type: 'tick'}`: queues using `queueMicrotask`\n * - `{type: 'timer', timeout: number}`: queues using `setTimeout`\n * - `{type: 'raf'}`: queues using `requestAnimationFrame` (default)\n * - `{type: 'callback', queueNotification: (notify: () => void) => void}`: lets you provide your own callback\n *\n *\n */\nexport const autoBatchEnhancer = (options: AutoBatchOptions = {\n  type: 'raf'\n}): StoreEnhancer => next => (...args) => {\n  const store = next(...args);\n  let notifying = true;\n  let shouldNotifyAtEndOfTick = false;\n  let notificationQueued = false;\n  const listeners = new Set<() => void>();\n  const queueCallback = options.type === 'tick' ? queueMicrotask : options.type === 'raf' ?\n  // requestAnimationFrame won't exist in SSR environments. Fall back to a vague approximation just to keep from erroring.\n  typeof window !== 'undefined' && window.requestAnimationFrame ? window.requestAnimationFrame : createQueueWithTimer(10) : options.type === 'callback' ? options.queueNotification : createQueueWithTimer(options.timeout);\n  const notifyListeners = () => {\n    // We're running at the end of the event loop tick.\n    // Run the real listener callbacks to actually update the UI.\n    notificationQueued = false;\n    if (shouldNotifyAtEndOfTick) {\n      shouldNotifyAtEndOfTick = false;\n      listeners.forEach(l => l());\n    }\n  };\n  return Object.assign({}, store, {\n    // Override the base `store.subscribe` method to keep original listeners\n    // from running if we're delaying notifications\n    subscribe(listener: () => void) {\n      // Each wrapped listener will only call the real listener if\n      // the `notifying` flag is currently active when it's called.\n      // This lets the base store work as normal, while the actual UI\n      // update becomes controlled by this enhancer.\n      const wrappedListener: typeof listener = () => notifying && listener();\n      const unsubscribe = store.subscribe(wrappedListener);\n      listeners.add(listener);\n      return () => {\n        unsubscribe();\n        listeners.delete(listener);\n      };\n    },\n    // Override the base `store.dispatch` method so that we can check actions\n    // for the `shouldAutoBatch` flag and determine if batching is active\n    dispatch(action: any) {\n      try {\n        // If the action does _not_ have the `shouldAutoBatch` flag,\n        // we resume/continue normal notify-after-each-dispatch behavior\n        notifying = !action?.meta?.[SHOULD_AUTOBATCH];\n        // If a `notifyListeners` microtask was queued, you can't cancel it.\n        // Instead, we set a flag so that it's a no-op when it does run\n        shouldNotifyAtEndOfTick = !notifying;\n        if (shouldNotifyAtEndOfTick) {\n          // We've seen at least 1 action with `SHOULD_AUTOBATCH`. Try to queue\n          // a microtask to notify listeners at the end of the event loop tick.\n          // Make sure we only enqueue this _once_ per tick.\n          if (!notificationQueued) {\n            notificationQueued = true;\n            queueCallback(notifyListeners);\n          }\n        }\n        // Go ahead and process the action as usual, including reducers.\n        // If normal notification behavior is enabled, the store will notify\n        // all of its own listeners, and the wrapper callbacks above will\n        // see `notifying` is true and pass on to the real listener callbacks.\n        // If we're \"batching\" behavior, then the wrapped callbacks will\n        // bail out, causing the base store notification behavior to be no-ops.\n        return store.dispatch(action);\n      } finally {\n        // Assume we're back to normal behavior after each action\n        notifying = true;\n      }\n    }\n  });\n};", "import type { StoreEnhancer } from 'redux';\nimport type { AutoBatchOptions } from './autoBatchEnhancer';\nimport { autoBatchEnhancer } from './autoBatchEnhancer';\nimport { Tuple } from './utils';\nimport type { Middlewares } from './configureStore';\nimport type { ExtractDispatchExtensions } from './tsHelpers';\ntype GetDefaultEnhancersOptions = {\n  autoBatch?: boolean | AutoBatchOptions;\n};\nexport type GetDefaultEnhancers<M extends Middlewares<any>> = (options?: GetDefaultEnhancersOptions) => Tuple<[StoreEnhancer<{\n  dispatch: ExtractDispatchExtensions<M>;\n}>]>;\nexport const buildGetDefaultEnhancers = <M extends Middlewares<any>,>(middlewareEnhancer: StoreEnhancer<{\n  dispatch: ExtractDispatchExtensions<M>;\n}>): GetDefaultEnhancers<M> => function getDefaultEnhancers(options) {\n  const {\n    autoBatch = true\n  } = options ?? {};\n  let enhancerArray = new Tuple<StoreEnhancer[]>(middlewareEnhancer);\n  if (autoBatch) {\n    enhancerArray.push(autoBatchEnhancer(typeof autoBatch === 'object' ? autoBatch : undefined));\n  }\n  return enhancerArray as any;\n};", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport type { Draft } from 'immer';\nimport { produce as createNextState, isDraft, isDraftable } from 'immer';\nimport type { Action, Reducer, UnknownAction } from 'redux';\nimport type { ActionReducerMapBuilder } from './mapBuilders';\nimport { executeReducerBuilderCallback } from './mapBuilders';\nimport type { NoInfer, TypeGuard } from './tsHelpers';\nimport { freezeDraftable } from './utils';\n\n/**\n * Defines a mapping from action types to corresponding action object shapes.\n *\n * @deprecated This should not be used manually - it is only used for internal\n *             inference purposes and should not have any further value.\n *             It might be removed in the future.\n * @public\n */\nexport type Actions<T extends keyof any = string> = Record<T, Action>;\nexport type ActionMatcherDescription<S, A extends Action> = {\n  matcher: TypeGuard<A>;\n  reducer: CaseReducer<S, NoInfer<A>>;\n};\nexport type ReadonlyActionMatcherDescriptionCollection<S> = ReadonlyArray<ActionMatcherDescription<S, any>>;\nexport type ActionMatcherDescriptionCollection<S> = Array<ActionMatcherDescription<S, any>>;\n\n/**\n * A *case reducer* is a reducer function for a specific action type. Case\n * reducers can be composed to full reducers using `createReducer()`.\n *\n * Unlike a normal Redux reducer, a case reducer is never called with an\n * `undefined` state to determine the initial state. Instead, the initial\n * state is explicitly specified as an argument to `createReducer()`.\n *\n * In addition, a case reducer can choose to mutate the passed-in `state`\n * value directly instead of returning a new state. This does not actually\n * cause the store state to be mutated directly; instead, thanks to\n * [immer](https://github.com/mweststrate/immer), the mutations are\n * translated to copy operations that result in a new state.\n *\n * @public\n */\nexport type CaseReducer<S = any, A extends Action = UnknownAction> = (state: Draft<S>, action: A) => NoInfer<S> | void | Draft<NoInfer<S>>;\n\n/**\n * A mapping from action types to case reducers for `createReducer()`.\n *\n * @deprecated This should not be used manually - it is only used\n *             for internal inference purposes and using it manually\n *             would lead to type erasure.\n *             It might be removed in the future.\n * @public\n */\nexport type CaseReducers<S, AS extends Actions> = { [T in keyof AS]: AS[T] extends Action ? CaseReducer<S, AS[T]> : void };\nexport type NotFunction<T> = T extends Function ? never : T;\nfunction isStateFunction<S>(x: unknown): x is () => S {\n  return typeof x === 'function';\n}\nexport type ReducerWithInitialState<S extends NotFunction<any>> = Reducer<S> & {\n  getInitialState: () => S;\n};\n\n/**\n * A utility function that allows defining a reducer as a mapping from action\n * type to *case reducer* functions that handle these action types. The\n * reducer's initial state is passed as the first argument.\n *\n * @remarks\n * The body of every case reducer is implicitly wrapped with a call to\n * `produce()` from the [immer](https://github.com/mweststrate/immer) library.\n * This means that rather than returning a new state object, you can also\n * mutate the passed-in state object directly; these mutations will then be\n * automatically and efficiently translated into copies, giving you both\n * convenience and immutability.\n *\n * @overloadSummary\n * This function accepts a callback that receives a `builder` object as its argument.\n * That builder provides `addCase`, `addMatcher` and `addDefaultCase` functions that may be\n * called to define what actions this reducer will handle.\n *\n * @param initialState - `State | (() => State)`: The initial state that should be used when the reducer is called the first time. This may also be a \"lazy initializer\" function, which should return an initial state value when called. This will be used whenever the reducer is called with `undefined` as its state value, and is primarily useful for cases like reading initial state from `localStorage`.\n * @param builderCallback - `(builder: Builder) => void` A callback that receives a *builder* object to define\n *   case reducers via calls to `builder.addCase(actionCreatorOrType, reducer)`.\n * @example\n```ts\nimport {\n  createAction,\n  createReducer,\n  UnknownAction,\n  PayloadAction,\n} from \"@reduxjs/toolkit\";\n\nconst increment = createAction<number>(\"increment\");\nconst decrement = createAction<number>(\"decrement\");\n\nfunction isActionWithNumberPayload(\n  action: UnknownAction\n): action is PayloadAction<number> {\n  return typeof action.payload === \"number\";\n}\n\nconst reducer = createReducer(\n  {\n    counter: 0,\n    sumOfNumberPayloads: 0,\n    unhandledActions: 0,\n  },\n  (builder) => {\n    builder\n      .addCase(increment, (state, action) => {\n        // action is inferred correctly here\n        state.counter += action.payload;\n      })\n      // You can chain calls, or have separate `builder.addCase()` lines each time\n      .addCase(decrement, (state, action) => {\n        state.counter -= action.payload;\n      })\n      // You can apply a \"matcher function\" to incoming actions\n      .addMatcher(isActionWithNumberPayload, (state, action) => {})\n      // and provide a default case if no other handlers matched\n      .addDefaultCase((state, action) => {});\n  }\n);\n```\n * @public\n */\nexport function createReducer<S extends NotFunction<any>>(initialState: S | (() => S), mapOrBuilderCallback: (builder: ActionReducerMapBuilder<S>) => void): ReducerWithInitialState<S> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof mapOrBuilderCallback === 'object') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(8) : \"The object notation for `createReducer` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\");\n    }\n  }\n  let [actionsMap, finalActionMatchers, finalDefaultCaseReducer] = executeReducerBuilderCallback(mapOrBuilderCallback);\n\n  // Ensure the initial state gets frozen either way (if draftable)\n  let getInitialState: () => S;\n  if (isStateFunction(initialState)) {\n    getInitialState = () => freezeDraftable(initialState());\n  } else {\n    const frozenInitialState = freezeDraftable(initialState);\n    getInitialState = () => frozenInitialState;\n  }\n  function reducer(state = getInitialState(), action: any): S {\n    let caseReducers = [actionsMap[action.type], ...finalActionMatchers.filter(({\n      matcher\n    }) => matcher(action)).map(({\n      reducer\n    }) => reducer)];\n    if (caseReducers.filter(cr => !!cr).length === 0) {\n      caseReducers = [finalDefaultCaseReducer];\n    }\n    return caseReducers.reduce((previousState, caseReducer): S => {\n      if (caseReducer) {\n        if (isDraft(previousState)) {\n          // If it's already a draft, we must already be inside a `createNextState` call,\n          // likely because this is being wrapped in `createReducer`, `createSlice`, or nested\n          // inside an existing draft. It's safe to just pass the draft to the mutator.\n          const draft = previousState as Draft<S>; // We can assume this is already a draft\n          const result = caseReducer(draft, action);\n          if (result === undefined) {\n            return previousState;\n          }\n          return result as S;\n        } else if (!isDraftable(previousState)) {\n          // If state is not draftable (ex: a primitive, such as 0), we want to directly\n          // return the caseReducer func and not wrap it with produce.\n          const result = caseReducer(previousState as any, action);\n          if (result === undefined) {\n            if (previousState === null) {\n              return previousState;\n            }\n            throw Error('A case reducer on a non-draftable value must not return undefined');\n          }\n          return result as S;\n        } else {\n          // @ts-ignore createNextState() produces an Immutable<Draft<S>> rather\n          // than an Immutable<S>, and TypeScript cannot find out how to reconcile\n          // these two types.\n          return createNextState(previousState, (draft: Draft<S>) => {\n            return caseReducer(draft, action);\n          });\n        }\n      }\n      return previousState;\n    }, state);\n  }\n  reducer.getInitialState = getInitialState;\n  return reducer as ReducerWithInitialState<S>;\n}", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2, formatProdErrorMessage as _formatProdErrorMessage3, formatProdErrorMessage as _formatProdErrorMessage4, formatProdErrorMessage as _formatProdErrorMessage5, formatProdErrorMessage as _formatProdErrorMessage6 } from \"@reduxjs/toolkit\";\nimport type { Action } from 'redux';\nimport type { CaseReducer, CaseReducers, ActionMatcherDescriptionCollection } from './createReducer';\nimport type { TypeGuard } from './tsHelpers';\nexport type TypedActionCreator<Type extends string> = {\n  (...args: any[]): Action<Type>;\n  type: Type;\n};\n\n/**\n * A builder for an action <-> reducer map.\n *\n * @public\n */\nexport interface ActionReducerMapBuilder<State> {\n  /**\n   * Adds a case reducer to handle a single exact action type.\n   * @remarks\n   * All calls to `builder.addCase` must come before any calls to `builder.addMatcher` or `builder.addDefaultCase`.\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\n   * @param reducer - The actual case reducer function.\n   */\n  addCase<ActionCreator extends TypedActionCreator<string>>(actionCreator: ActionCreator, reducer: CaseReducer<State, ReturnType<ActionCreator>>): ActionReducerMapBuilder<State>;\n  /**\n   * Adds a case reducer to handle a single exact action type.\n   * @remarks\n   * All calls to `builder.addCase` must come before any calls to `builder.addMatcher` or `builder.addDefaultCase`.\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\n   * @param reducer - The actual case reducer function.\n   */\n  addCase<Type extends string, A extends Action<Type>>(type: Type, reducer: CaseReducer<State, A>): ActionReducerMapBuilder<State>;\n\n  /**\n   * Allows you to match your incoming actions against your own filter function instead of only the `action.type` property.\n   * @remarks\n   * If multiple matcher reducers match, all of them will be executed in the order\n   * they were defined in - even if a case reducer already matched.\n   * All calls to `builder.addMatcher` must come after any calls to `builder.addCase` and before any calls to `builder.addDefaultCase`.\n   * @param matcher - A matcher function. In TypeScript, this should be a [type predicate](https://www.typescriptlang.org/docs/handbook/2/narrowing.html#using-type-predicates)\n   *   function\n   * @param reducer - The actual case reducer function.\n   *\n   * @example\n  ```ts\n  import {\n  createAction,\n  createReducer,\n  AsyncThunk,\n  UnknownAction,\n  } from \"@reduxjs/toolkit\";\n  type GenericAsyncThunk = AsyncThunk<unknown, unknown, any>;\n  type PendingAction = ReturnType<GenericAsyncThunk[\"pending\"]>;\n  type RejectedAction = ReturnType<GenericAsyncThunk[\"rejected\"]>;\n  type FulfilledAction = ReturnType<GenericAsyncThunk[\"fulfilled\"]>;\n  const initialState: Record<string, string> = {};\n  const resetAction = createAction(\"reset-tracked-loading-state\");\n  function isPendingAction(action: UnknownAction): action is PendingAction {\n  return typeof action.type === \"string\" && action.type.endsWith(\"/pending\");\n  }\n  const reducer = createReducer(initialState, (builder) => {\n  builder\n    .addCase(resetAction, () => initialState)\n    // matcher can be defined outside as a type predicate function\n    .addMatcher(isPendingAction, (state, action) => {\n      state[action.meta.requestId] = \"pending\";\n    })\n    .addMatcher(\n      // matcher can be defined inline as a type predicate function\n      (action): action is RejectedAction => action.type.endsWith(\"/rejected\"),\n      (state, action) => {\n        state[action.meta.requestId] = \"rejected\";\n      }\n    )\n    // matcher can just return boolean and the matcher can receive a generic argument\n    .addMatcher<FulfilledAction>(\n      (action) => action.type.endsWith(\"/fulfilled\"),\n      (state, action) => {\n        state[action.meta.requestId] = \"fulfilled\";\n      }\n    );\n  });\n  ```\n   */\n  addMatcher<A>(matcher: TypeGuard<A> | ((action: any) => boolean), reducer: CaseReducer<State, A extends Action ? A : A & Action>): Omit<ActionReducerMapBuilder<State>, 'addCase'>;\n\n  /**\n   * Adds a \"default case\" reducer that is executed if no case reducer and no matcher\n   * reducer was executed for this action.\n   * @param reducer - The fallback \"default case\" reducer function.\n   *\n   * @example\n  ```ts\n  import { createReducer } from '@reduxjs/toolkit'\n  const initialState = { otherActions: 0 }\n  const reducer = createReducer(initialState, builder => {\n  builder\n    // .addCase(...)\n    // .addMatcher(...)\n    .addDefaultCase((state, action) => {\n      state.otherActions++\n    })\n  })\n  ```\n   */\n  addDefaultCase(reducer: CaseReducer<State, Action>): {};\n}\nexport function executeReducerBuilderCallback<S>(builderCallback: (builder: ActionReducerMapBuilder<S>) => void): [CaseReducers<S, any>, ActionMatcherDescriptionCollection<S>, CaseReducer<S, Action> | undefined] {\n  const actionsMap: CaseReducers<S, any> = {};\n  const actionMatchers: ActionMatcherDescriptionCollection<S> = [];\n  let defaultCaseReducer: CaseReducer<S, Action> | undefined;\n  const builder = {\n    addCase(typeOrActionCreator: string | TypedActionCreator<any>, reducer: CaseReducer<S>) {\n      if (process.env.NODE_ENV !== 'production') {\n        /*\n         to keep the definition by the user in line with actual behavior,\n         we enforce `addCase` to always be called before calling `addMatcher`\n         as matching cases take precedence over matchers\n         */\n        if (actionMatchers.length > 0) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(26) : '`builder.addCase` should only be called before calling `builder.addMatcher`');\n        }\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(27) : '`builder.addCase` should only be called before calling `builder.addDefaultCase`');\n        }\n      }\n      const type = typeof typeOrActionCreator === 'string' ? typeOrActionCreator : typeOrActionCreator.type;\n      if (!type) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(28) : '`builder.addCase` cannot be called with an empty action type');\n      }\n      if (type in actionsMap) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage4(29) : '`builder.addCase` cannot be called with two reducers for the same action type ' + `'${type}'`);\n      }\n      actionsMap[type] = reducer;\n      return builder;\n    },\n    addMatcher<A>(matcher: TypeGuard<A>, reducer: CaseReducer<S, A extends Action ? A : A & Action>) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage5(30) : '`builder.addMatcher` should only be called before calling `builder.addDefaultCase`');\n        }\n      }\n      actionMatchers.push({\n        matcher,\n        reducer\n      });\n      return builder;\n    },\n    addDefaultCase(reducer: CaseReducer<S, Action>) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage6(31) : '`builder.addDefaultCase` can only be called once');\n        }\n      }\n      defaultCaseReducer = reducer;\n      return builder;\n    }\n  };\n  builderCallback(builder);\n  return [actionsMap, actionMatchers, defaultCaseReducer];\n}", "import type { Action<PERSON>romMatcher, Matcher, UnionToIntersection } from './tsHelpers';\nimport { hasMatchFunction } from './tsHelpers';\nimport type { AsyncThunk, AsyncThunkFulfilledActionCreator, AsyncThunkPendingActionCreator, AsyncThunkRejectedActionCreator } from './createAsyncThunk';\n\n/** @public */\nexport type ActionMatchingAnyOf<Matchers extends Matcher<any>[]> = ActionFromMatcher<Matchers[number]>;\n\n/** @public */\nexport type ActionMatchingAllOf<Matchers extends Matcher<any>[]> = UnionToIntersection<ActionMatchingAnyOf<Matchers>>;\nconst matches = (matcher: Matcher<any>, action: any) => {\n  if (hasMatchFunction(matcher)) {\n    return matcher.match(action);\n  } else {\n    return matcher(action);\n  }\n};\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action matches any one of the supplied type guards or action\n * creators.\n *\n * @param matchers The type guards or action creators to match against.\n *\n * @public\n */\nexport function isAnyOf<Matchers extends Matcher<any>[]>(...matchers: Matchers) {\n  return (action: any): action is ActionMatchingAnyOf<Matchers> => {\n    return matchers.some(matcher => matches(matcher, action));\n  };\n}\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action matches all of the supplied type guards or action\n * creators.\n *\n * @param matchers The type guards or action creators to match against.\n *\n * @public\n */\nexport function isAllOf<Matchers extends Matcher<any>[]>(...matchers: Matchers) {\n  return (action: any): action is ActionMatchingAllOf<Matchers> => {\n    return matchers.every(matcher => matches(matcher, action));\n  };\n}\n\n/**\n * @param action A redux action\n * @param validStatus An array of valid meta.requestStatus values\n *\n * @internal\n */\nexport function hasExpectedRequestMetadata(action: any, validStatus: readonly string[]) {\n  if (!action || !action.meta) return false;\n  const hasValidRequestId = typeof action.meta.requestId === 'string';\n  const hasValidRequestStatus = validStatus.indexOf(action.meta.requestStatus) > -1;\n  return hasValidRequestId && hasValidRequestStatus;\n}\nfunction isAsyncThunkArray(a: [any] | AnyAsyncThunk[]): a is AnyAsyncThunk[] {\n  return typeof a[0] === 'function' && 'pending' in a[0] && 'fulfilled' in a[0] && 'rejected' in a[0];\n}\nexport type UnknownAsyncThunkPendingAction = ReturnType<AsyncThunkPendingActionCreator<unknown>>;\nexport type PendingActionFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['pending']>;\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator, and that\n * the action is pending.\n *\n * @public\n */\nexport function isPending(): (action: any) => action is UnknownAsyncThunkPendingAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators,\n * and that the action is pending.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isPending<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is PendingActionFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a pending thunk action\n * @public\n */\nexport function isPending(action: any): action is UnknownAsyncThunkPendingAction;\nexport function isPending<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  if (asyncThunks.length === 0) {\n    return (action: any) => hasExpectedRequestMetadata(action, ['pending']);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isPending()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.pending));\n}\nexport type UnknownAsyncThunkRejectedAction = ReturnType<AsyncThunkRejectedActionCreator<unknown, unknown>>;\nexport type RejectedActionFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['rejected']>;\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator, and that\n * the action is rejected.\n *\n * @public\n */\nexport function isRejected(): (action: any) => action is UnknownAsyncThunkRejectedAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators,\n * and that the action is rejected.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isRejected<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is RejectedActionFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a rejected thunk action\n * @public\n */\nexport function isRejected(action: any): action is UnknownAsyncThunkRejectedAction;\nexport function isRejected<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  if (asyncThunks.length === 0) {\n    return (action: any) => hasExpectedRequestMetadata(action, ['rejected']);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejected()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.rejected));\n}\nexport type UnknownAsyncThunkRejectedWithValueAction = ReturnType<AsyncThunkRejectedActionCreator<unknown, unknown>>;\nexport type RejectedWithValueActionFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['rejected']> & (T extends AsyncThunk<any, any, {\n  rejectValue: infer RejectedValue;\n}> ? {\n  payload: RejectedValue;\n} : unknown);\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator, and that\n * the action is rejected with value.\n *\n * @public\n */\nexport function isRejectedWithValue(): (action: any) => action is UnknownAsyncThunkRejectedAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators,\n * and that the action is rejected with value.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isRejectedWithValue<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is RejectedWithValueActionFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a rejected thunk action with value\n * @public\n */\nexport function isRejectedWithValue(action: any): action is UnknownAsyncThunkRejectedAction;\nexport function isRejectedWithValue<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  const hasFlag = (action: any): action is any => {\n    return action && action.meta && action.meta.rejectedWithValue;\n  };\n  if (asyncThunks.length === 0) {\n    return isAllOf(isRejected(...asyncThunks), hasFlag);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejectedWithValue()(asyncThunks[0]);\n  }\n  return isAllOf(isRejected(...asyncThunks), hasFlag);\n}\nexport type UnknownAsyncThunkFulfilledAction = ReturnType<AsyncThunkFulfilledActionCreator<unknown, unknown>>;\nexport type FulfilledActionFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['fulfilled']>;\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator, and that\n * the action is fulfilled.\n *\n * @public\n */\nexport function isFulfilled(): (action: any) => action is UnknownAsyncThunkFulfilledAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators,\n * and that the action is fulfilled.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isFulfilled<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is FulfilledActionFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a fulfilled thunk action\n * @public\n */\nexport function isFulfilled(action: any): action is UnknownAsyncThunkFulfilledAction;\nexport function isFulfilled<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  if (asyncThunks.length === 0) {\n    return (action: any) => hasExpectedRequestMetadata(action, ['fulfilled']);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isFulfilled()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.fulfilled));\n}\nexport type UnknownAsyncThunkAction = UnknownAsyncThunkPendingAction | UnknownAsyncThunkRejectedAction | UnknownAsyncThunkFulfilledAction;\nexport type AnyAsyncThunk = {\n  pending: {\n    match: (action: any) => action is any;\n  };\n  fulfilled: {\n    match: (action: any) => action is any;\n  };\n  rejected: {\n    match: (action: any) => action is any;\n  };\n};\nexport type ActionsFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['pending']> | ActionFromMatcher<T['fulfilled']> | ActionFromMatcher<T['rejected']>;\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator.\n *\n * @public\n */\nexport function isAsyncThunkAction(): (action: any) => action is UnknownAsyncThunkAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isAsyncThunkAction<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is ActionsFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a thunk action\n * @public\n */\nexport function isAsyncThunkAction(action: any): action is UnknownAsyncThunkAction;\nexport function isAsyncThunkAction<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  if (asyncThunks.length === 0) {\n    return (action: any) => hasExpectedRequestMetadata(action, ['pending', 'fulfilled', 'rejected']);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isAsyncThunkAction()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.flatMap(asyncThunk => [asyncThunk.pending, asyncThunk.rejected, asyncThunk.fulfilled]));\n}", "// Borrowed from https://github.com/ai/nanoid/blob/3.0.2/non-secure/index.js\n// This alphabet uses `A-Za-z0-9_-` symbols. A genetic algorithm helped\n// optimize the gzip compression for this alphabet.\nlet urlAlphabet = 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';\n\n/**\r\n *\r\n * @public\r\n */\nexport let nanoid = (size = 21) => {\n  let id = '';\n  // A compact alternative for `for (var i = 0; i < step; i++)`.\n  let i = size;\n  while (i--) {\n    // `| 0` is more compact and faster than `Math.floor()`.\n    id += urlAlphabet[Math.random() * 64 | 0];\n  }\n  return id;\n};", "import type { Dispatch, UnknownAction } from 'redux';\nimport type { ThunkDispatch } from 'redux-thunk';\nimport type { ActionCreatorWithPreparedPayload } from './createAction';\nimport { createAction } from './createAction';\nimport { isAnyOf } from './matchers';\nimport { nanoid } from './nanoid';\nimport type { FallbackIfUnknown, Id, IsAny, IsUnknown, SafePromise } from './tsHelpers';\nexport type BaseThunkAPI<S, E, D extends Dispatch = Dispatch, RejectedValue = unknown, RejectedMeta = unknown, FulfilledMeta = unknown> = {\n  dispatch: D;\n  getState: () => S;\n  extra: E;\n  requestId: string;\n  signal: AbortSignal;\n  abort: (reason?: string) => void;\n  rejectWithValue: IsUnknown<RejectedMeta, (value: RejectedValue) => RejectWithValue<RejectedValue, RejectedMeta>, (value: RejectedValue, meta: RejectedMeta) => RejectWithValue<RejectedValue, RejectedMeta>>;\n  fulfillWithValue: IsUnknown<FulfilledMeta, <FulfilledValue>(value: FulfilledValue) => FulfilledValue, <FulfilledValue>(value: FulfilledValue, meta: FulfilledMeta) => FulfillWithMeta<FulfilledValue, FulfilledMeta>>;\n};\n\n/**\n * @public\n */\nexport interface SerializedError {\n  name?: string;\n  message?: string;\n  stack?: string;\n  code?: string;\n}\nconst commonProperties: Array<keyof SerializedError> = ['name', 'message', 'stack', 'code'];\nclass RejectWithValue<Payload, RejectedMeta> {\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  private readonly _type!: 'RejectWithValue';\n  constructor(public readonly payload: Payload, public readonly meta: RejectedMeta) {}\n}\nclass FulfillWithMeta<Payload, FulfilledMeta> {\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  private readonly _type!: 'FulfillWithMeta';\n  constructor(public readonly payload: Payload, public readonly meta: FulfilledMeta) {}\n}\n\n/**\n * Serializes an error into a plain object.\n * Reworked from https://github.com/sindresorhus/serialize-error\n *\n * @public\n */\nexport const miniSerializeError = (value: any): SerializedError => {\n  if (typeof value === 'object' && value !== null) {\n    const simpleError: SerializedError = {};\n    for (const property of commonProperties) {\n      if (typeof value[property] === 'string') {\n        simpleError[property] = value[property];\n      }\n    }\n    return simpleError;\n  }\n  return {\n    message: String(value)\n  };\n};\nexport type AsyncThunkConfig = {\n  state?: unknown;\n  dispatch?: ThunkDispatch<unknown, unknown, UnknownAction>;\n  extra?: unknown;\n  rejectValue?: unknown;\n  serializedErrorType?: unknown;\n  pendingMeta?: unknown;\n  fulfilledMeta?: unknown;\n  rejectedMeta?: unknown;\n};\nexport type GetState<ThunkApiConfig> = ThunkApiConfig extends {\n  state: infer State;\n} ? State : unknown;\ntype GetExtra<ThunkApiConfig> = ThunkApiConfig extends {\n  extra: infer Extra;\n} ? Extra : unknown;\ntype GetDispatch<ThunkApiConfig> = ThunkApiConfig extends {\n  dispatch: infer Dispatch;\n} ? FallbackIfUnknown<Dispatch, ThunkDispatch<GetState<ThunkApiConfig>, GetExtra<ThunkApiConfig>, UnknownAction>> : ThunkDispatch<GetState<ThunkApiConfig>, GetExtra<ThunkApiConfig>, UnknownAction>;\nexport type GetThunkAPI<ThunkApiConfig> = BaseThunkAPI<GetState<ThunkApiConfig>, GetExtra<ThunkApiConfig>, GetDispatch<ThunkApiConfig>, GetRejectValue<ThunkApiConfig>, GetRejectedMeta<ThunkApiConfig>, GetFulfilledMeta<ThunkApiConfig>>;\ntype GetRejectValue<ThunkApiConfig> = ThunkApiConfig extends {\n  rejectValue: infer RejectValue;\n} ? RejectValue : unknown;\ntype GetPendingMeta<ThunkApiConfig> = ThunkApiConfig extends {\n  pendingMeta: infer PendingMeta;\n} ? PendingMeta : unknown;\ntype GetFulfilledMeta<ThunkApiConfig> = ThunkApiConfig extends {\n  fulfilledMeta: infer FulfilledMeta;\n} ? FulfilledMeta : unknown;\ntype GetRejectedMeta<ThunkApiConfig> = ThunkApiConfig extends {\n  rejectedMeta: infer RejectedMeta;\n} ? RejectedMeta : unknown;\ntype GetSerializedErrorType<ThunkApiConfig> = ThunkApiConfig extends {\n  serializedErrorType: infer GetSerializedErrorType;\n} ? GetSerializedErrorType : SerializedError;\ntype MaybePromise<T> = T | Promise<T> | (T extends any ? Promise<T> : never);\n\n/**\n * A type describing the return value of the `payloadCreator` argument to `createAsyncThunk`.\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\n *\n * @public\n */\nexport type AsyncThunkPayloadCreatorReturnValue<Returned, ThunkApiConfig extends AsyncThunkConfig> = MaybePromise<IsUnknown<GetFulfilledMeta<ThunkApiConfig>, Returned, FulfillWithMeta<Returned, GetFulfilledMeta<ThunkApiConfig>>> | RejectWithValue<GetRejectValue<ThunkApiConfig>, GetRejectedMeta<ThunkApiConfig>>>;\n/**\n * A type describing the `payloadCreator` argument to `createAsyncThunk`.\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\n *\n * @public\n */\nexport type AsyncThunkPayloadCreator<Returned, ThunkArg = void, ThunkApiConfig extends AsyncThunkConfig = {}> = (arg: ThunkArg, thunkAPI: GetThunkAPI<ThunkApiConfig>) => AsyncThunkPayloadCreatorReturnValue<Returned, ThunkApiConfig>;\n\n/**\n * A ThunkAction created by `createAsyncThunk`.\n * Dispatching it returns a Promise for either a\n * fulfilled or rejected action.\n * Also, the returned value contains an `abort()` method\n * that allows the asyncAction to be cancelled from the outside.\n *\n * @public\n */\nexport type AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig> = (dispatch: NonNullable<GetDispatch<ThunkApiConfig>>, getState: () => GetState<ThunkApiConfig>, extra: GetExtra<ThunkApiConfig>) => SafePromise<ReturnType<AsyncThunkFulfilledActionCreator<Returned, ThunkArg>> | ReturnType<AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig>>> & {\n  abort: (reason?: string) => void;\n  requestId: string;\n  arg: ThunkArg;\n  unwrap: () => Promise<Returned>;\n};\n\n/**\n * Config provided when calling the async thunk action creator.\n */\nexport interface AsyncThunkDispatchConfig {\n  /**\n   * An external `AbortSignal` that will be tracked by the internal `AbortSignal`.\n   */\n  signal?: AbortSignal;\n}\ntype AsyncThunkActionCreator<Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig> = IsAny<ThunkArg,\n// any handling\n(arg: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>,\n// unknown handling\nunknown extends ThunkArg ? (arg: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument not specified or specified as void or undefined\n: [ThunkArg] extends [void] | [undefined] ? (arg?: undefined, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument contains void\n: [void] extends [ThunkArg] // make optional\n? (arg?: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument contains undefined\n: [undefined] extends [ThunkArg] ? WithStrictNullChecks<\n// with strict nullChecks: make optional\n(arg?: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>,\n// without strict null checks this will match everything, so don't make it optional\n(arg: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>> // default case: normal argument\n: (arg: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>>;\n\n/**\n * Options object for `createAsyncThunk`.\n *\n * @public\n */\nexport type AsyncThunkOptions<ThunkArg = void, ThunkApiConfig extends AsyncThunkConfig = {}> = {\n  /**\n   * A method to control whether the asyncThunk should be executed. Has access to the\n   * `arg`, `api.getState()` and `api.extra` arguments.\n   *\n   * @returns `false` if it should be skipped\n   */\n  condition?(arg: ThunkArg, api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>): MaybePromise<boolean | undefined>;\n  /**\n   * If `condition` returns `false`, the asyncThunk will be skipped.\n   * This option allows you to control whether a `rejected` action with `meta.condition == false`\n   * will be dispatched or not.\n   *\n   * @default `false`\n   */\n  dispatchConditionRejection?: boolean;\n  serializeError?: (x: unknown) => GetSerializedErrorType<ThunkApiConfig>;\n\n  /**\n   * A function to use when generating the `requestId` for the request sequence.\n   *\n   * @default `nanoid`\n   */\n  idGenerator?: (arg: ThunkArg) => string;\n} & IsUnknown<GetPendingMeta<ThunkApiConfig>, {\n  /**\n   * A method to generate additional properties to be added to `meta` of the pending action.\n   *\n   * Using this optional overload will not modify the types correctly, this overload is only in place to support JavaScript users.\n   * Please use the `ThunkApiConfig` parameter `pendingMeta` to get access to a correctly typed overload\n   */\n  getPendingMeta?(base: {\n    arg: ThunkArg;\n    requestId: string;\n  }, api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>): GetPendingMeta<ThunkApiConfig>;\n}, {\n  /**\n   * A method to generate additional properties to be added to `meta` of the pending action.\n   */\n  getPendingMeta(base: {\n    arg: ThunkArg;\n    requestId: string;\n  }, api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>): GetPendingMeta<ThunkApiConfig>;\n}>;\nexport type AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig = {}> = ActionCreatorWithPreparedPayload<[string, ThunkArg, GetPendingMeta<ThunkApiConfig>?], undefined, string, never, {\n  arg: ThunkArg;\n  requestId: string;\n  requestStatus: 'pending';\n} & GetPendingMeta<ThunkApiConfig>>;\nexport type AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig = {}> = ActionCreatorWithPreparedPayload<[Error | null, string, ThunkArg, GetRejectValue<ThunkApiConfig>?, GetRejectedMeta<ThunkApiConfig>?], GetRejectValue<ThunkApiConfig> | undefined, string, GetSerializedErrorType<ThunkApiConfig>, {\n  arg: ThunkArg;\n  requestId: string;\n  requestStatus: 'rejected';\n  aborted: boolean;\n  condition: boolean;\n} & (({\n  rejectedWithValue: false;\n} & { [K in keyof GetRejectedMeta<ThunkApiConfig>]?: undefined }) | ({\n  rejectedWithValue: true;\n} & GetRejectedMeta<ThunkApiConfig>))>;\nexport type AsyncThunkFulfilledActionCreator<Returned, ThunkArg, ThunkApiConfig = {}> = ActionCreatorWithPreparedPayload<[Returned, string, ThunkArg, GetFulfilledMeta<ThunkApiConfig>?], Returned, string, never, {\n  arg: ThunkArg;\n  requestId: string;\n  requestStatus: 'fulfilled';\n} & GetFulfilledMeta<ThunkApiConfig>>;\n\n/**\n * A type describing the return value of `createAsyncThunk`.\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\n *\n * @public\n */\nexport type AsyncThunk<Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig> = AsyncThunkActionCreator<Returned, ThunkArg, ThunkApiConfig> & {\n  pending: AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig>;\n  rejected: AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig>;\n  fulfilled: AsyncThunkFulfilledActionCreator<Returned, ThunkArg, ThunkApiConfig>;\n  // matchSettled?\n  settled: (action: any) => action is ReturnType<AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig> | AsyncThunkFulfilledActionCreator<Returned, ThunkArg, ThunkApiConfig>>;\n  typePrefix: string;\n};\nexport type OverrideThunkApiConfigs<OldConfig, NewConfig> = Id<NewConfig & Omit<OldConfig, keyof NewConfig>>;\nexport type CreateAsyncThunkFunction<CurriedThunkApiConfig extends AsyncThunkConfig> = {\n  /**\n   *\n   * @param typePrefix\n   * @param payloadCreator\n   * @param options\n   *\n   * @public\n   */\n  // separate signature without `AsyncThunkConfig` for better inference\n  <Returned, ThunkArg = void>(typePrefix: string, payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, CurriedThunkApiConfig>, options?: AsyncThunkOptions<ThunkArg, CurriedThunkApiConfig>): AsyncThunk<Returned, ThunkArg, CurriedThunkApiConfig>;\n\n  /**\n   *\n   * @param typePrefix\n   * @param payloadCreator\n   * @param options\n   *\n   * @public\n   */\n  <Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig>(typePrefix: string, payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>, options?: AsyncThunkOptions<ThunkArg, OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>): AsyncThunk<Returned, ThunkArg, OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>;\n};\ntype CreateAsyncThunk<CurriedThunkApiConfig extends AsyncThunkConfig> = CreateAsyncThunkFunction<CurriedThunkApiConfig> & {\n  withTypes<ThunkApiConfig extends AsyncThunkConfig>(): CreateAsyncThunk<OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>;\n};\nconst externalAbortMessage = 'External signal was aborted';\nexport const createAsyncThunk = /* @__PURE__ */(() => {\n  function createAsyncThunk<Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig>(typePrefix: string, payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, ThunkApiConfig>, options?: AsyncThunkOptions<ThunkArg, ThunkApiConfig>): AsyncThunk<Returned, ThunkArg, ThunkApiConfig> {\n    type RejectedValue = GetRejectValue<ThunkApiConfig>;\n    type PendingMeta = GetPendingMeta<ThunkApiConfig>;\n    type FulfilledMeta = GetFulfilledMeta<ThunkApiConfig>;\n    type RejectedMeta = GetRejectedMeta<ThunkApiConfig>;\n    const fulfilled: AsyncThunkFulfilledActionCreator<Returned, ThunkArg, ThunkApiConfig> = createAction(typePrefix + '/fulfilled', (payload: Returned, requestId: string, arg: ThunkArg, meta?: FulfilledMeta) => ({\n      payload,\n      meta: {\n        ...(meta as any || {}),\n        arg,\n        requestId,\n        requestStatus: 'fulfilled' as const\n      }\n    }));\n    const pending: AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig> = createAction(typePrefix + '/pending', (requestId: string, arg: ThunkArg, meta?: PendingMeta) => ({\n      payload: undefined,\n      meta: {\n        ...(meta as any || {}),\n        arg,\n        requestId,\n        requestStatus: 'pending' as const\n      }\n    }));\n    const rejected: AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig> = createAction(typePrefix + '/rejected', (error: Error | null, requestId: string, arg: ThunkArg, payload?: RejectedValue, meta?: RejectedMeta) => ({\n      payload,\n      error: (options && options.serializeError || miniSerializeError)(error || 'Rejected') as GetSerializedErrorType<ThunkApiConfig>,\n      meta: {\n        ...(meta as any || {}),\n        arg,\n        requestId,\n        rejectedWithValue: !!payload,\n        requestStatus: 'rejected' as const,\n        aborted: error?.name === 'AbortError',\n        condition: error?.name === 'ConditionError'\n      }\n    }));\n    function actionCreator(arg: ThunkArg, {\n      signal\n    }: AsyncThunkDispatchConfig = {}): AsyncThunkAction<Returned, ThunkArg, Required<ThunkApiConfig>> {\n      return (dispatch, getState, extra) => {\n        const requestId = options?.idGenerator ? options.idGenerator(arg) : nanoid();\n        const abortController = new AbortController();\n        let abortHandler: (() => void) | undefined;\n        let abortReason: string | undefined;\n        function abort(reason?: string) {\n          abortReason = reason;\n          abortController.abort();\n        }\n        if (signal) {\n          if (signal.aborted) {\n            abort(externalAbortMessage);\n          } else {\n            signal.addEventListener('abort', () => abort(externalAbortMessage), {\n              once: true\n            });\n          }\n        }\n        const promise = async function () {\n          let finalAction: ReturnType<typeof fulfilled | typeof rejected>;\n          try {\n            let conditionResult = options?.condition?.(arg, {\n              getState,\n              extra\n            });\n            if (isThenable(conditionResult)) {\n              conditionResult = await conditionResult;\n            }\n            if (conditionResult === false || abortController.signal.aborted) {\n              // eslint-disable-next-line no-throw-literal\n              throw {\n                name: 'ConditionError',\n                message: 'Aborted due to condition callback returning false.'\n              };\n            }\n            const abortedPromise = new Promise<never>((_, reject) => {\n              abortHandler = () => {\n                reject({\n                  name: 'AbortError',\n                  message: abortReason || 'Aborted'\n                });\n              };\n              abortController.signal.addEventListener('abort', abortHandler);\n            });\n            dispatch(pending(requestId, arg, options?.getPendingMeta?.({\n              requestId,\n              arg\n            }, {\n              getState,\n              extra\n            })) as any);\n            finalAction = await Promise.race([abortedPromise, Promise.resolve(payloadCreator(arg, {\n              dispatch,\n              getState,\n              extra,\n              requestId,\n              signal: abortController.signal,\n              abort,\n              rejectWithValue: ((value: RejectedValue, meta?: RejectedMeta) => {\n                return new RejectWithValue(value, meta);\n              }) as any,\n              fulfillWithValue: ((value: unknown, meta?: FulfilledMeta) => {\n                return new FulfillWithMeta(value, meta);\n              }) as any\n            })).then(result => {\n              if (result instanceof RejectWithValue) {\n                throw result;\n              }\n              if (result instanceof FulfillWithMeta) {\n                return fulfilled(result.payload, requestId, arg, result.meta);\n              }\n              return fulfilled(result as any, requestId, arg);\n            })]);\n          } catch (err) {\n            finalAction = err instanceof RejectWithValue ? rejected(null, requestId, arg, err.payload, err.meta) : rejected(err as any, requestId, arg);\n          } finally {\n            if (abortHandler) {\n              abortController.signal.removeEventListener('abort', abortHandler);\n            }\n          }\n          // We dispatch the result action _after_ the catch, to avoid having any errors\n          // here get swallowed by the try/catch block,\n          // per https://twitter.com/dan_abramov/status/770914221638942720\n          // and https://github.com/reduxjs/redux-toolkit/blob/e85eb17b39a2118d859f7b7746e0f3fee523e089/docs/tutorials/advanced-tutorial.md#async-error-handling-logic-in-thunks\n\n          const skipDispatch = options && !options.dispatchConditionRejection && rejected.match(finalAction) && (finalAction as any).meta.condition;\n          if (!skipDispatch) {\n            dispatch(finalAction as any);\n          }\n          return finalAction;\n        }();\n        return Object.assign(promise as SafePromise<any>, {\n          abort,\n          requestId,\n          arg,\n          unwrap() {\n            return promise.then<any>(unwrapResult);\n          }\n        });\n      };\n    }\n    return Object.assign(actionCreator as AsyncThunkActionCreator<Returned, ThunkArg, ThunkApiConfig>, {\n      pending,\n      rejected,\n      fulfilled,\n      settled: isAnyOf(rejected, fulfilled),\n      typePrefix\n    });\n  }\n  createAsyncThunk.withTypes = () => createAsyncThunk;\n  return createAsyncThunk as CreateAsyncThunk<AsyncThunkConfig>;\n})();\ninterface UnwrappableAction {\n  payload: any;\n  meta?: any;\n  error?: any;\n}\ntype UnwrappedActionPayload<T extends UnwrappableAction> = Exclude<T, {\n  error: any;\n}>['payload'];\n\n/**\n * @public\n */\nexport function unwrapResult<R extends UnwrappableAction>(action: R): UnwrappedActionPayload<R> {\n  if (action.meta && action.meta.rejectedWithValue) {\n    throw action.payload;\n  }\n  if (action.error) {\n    throw action.error;\n  }\n  return action.payload;\n}\ntype WithStrictNullChecks<True, False> = undefined extends boolean ? False : True;\nfunction isThenable(value: any): value is PromiseLike<any> {\n  return value !== null && typeof value === 'object' && typeof value.then === 'function';\n}", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2, formatProdErrorMessage as _formatProdErrorMessage3, formatProdErrorMessage as _formatProdErrorMessage4, formatProdErrorMessage as _formatProdErrorMessage5, formatProdErrorMessage as _formatProdErrorMessage6, formatProdErrorMessage as _formatProdErrorMessage7, formatProdErrorMessage as _formatProdErrorMessage8 } from \"@reduxjs/toolkit\";\nimport type { Action, Reducer, UnknownAction } from 'redux';\nimport type { Selector } from 'reselect';\nimport type { InjectConfig } from './combineSlices';\nimport type { ActionCreatorWithoutPayload, PayloadAction, PayloadActionCreator, PrepareAction, _ActionCreatorWithPreparedPayload } from './createAction';\nimport { createAction } from './createAction';\nimport type { AsyncThunk, AsyncThunkConfig, AsyncThunkOptions, AsyncThunkPayloadCreator, OverrideThunkApiConfigs } from './createAsyncThunk';\nimport { createAsyncThunk as _createAsyncThunk } from './createAsyncThunk';\nimport type { ActionMatcherDescriptionCollection, CaseReducer, ReducerWithInitialState } from './createReducer';\nimport { createReducer } from './createReducer';\nimport type { ActionReducerMapBuilder, TypedActionCreator } from './mapBuilders';\nimport { executeReducerBuilderCallback } from './mapBuilders';\nimport type { Id, TypeGuard } from './tsHelpers';\nimport { getOrInsertComputed } from './utils';\nconst asyncThunkSymbol = /* @__PURE__ */Symbol.for('rtk-slice-createasyncthunk');\n// type is annotated because it's too long to infer\nexport const asyncThunkCreator: {\n  [asyncThunkSymbol]: typeof _createAsyncThunk;\n} = {\n  [asyncThunkSymbol]: _createAsyncThunk\n};\ntype InjectIntoConfig<NewReducerPath extends string> = InjectConfig & {\n  reducerPath?: NewReducerPath;\n};\n\n/**\n * The return value of `createSlice`\n *\n * @public\n */\nexport interface Slice<State = any, CaseReducers extends SliceCaseReducers<State> = SliceCaseReducers<State>, Name extends string = string, ReducerPath extends string = Name, Selectors extends SliceSelectors<State> = SliceSelectors<State>> {\n  /**\n   * The slice name.\n   */\n  name: Name;\n\n  /**\n   *  The slice reducer path.\n   */\n  reducerPath: ReducerPath;\n\n  /**\n   * The slice's reducer.\n   */\n  reducer: Reducer<State>;\n\n  /**\n   * Action creators for the types of actions that are handled by the slice\n   * reducer.\n   */\n  actions: CaseReducerActions<CaseReducers, Name>;\n\n  /**\n   * The individual case reducer functions that were passed in the `reducers` parameter.\n   * This enables reuse and testing if they were defined inline when calling `createSlice`.\n   */\n  caseReducers: SliceDefinedCaseReducers<CaseReducers>;\n\n  /**\n   * Provides access to the initial state value given to the slice.\n   * If a lazy state initializer was provided, it will be called and a fresh value returned.\n   */\n  getInitialState: () => State;\n\n  /**\n   * Get localised slice selectors (expects to be called with *just* the slice's state as the first parameter)\n   */\n  getSelectors(): Id<SliceDefinedSelectors<State, Selectors, State>>;\n\n  /**\n   * Get globalised slice selectors (`selectState` callback is expected to receive first parameter and return slice state)\n   */\n  getSelectors<RootState>(selectState: (rootState: RootState) => State): Id<SliceDefinedSelectors<State, Selectors, RootState>>;\n\n  /**\n   * Selectors that assume the slice's state is `rootState[slice.reducerPath]` (which is usually the case)\n   *\n   * Equivalent to `slice.getSelectors((state: RootState) => state[slice.reducerPath])`.\n   */\n  get selectors(): Id<SliceDefinedSelectors<State, Selectors, { [K in ReducerPath]: State }>>;\n\n  /**\n   * Inject slice into provided reducer (return value from `combineSlices`), and return injected slice.\n   */\n  injectInto<NewReducerPath extends string = ReducerPath>(this: this, injectable: {\n    inject: (slice: {\n      reducerPath: string;\n      reducer: Reducer;\n    }, config?: InjectConfig) => void;\n  }, config?: InjectIntoConfig<NewReducerPath>): InjectedSlice<State, CaseReducers, Name, NewReducerPath, Selectors>;\n\n  /**\n   * Select the slice state, using the slice's current reducerPath.\n   *\n   * Will throw an error if slice is not found.\n   */\n  selectSlice(state: { [K in ReducerPath]: State }): State;\n}\n\n/**\n * A slice after being called with `injectInto(reducer)`.\n *\n * Selectors can now be called with an `undefined` value, in which case they use the slice's initial state.\n */\ntype InjectedSlice<State = any, CaseReducers extends SliceCaseReducers<State> = SliceCaseReducers<State>, Name extends string = string, ReducerPath extends string = Name, Selectors extends SliceSelectors<State> = SliceSelectors<State>> = Omit<Slice<State, CaseReducers, Name, ReducerPath, Selectors>, 'getSelectors' | 'selectors'> & {\n  /**\n   * Get localised slice selectors (expects to be called with *just* the slice's state as the first parameter)\n   */\n  getSelectors(): Id<SliceDefinedSelectors<State, Selectors, State | undefined>>;\n\n  /**\n   * Get globalised slice selectors (`selectState` callback is expected to receive first parameter and return slice state)\n   */\n  getSelectors<RootState>(selectState: (rootState: RootState) => State | undefined): Id<SliceDefinedSelectors<State, Selectors, RootState>>;\n\n  /**\n   * Selectors that assume the slice's state is `rootState[slice.name]` (which is usually the case)\n   *\n   * Equivalent to `slice.getSelectors((state: RootState) => state[slice.name])`.\n   */\n  get selectors(): Id<SliceDefinedSelectors<State, Selectors, { [K in ReducerPath]?: State | undefined }>>;\n\n  /**\n   * Select the slice state, using the slice's current reducerPath.\n   *\n   * Returns initial state if slice is not found.\n   */\n  selectSlice(state: { [K in ReducerPath]?: State | undefined }): State;\n};\n\n/**\n * Options for `createSlice()`.\n *\n * @public\n */\nexport interface CreateSliceOptions<State = any, CR extends SliceCaseReducers<State> = SliceCaseReducers<State>, Name extends string = string, ReducerPath extends string = Name, Selectors extends SliceSelectors<State> = SliceSelectors<State>> {\n  /**\n   * The slice's name. Used to namespace the generated action types.\n   */\n  name: Name;\n\n  /**\n   * The slice's reducer path. Used when injecting into a combined slice reducer.\n   */\n  reducerPath?: ReducerPath;\n\n  /**\n   * The initial state that should be used when the reducer is called the first time. This may also be a \"lazy initializer\" function, which should return an initial state value when called. This will be used whenever the reducer is called with `undefined` as its state value, and is primarily useful for cases like reading initial state from `localStorage`.\n   */\n  initialState: State | (() => State);\n\n  /**\n   * A mapping from action types to action-type-specific *case reducer*\n   * functions. For every action type, a matching action creator will be\n   * generated using `createAction()`.\n   */\n  reducers: ValidateSliceCaseReducers<State, CR> | ((creators: ReducerCreators<State>) => CR);\n\n  /**\n   * A callback that receives a *builder* object to define\n   * case reducers via calls to `builder.addCase(actionCreatorOrType, reducer)`.\n   *\n   *\n   * @example\n  ```ts\n  import { createAction, createSlice, Action } from '@reduxjs/toolkit'\n  const incrementBy = createAction<number>('incrementBy')\n  const decrement = createAction('decrement')\n  interface RejectedAction extends Action {\n  error: Error\n  }\n  function isRejectedAction(action: Action): action is RejectedAction {\n  return action.type.endsWith('rejected')\n  }\n  createSlice({\n  name: 'counter',\n  initialState: 0,\n  reducers: {},\n  extraReducers: builder => {\n    builder\n      .addCase(incrementBy, (state, action) => {\n        // action is inferred correctly here if using TS\n      })\n      // You can chain calls, or have separate `builder.addCase()` lines each time\n      .addCase(decrement, (state, action) => {})\n      // You can match a range of action types\n      .addMatcher(\n        isRejectedAction,\n        // `action` will be inferred as a RejectedAction due to isRejectedAction being defined as a type guard\n        (state, action) => {}\n      )\n      // and provide a default case if no other handlers matched\n      .addDefaultCase((state, action) => {})\n    }\n  })\n  ```\n   */\n  extraReducers?: (builder: ActionReducerMapBuilder<State>) => void;\n\n  /**\n   * A map of selectors that receive the slice's state and any additional arguments, and return a result.\n   */\n  selectors?: Selectors;\n}\nexport enum ReducerType {\n  reducer = 'reducer',\n  reducerWithPrepare = 'reducerWithPrepare',\n  asyncThunk = 'asyncThunk',\n}\ntype ReducerDefinition<T extends ReducerType = ReducerType> = {\n  _reducerDefinitionType: T;\n};\nexport type CaseReducerDefinition<S = any, A extends Action = UnknownAction> = CaseReducer<S, A> & ReducerDefinition<ReducerType.reducer>;\n\n/**\n * A CaseReducer with a `prepare` method.\n *\n * @public\n */\nexport type CaseReducerWithPrepare<State, Action extends PayloadAction> = {\n  reducer: CaseReducer<State, Action>;\n  prepare: PrepareAction<Action['payload']>;\n};\nexport interface CaseReducerWithPrepareDefinition<State, Action extends PayloadAction> extends CaseReducerWithPrepare<State, Action>, ReducerDefinition<ReducerType.reducerWithPrepare> {}\ntype AsyncThunkSliceReducerConfig<State, ThunkArg extends any, Returned = unknown, ThunkApiConfig extends AsyncThunkConfig = {}> = {\n  pending?: CaseReducer<State, ReturnType<AsyncThunk<Returned, ThunkArg, ThunkApiConfig>['pending']>>;\n  rejected?: CaseReducer<State, ReturnType<AsyncThunk<Returned, ThunkArg, ThunkApiConfig>['rejected']>>;\n  fulfilled?: CaseReducer<State, ReturnType<AsyncThunk<Returned, ThunkArg, ThunkApiConfig>['fulfilled']>>;\n  settled?: CaseReducer<State, ReturnType<AsyncThunk<Returned, ThunkArg, ThunkApiConfig>['rejected' | 'fulfilled']>>;\n  options?: AsyncThunkOptions<ThunkArg, ThunkApiConfig>;\n};\ntype AsyncThunkSliceReducerDefinition<State, ThunkArg extends any, Returned = unknown, ThunkApiConfig extends AsyncThunkConfig = {}> = AsyncThunkSliceReducerConfig<State, ThunkArg, Returned, ThunkApiConfig> & ReducerDefinition<ReducerType.asyncThunk> & {\n  payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, ThunkApiConfig>;\n};\n\n/**\n * Providing these as part of the config would cause circular types, so we disallow passing them\n */\ntype PreventCircular<ThunkApiConfig> = { [K in keyof ThunkApiConfig]: K extends 'state' | 'dispatch' ? never : ThunkApiConfig[K] };\ninterface AsyncThunkCreator<State, CurriedThunkApiConfig extends PreventCircular<AsyncThunkConfig> = PreventCircular<AsyncThunkConfig>> {\n  <Returned, ThunkArg = void>(payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, CurriedThunkApiConfig>, config?: AsyncThunkSliceReducerConfig<State, ThunkArg, Returned, CurriedThunkApiConfig>): AsyncThunkSliceReducerDefinition<State, ThunkArg, Returned, CurriedThunkApiConfig>;\n  <Returned, ThunkArg, ThunkApiConfig extends PreventCircular<AsyncThunkConfig> = {}>(payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, ThunkApiConfig>, config?: AsyncThunkSliceReducerConfig<State, ThunkArg, Returned, ThunkApiConfig>): AsyncThunkSliceReducerDefinition<State, ThunkArg, Returned, ThunkApiConfig>;\n  withTypes<ThunkApiConfig extends PreventCircular<AsyncThunkConfig>>(): AsyncThunkCreator<State, OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>;\n}\nexport interface ReducerCreators<State> {\n  reducer(caseReducer: CaseReducer<State, PayloadAction>): CaseReducerDefinition<State, PayloadAction>;\n  reducer<Payload>(caseReducer: CaseReducer<State, PayloadAction<Payload>>): CaseReducerDefinition<State, PayloadAction<Payload>>;\n  asyncThunk: AsyncThunkCreator<State>;\n  preparedReducer<Prepare extends PrepareAction<any>>(prepare: Prepare, reducer: CaseReducer<State, ReturnType<_ActionCreatorWithPreparedPayload<Prepare>>>): {\n    _reducerDefinitionType: ReducerType.reducerWithPrepare;\n    prepare: Prepare;\n    reducer: CaseReducer<State, ReturnType<_ActionCreatorWithPreparedPayload<Prepare>>>;\n  };\n}\n\n/**\n * The type describing a slice's `reducers` option.\n *\n * @public\n */\nexport type SliceCaseReducers<State> = Record<string, ReducerDefinition> | Record<string, CaseReducer<State, PayloadAction<any>> | CaseReducerWithPrepare<State, PayloadAction<any, string, any, any>>>;\n\n/**\n * The type describing a slice's `selectors` option.\n */\nexport type SliceSelectors<State> = {\n  [K: string]: (sliceState: State, ...args: any[]) => any;\n};\ntype SliceActionType<SliceName extends string, ActionName extends keyof any> = ActionName extends string | number ? `${SliceName}/${ActionName}` : string;\n\n/**\n * Derives the slice's `actions` property from the `reducers` options\n *\n * @public\n */\nexport type CaseReducerActions<CaseReducers extends SliceCaseReducers<any>, SliceName extends string> = { [Type in keyof CaseReducers]: CaseReducers[Type] extends infer Definition ? Definition extends {\n  prepare: any;\n} ? ActionCreatorForCaseReducerWithPrepare<Definition, SliceActionType<SliceName, Type>> : Definition extends AsyncThunkSliceReducerDefinition<any, infer ThunkArg, infer Returned, infer ThunkApiConfig> ? AsyncThunk<Returned, ThunkArg, ThunkApiConfig> : Definition extends {\n  reducer: any;\n} ? ActionCreatorForCaseReducer<Definition['reducer'], SliceActionType<SliceName, Type>> : ActionCreatorForCaseReducer<Definition, SliceActionType<SliceName, Type>> : never };\n\n/**\n * Get a `PayloadActionCreator` type for a passed `CaseReducerWithPrepare`\n *\n * @internal\n */\ntype ActionCreatorForCaseReducerWithPrepare<CR extends {\n  prepare: any;\n}, Type extends string> = _ActionCreatorWithPreparedPayload<CR['prepare'], Type>;\n\n/**\n * Get a `PayloadActionCreator` type for a passed `CaseReducer`\n *\n * @internal\n */\ntype ActionCreatorForCaseReducer<CR, Type extends string> = CR extends ((state: any, action: infer Action) => any) ? Action extends {\n  payload: infer P;\n} ? PayloadActionCreator<P, Type> : ActionCreatorWithoutPayload<Type> : ActionCreatorWithoutPayload<Type>;\n\n/**\n * Extracts the CaseReducers out of a `reducers` object, even if they are\n * tested into a `CaseReducerWithPrepare`.\n *\n * @internal\n */\ntype SliceDefinedCaseReducers<CaseReducers extends SliceCaseReducers<any>> = { [Type in keyof CaseReducers]: CaseReducers[Type] extends infer Definition ? Definition extends AsyncThunkSliceReducerDefinition<any, any, any> ? Id<Pick<Required<Definition>, 'fulfilled' | 'rejected' | 'pending' | 'settled'>> : Definition extends {\n  reducer: infer Reducer;\n} ? Reducer : Definition : never };\ntype RemappedSelector<S extends Selector, NewState> = S extends Selector<any, infer R, infer P> ? Selector<NewState, R, P> & {\n  unwrapped: S;\n} : never;\n\n/**\n * Extracts the final selector type from the `selectors` object.\n *\n * Removes the `string` index signature from the default value.\n */\ntype SliceDefinedSelectors<State, Selectors extends SliceSelectors<State>, RootState> = { [K in keyof Selectors as string extends K ? never : K]: RemappedSelector<Selectors[K], RootState> };\n\n/**\n * Used on a SliceCaseReducers object.\n * Ensures that if a CaseReducer is a `CaseReducerWithPrepare`, that\n * the `reducer` and the `prepare` function use the same type of `payload`.\n *\n * Might do additional such checks in the future.\n *\n * This type is only ever useful if you want to write your own wrapper around\n * `createSlice`. Please don't use it otherwise!\n *\n * @public\n */\nexport type ValidateSliceCaseReducers<S, ACR extends SliceCaseReducers<S>> = ACR & { [T in keyof ACR]: ACR[T] extends {\n  reducer(s: S, action?: infer A): any;\n} ? {\n  prepare(...a: never[]): Omit<A, 'type'>;\n} : {} };\nfunction getType(slice: string, actionKey: string): string {\n  return `${slice}/${actionKey}`;\n}\ninterface BuildCreateSliceConfig {\n  creators?: {\n    asyncThunk?: typeof asyncThunkCreator;\n  };\n}\nexport function buildCreateSlice({\n  creators\n}: BuildCreateSliceConfig = {}) {\n  const cAT = creators?.asyncThunk?.[asyncThunkSymbol];\n  return function createSlice<State, CaseReducers extends SliceCaseReducers<State>, Name extends string, Selectors extends SliceSelectors<State>, ReducerPath extends string = Name>(options: CreateSliceOptions<State, CaseReducers, Name, ReducerPath, Selectors>): Slice<State, CaseReducers, Name, ReducerPath, Selectors> {\n    const {\n      name,\n      reducerPath = name as unknown as ReducerPath\n    } = options;\n    if (!name) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(11) : '`name` is a required option for createSlice');\n    }\n    if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {\n      if (options.initialState === undefined) {\n        console.error('You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`');\n      }\n    }\n    const reducers = (typeof options.reducers === 'function' ? options.reducers(buildReducerCreators<State>()) : options.reducers) || {};\n    const reducerNames = Object.keys(reducers);\n    const context: ReducerHandlingContext<State> = {\n      sliceCaseReducersByName: {},\n      sliceCaseReducersByType: {},\n      actionCreators: {},\n      sliceMatchers: []\n    };\n    const contextMethods: ReducerHandlingContextMethods<State> = {\n      addCase(typeOrActionCreator: string | TypedActionCreator<any>, reducer: CaseReducer<State>) {\n        const type = typeof typeOrActionCreator === 'string' ? typeOrActionCreator : typeOrActionCreator.type;\n        if (!type) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(12) : '`context.addCase` cannot be called with an empty action type');\n        }\n        if (type in context.sliceCaseReducersByType) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(13) : '`context.addCase` cannot be called with two reducers for the same action type: ' + type);\n        }\n        context.sliceCaseReducersByType[type] = reducer;\n        return contextMethods;\n      },\n      addMatcher(matcher, reducer) {\n        context.sliceMatchers.push({\n          matcher,\n          reducer\n        });\n        return contextMethods;\n      },\n      exposeAction(name, actionCreator) {\n        context.actionCreators[name] = actionCreator;\n        return contextMethods;\n      },\n      exposeCaseReducer(name, reducer) {\n        context.sliceCaseReducersByName[name] = reducer;\n        return contextMethods;\n      }\n    };\n    reducerNames.forEach(reducerName => {\n      const reducerDefinition = reducers[reducerName];\n      const reducerDetails: ReducerDetails = {\n        reducerName,\n        type: getType(name, reducerName),\n        createNotation: typeof options.reducers === 'function'\n      };\n      if (isAsyncThunkSliceReducerDefinition<State>(reducerDefinition)) {\n        handleThunkCaseReducerDefinition(reducerDetails, reducerDefinition, contextMethods, cAT);\n      } else {\n        handleNormalReducerDefinition<State>(reducerDetails, reducerDefinition as any, contextMethods);\n      }\n    });\n    function buildReducer() {\n      if (process.env.NODE_ENV !== 'production') {\n        if (typeof options.extraReducers === 'object') {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage4(14) : \"The object notation for `createSlice.extraReducers` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\");\n        }\n      }\n      const [extraReducers = {}, actionMatchers = [], defaultCaseReducer = undefined] = typeof options.extraReducers === 'function' ? executeReducerBuilderCallback(options.extraReducers) : [options.extraReducers];\n      const finalCaseReducers = {\n        ...extraReducers,\n        ...context.sliceCaseReducersByType\n      };\n      return createReducer(options.initialState, builder => {\n        for (let key in finalCaseReducers) {\n          builder.addCase(key, finalCaseReducers[key] as CaseReducer<any>);\n        }\n        for (let sM of context.sliceMatchers) {\n          builder.addMatcher(sM.matcher, sM.reducer);\n        }\n        for (let m of actionMatchers) {\n          builder.addMatcher(m.matcher, m.reducer);\n        }\n        if (defaultCaseReducer) {\n          builder.addDefaultCase(defaultCaseReducer);\n        }\n      });\n    }\n    const selectSelf = (state: State) => state;\n    const injectedSelectorCache = new Map<boolean, WeakMap<(rootState: any) => State | undefined, Record<string, (rootState: any) => any>>>();\n    const injectedStateCache = new WeakMap<(rootState: any) => State, State>();\n    let _reducer: ReducerWithInitialState<State>;\n    function reducer(state: State | undefined, action: UnknownAction) {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer(state, action);\n    }\n    function getInitialState() {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer.getInitialState();\n    }\n    function makeSelectorProps<CurrentReducerPath extends string = ReducerPath>(reducerPath: CurrentReducerPath, injected = false): Pick<Slice<State, CaseReducers, Name, CurrentReducerPath, Selectors>, 'getSelectors' | 'selectors' | 'selectSlice' | 'reducerPath'> {\n      function selectSlice(state: { [K in CurrentReducerPath]: State }) {\n        let sliceState = state[reducerPath];\n        if (typeof sliceState === 'undefined') {\n          if (injected) {\n            sliceState = getOrInsertComputed(injectedStateCache, selectSlice, getInitialState);\n          } else if (process.env.NODE_ENV !== 'production') {\n            throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage5(15) : 'selectSlice returned undefined for an uninjected slice reducer');\n          }\n        }\n        return sliceState;\n      }\n      function getSelectors(selectState: (rootState: any) => State = selectSelf) {\n        const selectorCache = getOrInsertComputed(injectedSelectorCache, injected, () => new WeakMap());\n        return getOrInsertComputed(selectorCache, selectState, () => {\n          const map: Record<string, Selector<any, any>> = {};\n          for (const [name, selector] of Object.entries(options.selectors ?? {})) {\n            map[name] = wrapSelector(selector, selectState, () => getOrInsertComputed(injectedStateCache, selectState, getInitialState), injected);\n          }\n          return map;\n        }) as any;\n      }\n      return {\n        reducerPath,\n        getSelectors,\n        get selectors() {\n          return getSelectors(selectSlice);\n        },\n        selectSlice\n      };\n    }\n    const slice: Slice<State, CaseReducers, Name, ReducerPath, Selectors> = {\n      name,\n      reducer,\n      actions: context.actionCreators as any,\n      caseReducers: context.sliceCaseReducersByName as any,\n      getInitialState,\n      ...makeSelectorProps(reducerPath),\n      injectInto(injectable, {\n        reducerPath: pathOpt,\n        ...config\n      } = {}) {\n        const newReducerPath = pathOpt ?? reducerPath;\n        injectable.inject({\n          reducerPath: newReducerPath,\n          reducer\n        }, config);\n        return {\n          ...slice,\n          ...makeSelectorProps(newReducerPath, true)\n        } as any;\n      }\n    };\n    return slice;\n  };\n}\nfunction wrapSelector<State, NewState, S extends Selector<State>>(selector: S, selectState: Selector<NewState, State>, getInitialState: () => State, injected?: boolean) {\n  function wrapper(rootState: NewState, ...args: any[]) {\n    let sliceState = selectState(rootState);\n    if (typeof sliceState === 'undefined') {\n      if (injected) {\n        sliceState = getInitialState();\n      } else if (process.env.NODE_ENV !== 'production') {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage6(16) : 'selectState returned undefined for an uninjected slice reducer');\n      }\n    }\n    return selector(sliceState, ...args);\n  }\n  wrapper.unwrapped = selector;\n  return wrapper as RemappedSelector<S, NewState>;\n}\n\n/**\n * A function that accepts an initial state, an object full of reducer\n * functions, and a \"slice name\", and automatically generates\n * action creators and action types that correspond to the\n * reducers and state.\n *\n * @public\n */\nexport const createSlice = /* @__PURE__ */buildCreateSlice();\ninterface ReducerHandlingContext<State> {\n  sliceCaseReducersByName: Record<string, CaseReducer<State, any> | Pick<AsyncThunkSliceReducerDefinition<State, any, any, any>, 'fulfilled' | 'rejected' | 'pending' | 'settled'>>;\n  sliceCaseReducersByType: Record<string, CaseReducer<State, any>>;\n  sliceMatchers: ActionMatcherDescriptionCollection<State>;\n  actionCreators: Record<string, Function>;\n}\ninterface ReducerHandlingContextMethods<State> {\n  /**\n   * Adds a case reducer to handle a single action type.\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\n   * @param reducer - The actual case reducer function.\n   */\n  addCase<ActionCreator extends TypedActionCreator<string>>(actionCreator: ActionCreator, reducer: CaseReducer<State, ReturnType<ActionCreator>>): ReducerHandlingContextMethods<State>;\n  /**\n   * Adds a case reducer to handle a single action type.\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\n   * @param reducer - The actual case reducer function.\n   */\n  addCase<Type extends string, A extends Action<Type>>(type: Type, reducer: CaseReducer<State, A>): ReducerHandlingContextMethods<State>;\n\n  /**\n   * Allows you to match incoming actions against your own filter function instead of only the `action.type` property.\n   * @remarks\n   * If multiple matcher reducers match, all of them will be executed in the order\n   * they were defined in - even if a case reducer already matched.\n   * All calls to `builder.addMatcher` must come after any calls to `builder.addCase` and before any calls to `builder.addDefaultCase`.\n   * @param matcher - A matcher function. In TypeScript, this should be a [type predicate](https://www.typescriptlang.org/docs/handbook/2/narrowing.html#using-type-predicates)\n   *   function\n   * @param reducer - The actual case reducer function.\n   *\n   */\n  addMatcher<A>(matcher: TypeGuard<A>, reducer: CaseReducer<State, A extends Action ? A : A & Action>): ReducerHandlingContextMethods<State>;\n  /**\n   * Add an action to be exposed under the final `slice.actions` key.\n   * @param name The key to be exposed as.\n   * @param actionCreator The action to expose.\n   * @example\n   * context.exposeAction(\"addPost\", createAction<Post>(\"addPost\"));\n   *\n   * export const { addPost } = slice.actions\n   *\n   * dispatch(addPost(post))\n   */\n  exposeAction(name: string, actionCreator: Function): ReducerHandlingContextMethods<State>;\n  /**\n   * Add a case reducer to be exposed under the final `slice.caseReducers` key.\n   * @param name The key to be exposed as.\n   * @param reducer The reducer to expose.\n   * @example\n   * context.exposeCaseReducer(\"addPost\", (state, action: PayloadAction<Post>) => {\n   *   state.push(action.payload)\n   * })\n   *\n   * slice.caseReducers.addPost([], addPost(post))\n   */\n  exposeCaseReducer(name: string, reducer: CaseReducer<State, any> | Pick<AsyncThunkSliceReducerDefinition<State, any, any, any>, 'fulfilled' | 'rejected' | 'pending' | 'settled'>): ReducerHandlingContextMethods<State>;\n}\ninterface ReducerDetails {\n  /** The key the reducer was defined under */\n  reducerName: string;\n  /** The predefined action type, i.e. `${slice.name}/${reducerName}` */\n  type: string;\n  /** Whether create. notation was used when defining reducers */\n  createNotation: boolean;\n}\nfunction buildReducerCreators<State>(): ReducerCreators<State> {\n  function asyncThunk(payloadCreator: AsyncThunkPayloadCreator<any, any>, config: AsyncThunkSliceReducerConfig<State, any>): AsyncThunkSliceReducerDefinition<State, any> {\n    return {\n      _reducerDefinitionType: ReducerType.asyncThunk,\n      payloadCreator,\n      ...config\n    };\n  }\n  asyncThunk.withTypes = () => asyncThunk;\n  return {\n    reducer(caseReducer: CaseReducer<State, any>) {\n      return Object.assign({\n        // hack so the wrapping function has the same name as the original\n        // we need to create a wrapper so the `reducerDefinitionType` is not assigned to the original\n        [caseReducer.name](...args: Parameters<typeof caseReducer>) {\n          return caseReducer(...args);\n        }\n      }[caseReducer.name], {\n        _reducerDefinitionType: ReducerType.reducer\n      } as const);\n    },\n    preparedReducer(prepare, reducer) {\n      return {\n        _reducerDefinitionType: ReducerType.reducerWithPrepare,\n        prepare,\n        reducer\n      };\n    },\n    asyncThunk: asyncThunk as any\n  };\n}\nfunction handleNormalReducerDefinition<State>({\n  type,\n  reducerName,\n  createNotation\n}: ReducerDetails, maybeReducerWithPrepare: CaseReducer<State, {\n  payload: any;\n  type: string;\n}> | CaseReducerWithPrepare<State, PayloadAction<any, string, any, any>>, context: ReducerHandlingContextMethods<State>) {\n  let caseReducer: CaseReducer<State, any>;\n  let prepareCallback: PrepareAction<any> | undefined;\n  if ('reducer' in maybeReducerWithPrepare) {\n    if (createNotation && !isCaseReducerWithPrepareDefinition(maybeReducerWithPrepare)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage7(17) : 'Please use the `create.preparedReducer` notation for prepared action creators with the `create` notation.');\n    }\n    caseReducer = maybeReducerWithPrepare.reducer;\n    prepareCallback = maybeReducerWithPrepare.prepare;\n  } else {\n    caseReducer = maybeReducerWithPrepare;\n  }\n  context.addCase(type, caseReducer).exposeCaseReducer(reducerName, caseReducer).exposeAction(reducerName, prepareCallback ? createAction(type, prepareCallback) : createAction(type));\n}\nfunction isAsyncThunkSliceReducerDefinition<State>(reducerDefinition: any): reducerDefinition is AsyncThunkSliceReducerDefinition<State, any, any, any> {\n  return reducerDefinition._reducerDefinitionType === ReducerType.asyncThunk;\n}\nfunction isCaseReducerWithPrepareDefinition<State>(reducerDefinition: any): reducerDefinition is CaseReducerWithPrepareDefinition<State, any> {\n  return reducerDefinition._reducerDefinitionType === ReducerType.reducerWithPrepare;\n}\nfunction handleThunkCaseReducerDefinition<State>({\n  type,\n  reducerName\n}: ReducerDetails, reducerDefinition: AsyncThunkSliceReducerDefinition<State, any, any, any>, context: ReducerHandlingContextMethods<State>, cAT: typeof _createAsyncThunk | undefined) {\n  if (!cAT) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage8(18) : 'Cannot use `create.asyncThunk` in the built-in `createSlice`. ' + 'Use `buildCreateSlice({ creators: { asyncThunk: asyncThunkCreator } })` to create a customised version of `createSlice`.');\n  }\n  const {\n    payloadCreator,\n    fulfilled,\n    pending,\n    rejected,\n    settled,\n    options\n  } = reducerDefinition;\n  const thunk = cAT(type, payloadCreator, options as any);\n  context.exposeAction(reducerName, thunk);\n  if (fulfilled) {\n    context.addCase(thunk.fulfilled, fulfilled);\n  }\n  if (pending) {\n    context.addCase(thunk.pending, pending);\n  }\n  if (rejected) {\n    context.addCase(thunk.rejected, rejected);\n  }\n  if (settled) {\n    context.addMatcher(thunk.settled, settled);\n  }\n  context.exposeCaseReducer(reducerName, {\n    fulfilled: fulfilled || noop,\n    pending: pending || noop,\n    rejected: rejected || noop,\n    settled: settled || noop\n  });\n}\nfunction noop() {}", "import type { EntityId, EntityState, EntityStateAdapter, EntityStateFactory } from './models';\nexport function getInitialEntityState<T, Id extends EntityId>(): EntityState<T, Id> {\n  return {\n    ids: [],\n    entities: {} as Record<Id, T>\n  };\n}\nexport function createInitialStateFactory<T, Id extends EntityId>(stateAdapter: EntityStateAdapter<T, Id>): EntityStateFactory<T, Id> {\n  function getInitialState(state?: undefined, entities?: readonly T[] | Record<Id, T>): EntityState<T, Id>;\n  function getInitialState<S extends object>(additionalState: S, entities?: readonly T[] | Record<Id, T>): EntityState<T, Id> & S;\n  function getInitialState(additionalState: any = {}, entities?: readonly T[] | Record<Id, T>): any {\n    const state = Object.assign(getInitialEntityState(), additionalState);\n    return entities ? stateAdapter.setAll(state, entities) : state;\n  }\n  return {\n    getInitialState\n  };\n}", "import type { CreateSelectorFunction, Selector } from 'reselect';\nimport { createDraftSafeSelector } from '../createDraftSafeSelector';\nimport type { EntityId, EntitySelectors, EntityState } from './models';\ntype AnyFunction = (...args: any) => any;\ntype AnyCreateSelectorFunction = CreateSelectorFunction<<F extends AnyFunction>(f: F) => F, <F extends AnyFunction>(f: F) => F>;\nexport type GetSelectorsOptions = {\n  createSelector?: AnyCreateSelectorFunction;\n};\nexport function createSelectorsFactory<T, Id extends EntityId>() {\n  function getSelectors(selectState?: undefined, options?: GetSelectorsOptions): EntitySelectors<T, EntityState<T, Id>, Id>;\n  function getSelectors<V>(selectState: (state: V) => EntityState<T, Id>, options?: GetSelectorsOptions): EntitySelectors<T, V, Id>;\n  function getSelectors<V>(selectState?: (state: V) => EntityState<T, Id>, options: GetSelectorsOptions = {}): EntitySelectors<T, any, Id> {\n    const {\n      createSelector = createDraftSafeSelector as AnyCreateSelectorFunction\n    } = options;\n    const selectIds = (state: EntityState<T, Id>) => state.ids;\n    const selectEntities = (state: EntityState<T, Id>) => state.entities;\n    const selectAll = createSelector(selectIds, selectEntities, (ids, entities): T[] => ids.map(id => entities[id]!));\n    const selectId = (_: unknown, id: Id) => id;\n    const selectById = (entities: Record<Id, T>, id: Id) => entities[id];\n    const selectTotal = createSelector(selectIds, ids => ids.length);\n    if (!selectState) {\n      return {\n        selectIds,\n        selectEntities,\n        selectAll,\n        selectTotal,\n        selectById: createSelector(selectEntities, selectId, selectById)\n      };\n    }\n    const selectGlobalizedEntities = createSelector(selectState as Selector<V, EntityState<T, Id>>, selectEntities);\n    return {\n      selectIds: createSelector(selectState, selectIds),\n      selectEntities: selectGlobalizedEntities,\n      selectAll: createSelector(selectState, selectAll),\n      selectTotal: createSelector(selectState, selectTotal),\n      selectById: createSelector(selectGlobalizedEntities, selectId, selectById)\n    };\n  }\n  return {\n    getSelectors\n  };\n}", "import { produce as createNextState, isDraft } from 'immer';\nimport type { Draft } from 'immer';\nimport type { EntityId, DraftableEntityState, PreventAny } from './models';\nimport type { PayloadAction } from '../createAction';\nimport { isFSA } from '../createAction';\nexport const isDraftTyped = isDraft as <T>(value: T | Draft<T>) => value is Draft<T>;\nexport function createSingleArgumentStateOperator<T, Id extends EntityId>(mutator: (state: DraftableEntityState<T, Id>) => void) {\n  const operator = createStateOperator((_: undefined, state: DraftableEntityState<T, Id>) => mutator(state));\n  return function operation<S extends DraftableEntityState<T, Id>>(state: PreventAny<S, T, Id>): S {\n    return operator(state as S, undefined);\n  };\n}\nexport function createStateOperator<T, Id extends EntityId, R>(mutator: (arg: R, state: DraftableEntityState<T, Id>) => void) {\n  return function operation<S extends DraftableEntityState<T, Id>>(state: S, arg: R | PayloadAction<R>): S {\n    function isPayloadActionArgument(arg: R | PayloadAction<R>): arg is PayloadAction<R> {\n      return isFSA(arg);\n    }\n    const runMutator = (draft: DraftableEntityState<T, Id>) => {\n      if (isPayloadActionArgument(arg)) {\n        mutator(arg.payload, draft);\n      } else {\n        mutator(arg, draft);\n      }\n    };\n    if (isDraftTyped<DraftableEntityState<T, Id>>(state)) {\n      // we must already be inside a `createNextState` call, likely because\n      // this is being wrapped in `createReducer` or `createSlice`.\n      // It's safe to just pass the draft to the mutator.\n      runMutator(state);\n\n      // since it's a draft, we'll just return it\n      return state;\n    }\n    return createNextState(state, runMutator);\n  };\n}", "import type { Draft } from 'immer';\nimport { current, isDraft } from 'immer';\nimport type { DraftableEntityState, EntityId, IdSelector, Update } from './models';\nexport function selectIdValue<T, Id extends EntityId>(entity: T, selectId: IdSelector<T, Id>) {\n  const key = selectId(entity);\n  if (process.env.NODE_ENV !== 'production' && key === undefined) {\n    console.warn('The entity passed to the `selectId` implementation returned undefined.', 'You should probably provide your own `selectId` implementation.', 'The entity that was passed:', entity, 'The `selectId` implementation:', selectId.toString());\n  }\n  return key;\n}\nexport function ensureEntitiesArray<T, Id extends EntityId>(entities: readonly T[] | Record<Id, T>): readonly T[] {\n  if (!Array.isArray(entities)) {\n    entities = Object.values(entities);\n  }\n  return entities;\n}\nexport function getCurrent<T>(value: T | Draft<T>): T {\n  return (isDraft(value) ? current(value) : value) as T;\n}\nexport function splitAddedUpdatedEntities<T, Id extends EntityId>(newEntities: readonly T[] | Record<Id, T>, selectId: IdSelector<T, Id>, state: DraftableEntityState<T, Id>): [T[], Update<T, Id>[], Id[]] {\n  newEntities = ensureEntitiesArray(newEntities);\n  const existingIdsArray = getCurrent(state.ids);\n  const existingIds = new Set<Id>(existingIdsArray);\n  const added: T[] = [];\n  const addedIds = new Set<Id>([]);\n  const updated: Update<T, Id>[] = [];\n  for (const entity of newEntities) {\n    const id = selectIdValue(entity, selectId);\n    if (existingIds.has(id) || addedIds.has(id)) {\n      updated.push({\n        id,\n        changes: entity\n      });\n    } else {\n      addedIds.add(id);\n      added.push(entity);\n    }\n  }\n  return [added, updated, existingIdsArray];\n}", "import type { Draft } from 'immer';\nimport type { EntityStateAdapter, IdSelector, Update, EntityId, DraftableEntityState } from './models';\nimport { createStateOperator, createSingleArgumentStateOperator } from './state_adapter';\nimport { selectIdValue, ensureEntitiesArray, splitAddedUpdatedEntities } from './utils';\nexport function createUnsortedStateAdapter<T, Id extends EntityId>(selectId: IdSelector<T, Id>): EntityStateAdapter<T, Id> {\n  type R = DraftableEntityState<T, Id>;\n  function addOneMutably(entity: T, state: R): void {\n    const key = selectIdValue(entity, selectId);\n    if (key in state.entities) {\n      return;\n    }\n    state.ids.push(key as Id & Draft<Id>);\n    (state.entities as Record<Id, T>)[key] = entity;\n  }\n  function addManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      addOneMutably(entity, state);\n    }\n  }\n  function setOneMutably(entity: T, state: R): void {\n    const key = selectIdValue(entity, selectId);\n    if (!(key in state.entities)) {\n      state.ids.push(key as Id & Draft<Id>);\n    }\n    ;\n    (state.entities as Record<Id, T>)[key] = entity;\n  }\n  function setManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      setOneMutably(entity, state);\n    }\n  }\n  function setAllMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.ids = [];\n    state.entities = {} as Record<Id, T>;\n    addManyMutably(newEntities, state);\n  }\n  function removeOneMutably(key: Id, state: R): void {\n    return removeManyMutably([key], state);\n  }\n  function removeManyMutably(keys: readonly Id[], state: R): void {\n    let didMutate = false;\n    keys.forEach(key => {\n      if (key in state.entities) {\n        delete (state.entities as Record<Id, T>)[key];\n        didMutate = true;\n      }\n    });\n    if (didMutate) {\n      state.ids = (state.ids as Id[]).filter(id => id in state.entities) as Id[] | Draft<Id[]>;\n    }\n  }\n  function removeAllMutably(state: R): void {\n    Object.assign(state, {\n      ids: [],\n      entities: {}\n    });\n  }\n  function takeNewKey(keys: {\n    [id: string]: Id;\n  }, update: Update<T, Id>, state: R): boolean {\n    const original: T | undefined = (state.entities as Record<Id, T>)[update.id];\n    if (original === undefined) {\n      return false;\n    }\n    const updated: T = Object.assign({}, original, update.changes);\n    const newKey = selectIdValue(updated, selectId);\n    const hasNewKey = newKey !== update.id;\n    if (hasNewKey) {\n      keys[update.id] = newKey;\n      delete (state.entities as Record<Id, T>)[update.id];\n    }\n    ;\n    (state.entities as Record<Id, T>)[newKey] = updated;\n    return hasNewKey;\n  }\n  function updateOneMutably(update: Update<T, Id>, state: R): void {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates: ReadonlyArray<Update<T, Id>>, state: R): void {\n    const newKeys: {\n      [id: string]: Id;\n    } = {};\n    const updatesPerEntity: {\n      [id: string]: Update<T, Id>;\n    } = {};\n    updates.forEach(update => {\n      // Only apply updates to entities that currently exist\n      if (update.id in state.entities) {\n        // If there are multiple updates to one entity, merge them together\n        updatesPerEntity[update.id] = {\n          id: update.id,\n          // Spreads ignore falsy values, so this works even if there isn't\n          // an existing update already at this key\n          changes: {\n            ...updatesPerEntity[update.id]?.changes,\n            ...update.changes\n          }\n        };\n      }\n    });\n    updates = Object.values(updatesPerEntity);\n    const didMutateEntities = updates.length > 0;\n    if (didMutateEntities) {\n      const didMutateIds = updates.filter(update => takeNewKey(newKeys, update, state)).length > 0;\n      if (didMutateIds) {\n        state.ids = Object.values(state.entities).map(e => selectIdValue(e as T, selectId));\n      }\n    }\n  }\n  function upsertOneMutably(entity: T, state: R): void {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    const [added, updated] = splitAddedUpdatedEntities<T, Id>(newEntities, selectId, state);\n    addManyMutably(added, state);\n    updateManyMutably(updated, state);\n  }\n  return {\n    removeAll: createSingleArgumentStateOperator(removeAllMutably),\n    addOne: createStateOperator(addOneMutably),\n    addMany: createStateOperator(addManyMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    upsertMany: createStateOperator(upsertManyMutably),\n    removeOne: createStateOperator(removeOneMutably),\n    removeMany: createStateOperator(removeManyMutably)\n  };\n}", "import type { IdSelector, Comparer, EntityStateAdapter, Update, EntityId, DraftableEntityState } from './models';\nimport { createStateOperator } from './state_adapter';\nimport { createUnsortedStateAdapter } from './unsorted_state_adapter';\nimport { selectIdValue, ensureEntitiesArray, splitAddedUpdatedEntities, getCurrent } from './utils';\n\n// Borrowed from Replay\nexport function findInsertIndex<T>(sortedItems: T[], item: T, comparisonFunction: Comparer<T>): number {\n  let lowIndex = 0;\n  let highIndex = sortedItems.length;\n  while (lowIndex < highIndex) {\n    let middleIndex = lowIndex + highIndex >>> 1;\n    const currentItem = sortedItems[middleIndex];\n    const res = comparisonFunction(item, currentItem);\n    if (res >= 0) {\n      lowIndex = middleIndex + 1;\n    } else {\n      highIndex = middleIndex;\n    }\n  }\n  return lowIndex;\n}\nexport function insert<T>(sortedItems: T[], item: T, comparisonFunction: Comparer<T>): T[] {\n  const insertAtIndex = findInsertIndex(sortedItems, item, comparisonFunction);\n  sortedItems.splice(insertAtIndex, 0, item);\n  return sortedItems;\n}\nexport function createSortedStateAdapter<T, Id extends EntityId>(selectId: IdSelector<T, Id>, comparer: Comparer<T>): EntityStateAdapter<T, Id> {\n  type R = DraftableEntityState<T, Id>;\n  const {\n    removeOne,\n    removeMany,\n    removeAll\n  } = createUnsortedStateAdapter(selectId);\n  function addOneMutably(entity: T, state: R): void {\n    return addManyMutably([entity], state);\n  }\n  function addManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R, existingIds?: Id[]): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    const existingKeys = new Set<Id>(existingIds ?? getCurrent(state.ids));\n    const models = newEntities.filter(model => !existingKeys.has(selectIdValue(model, selectId)));\n    if (models.length !== 0) {\n      mergeFunction(state, models);\n    }\n  }\n  function setOneMutably(entity: T, state: R): void {\n    return setManyMutably([entity], state);\n  }\n  function setManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    if (newEntities.length !== 0) {\n      for (const item of newEntities) {\n        delete (state.entities as Record<Id, T>)[selectId(item)];\n      }\n      mergeFunction(state, newEntities);\n    }\n  }\n  function setAllMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.entities = {} as Record<Id, T>;\n    state.ids = [];\n    addManyMutably(newEntities, state, []);\n  }\n  function updateOneMutably(update: Update<T, Id>, state: R): void {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates: ReadonlyArray<Update<T, Id>>, state: R): void {\n    let appliedUpdates = false;\n    let replacedIds = false;\n    for (let update of updates) {\n      const entity: T | undefined = (state.entities as Record<Id, T>)[update.id];\n      if (!entity) {\n        continue;\n      }\n      appliedUpdates = true;\n      Object.assign(entity, update.changes);\n      const newId = selectId(entity);\n      if (update.id !== newId) {\n        // We do support the case where updates can change an item's ID.\n        // This makes things trickier - go ahead and swap the IDs in state now.\n        replacedIds = true;\n        delete (state.entities as Record<Id, T>)[update.id];\n        const oldIndex = (state.ids as Id[]).indexOf(update.id);\n        state.ids[oldIndex] = newId;\n        (state.entities as Record<Id, T>)[newId] = entity;\n      }\n    }\n    if (appliedUpdates) {\n      mergeFunction(state, [], appliedUpdates, replacedIds);\n    }\n  }\n  function upsertOneMutably(entity: T, state: R): void {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    const [added, updated, existingIdsArray] = splitAddedUpdatedEntities<T, Id>(newEntities, selectId, state);\n    if (added.length) {\n      addManyMutably(added, state, existingIdsArray);\n    }\n    if (updated.length) {\n      updateManyMutably(updated, state);\n    }\n  }\n  function areArraysEqual(a: readonly unknown[], b: readonly unknown[]) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] === b[i]) {\n        continue;\n      }\n      return false;\n    }\n    return true;\n  }\n  type MergeFunction = (state: R, addedItems: readonly T[], appliedUpdates?: boolean, replacedIds?: boolean) => void;\n  const mergeFunction: MergeFunction = (state, addedItems, appliedUpdates, replacedIds) => {\n    const currentEntities = getCurrent(state.entities);\n    const currentIds = getCurrent(state.ids);\n    const stateEntities = state.entities as Record<Id, T>;\n    let ids: Iterable<Id> = currentIds;\n    if (replacedIds) {\n      ids = new Set(currentIds);\n    }\n    let sortedEntities: T[] = [];\n    for (const id of ids) {\n      const entity = currentEntities[id];\n      if (entity) {\n        sortedEntities.push(entity);\n      }\n    }\n    const wasPreviouslyEmpty = sortedEntities.length === 0;\n\n    // Insert/overwrite all new/updated\n    for (const item of addedItems) {\n      stateEntities[selectId(item)] = item;\n      if (!wasPreviouslyEmpty) {\n        // Binary search insertion generally requires fewer comparisons\n        insert(sortedEntities, item, comparer);\n      }\n    }\n    if (wasPreviouslyEmpty) {\n      // All we have is the incoming values, sort them\n      sortedEntities = addedItems.slice().sort(comparer);\n    } else if (appliedUpdates) {\n      // We should have a _mostly_-sorted array already\n      sortedEntities.sort(comparer);\n    }\n    const newSortedIds = sortedEntities.map(selectId);\n    if (!areArraysEqual(currentIds, newSortedIds)) {\n      state.ids = newSortedIds;\n    }\n  };\n  return {\n    removeOne,\n    removeMany,\n    removeAll,\n    addOne: createStateOperator(addOneMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    addMany: createStateOperator(addManyMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertMany: createStateOperator(upsertManyMutably)\n  };\n}", "import type { EntityAdapter, EntityId, EntityAdapterOptions } from './models';\nimport { createInitialStateFactory } from './entity_state';\nimport { createSelectorsFactory } from './state_selectors';\nimport { createSortedStateAdapter } from './sorted_state_adapter';\nimport { createUnsortedStateAdapter } from './unsorted_state_adapter';\nimport type { WithRequiredProp } from '../tsHelpers';\nexport function createEntityAdapter<T, Id extends EntityId>(options: WithRequiredProp<EntityAdapterOptions<T, Id>, 'selectId'>): EntityAdapter<T, Id>;\nexport function createEntityAdapter<T extends {\n  id: EntityId;\n}>(options?: Omit<EntityAdapterOptions<T, T['id']>, 'selectId'>): EntityAdapter<T, T['id']>;\n\n/**\n *\n * @param options\n *\n * @public\n */\nexport function createEntityAdapter<T>(options: EntityAdapterOptions<T, EntityId> = {}): EntityAdapter<T, EntityId> {\n  const {\n    selectId,\n    sortComparer\n  }: Required<EntityAdapterOptions<T, EntityId>> = {\n    sortComparer: false,\n    selectId: (instance: any) => instance.id,\n    ...options\n  };\n  const stateAdapter = sortComparer ? createSortedStateAdapter(selectId, sortComparer) : createUnsortedStateAdapter(selectId);\n  const stateFactory = createInitialStateFactory(stateAdapter);\n  const selectorsFactory = createSelectorsFactory<T, EntityId>();\n  return {\n    selectId,\n    sortComparer,\n    ...stateFactory,\n    ...selectorsFactory,\n    ...stateAdapter\n  };\n}", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2, formatProdErrorMessage as _formatProdErrorMessage3 } from \"@reduxjs/toolkit\";\nimport type { Action, Dispatch, MiddlewareAPI, UnknownAction } from 'redux';\nimport { isAction } from 'redux';\nimport type { ThunkDispatch } from 'redux-thunk';\nimport { createAction } from '../createAction';\nimport { nanoid } from '../nanoid';\nimport { TaskAbortError, listenerCancelled, listenerCompleted, taskCancelled, taskCompleted } from './exceptions';\nimport { createDelay, createPause, raceWithSignal, runTask, validateActive } from './task';\nimport type { AbortSignalWithReason, AddListenerOverloads, AnyListenerPredicate, CreateListenerMiddlewareOptions, FallbackAddListenerOptions, ForkOptions, ForkedTask, ForkedTaskExecutor, ListenerEntry, ListenerErrorHandler, ListenerErrorInfo, ListenerMiddleware, ListenerMiddlewareInstance, TakePattern, TaskResult, TypedAddListener, TypedCreateListenerEntry, TypedRemoveListener, UnsubscribeListener, UnsubscribeListenerOptions } from './types';\nimport { abortControllerWithReason, addAbortSignalListener, assertFunction, catchRejection, noop } from './utils';\nexport { TaskAbortError } from './exceptions';\nexport type { AsyncTaskExecutor, CreateListenerMiddlewareOptions, ForkedTask, ForkedTaskAPI, ForkedTaskExecutor, ListenerEffect, ListenerEffectAPI, ListenerErrorHandler, ListenerMiddleware, ListenerMiddlewareInstance, SyncTaskExecutor, TaskCancelled, TaskRejected, TaskResolved, TaskResult, TypedAddListener, TypedRemoveListener, TypedStartListening, TypedStopListening, UnsubscribeListener, UnsubscribeListenerOptions } from './types';\n\n//Overly-aggressive byte-shaving\nconst {\n  assign\n} = Object;\n/**\n * @internal\n */\nconst INTERNAL_NIL_TOKEN = {} as const;\nconst alm = 'listenerMiddleware' as const;\nconst createFork = (parentAbortSignal: AbortSignalWithReason<unknown>, parentBlockingPromises: Promise<any>[]) => {\n  const linkControllers = (controller: AbortController) => addAbortSignalListener(parentAbortSignal, () => abortControllerWithReason(controller, parentAbortSignal.reason));\n  return <T,>(taskExecutor: ForkedTaskExecutor<T>, opts?: ForkOptions): ForkedTask<T> => {\n    assertFunction(taskExecutor, 'taskExecutor');\n    const childAbortController = new AbortController();\n    linkControllers(childAbortController);\n    const result = runTask<T>(async (): Promise<T> => {\n      validateActive(parentAbortSignal);\n      validateActive(childAbortController.signal);\n      const result = (await taskExecutor({\n        pause: createPause(childAbortController.signal),\n        delay: createDelay(childAbortController.signal),\n        signal: childAbortController.signal\n      })) as T;\n      validateActive(childAbortController.signal);\n      return result;\n    }, () => abortControllerWithReason(childAbortController, taskCompleted));\n    if (opts?.autoJoin) {\n      parentBlockingPromises.push(result.catch(noop));\n    }\n    return {\n      result: createPause<TaskResult<T>>(parentAbortSignal)(result),\n      cancel() {\n        abortControllerWithReason(childAbortController, taskCancelled);\n      }\n    };\n  };\n};\nconst createTakePattern = <S,>(startListening: AddListenerOverloads<UnsubscribeListener, S, Dispatch>, signal: AbortSignal): TakePattern<S> => {\n  /**\n   * A function that takes a ListenerPredicate and an optional timeout,\n   * and resolves when either the predicate returns `true` based on an action\n   * state combination or when the timeout expires.\n   * If the parent listener is canceled while waiting, this will throw a\n   * TaskAbortError.\n   */\n  const take = async <P extends AnyListenerPredicate<S>,>(predicate: P, timeout: number | undefined) => {\n    validateActive(signal);\n\n    // Placeholder unsubscribe function until the listener is added\n    let unsubscribe: UnsubscribeListener = () => {};\n    const tuplePromise = new Promise<[Action, S, S]>((resolve, reject) => {\n      // Inside the Promise, we synchronously add the listener.\n      let stopListening = startListening({\n        predicate: predicate as any,\n        effect: (action, listenerApi): void => {\n          // One-shot listener that cleans up as soon as the predicate passes\n          listenerApi.unsubscribe();\n          // Resolve the promise with the same arguments the predicate saw\n          resolve([action, listenerApi.getState(), listenerApi.getOriginalState()]);\n        }\n      });\n      unsubscribe = () => {\n        stopListening();\n        reject();\n      };\n    });\n    const promises: (Promise<null> | Promise<[Action, S, S]>)[] = [tuplePromise];\n    if (timeout != null) {\n      promises.push(new Promise<null>(resolve => setTimeout(resolve, timeout, null)));\n    }\n    try {\n      const output = await raceWithSignal(signal, Promise.race(promises));\n      validateActive(signal);\n      return output;\n    } finally {\n      // Always clean up the listener\n      unsubscribe();\n    }\n  };\n  return ((predicate: AnyListenerPredicate<S>, timeout: number | undefined) => catchRejection(take(predicate, timeout))) as TakePattern<S>;\n};\nconst getListenerEntryPropsFrom = (options: FallbackAddListenerOptions) => {\n  let {\n    type,\n    actionCreator,\n    matcher,\n    predicate,\n    effect\n  } = options;\n  if (type) {\n    predicate = createAction(type).match;\n  } else if (actionCreator) {\n    type = actionCreator!.type;\n    predicate = actionCreator.match;\n  } else if (matcher) {\n    predicate = matcher;\n  } else if (predicate) {\n    // pass\n  } else {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(21) : 'Creating or removing a listener requires one of the known fields for matching an action');\n  }\n  assertFunction(effect, 'options.listener');\n  return {\n    predicate,\n    type,\n    effect\n  };\n};\n\n/** Accepts the possible options for creating a listener, and returns a formatted listener entry */\nexport const createListenerEntry: TypedCreateListenerEntry<unknown> = /* @__PURE__ */assign((options: FallbackAddListenerOptions) => {\n  const {\n    type,\n    predicate,\n    effect\n  } = getListenerEntryPropsFrom(options);\n  const entry: ListenerEntry<unknown> = {\n    id: nanoid(),\n    effect,\n    type,\n    predicate,\n    pending: new Set<AbortController>(),\n    unsubscribe: () => {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(22) : 'Unsubscribe not initialized');\n    }\n  };\n  return entry;\n}, {\n  withTypes: () => createListenerEntry\n}) as unknown as TypedCreateListenerEntry<unknown>;\nconst findListenerEntry = (listenerMap: Map<string, ListenerEntry>, options: FallbackAddListenerOptions) => {\n  const {\n    type,\n    effect,\n    predicate\n  } = getListenerEntryPropsFrom(options);\n  return Array.from(listenerMap.values()).find(entry => {\n    const matchPredicateOrType = typeof type === 'string' ? entry.type === type : entry.predicate === predicate;\n    return matchPredicateOrType && entry.effect === effect;\n  });\n};\nconst cancelActiveListeners = (entry: ListenerEntry<unknown, Dispatch<UnknownAction>>) => {\n  entry.pending.forEach(controller => {\n    abortControllerWithReason(controller, listenerCancelled);\n  });\n};\nconst createClearListenerMiddleware = (listenerMap: Map<string, ListenerEntry>) => {\n  return () => {\n    listenerMap.forEach(cancelActiveListeners);\n    listenerMap.clear();\n  };\n};\n\n/**\n * Safely reports errors to the `errorHandler` provided.\n * Errors that occur inside `errorHandler` are notified in a new task.\n * Inspired by [rxjs reportUnhandledError](https://github.com/ReactiveX/rxjs/blob/6fafcf53dc9e557439b25debaeadfd224b245a66/src/internal/util/reportUnhandledError.ts)\n * @param errorHandler\n * @param errorToNotify\n */\nconst safelyNotifyError = (errorHandler: ListenerErrorHandler, errorToNotify: unknown, errorInfo: ListenerErrorInfo): void => {\n  try {\n    errorHandler(errorToNotify, errorInfo);\n  } catch (errorHandlerError) {\n    // We cannot let an error raised here block the listener queue.\n    // The error raised here will be picked up by `window.onerror`, `process.on('error')` etc...\n    setTimeout(() => {\n      throw errorHandlerError;\n    }, 0);\n  }\n};\n\n/**\n * @public\n */\nexport const addListener = /* @__PURE__ */assign(/* @__PURE__ */createAction(`${alm}/add`), {\n  withTypes: () => addListener\n}) as unknown as TypedAddListener<unknown>;\n\n/**\n * @public\n */\nexport const clearAllListeners = /* @__PURE__ */createAction(`${alm}/removeAll`);\n\n/**\n * @public\n */\nexport const removeListener = /* @__PURE__ */assign(/* @__PURE__ */createAction(`${alm}/remove`), {\n  withTypes: () => removeListener\n}) as unknown as TypedRemoveListener<unknown>;\nconst defaultErrorHandler: ListenerErrorHandler = (...args: unknown[]) => {\n  console.error(`${alm}/error`, ...args);\n};\n\n/**\n * @public\n */\nexport const createListenerMiddleware = <StateType = unknown, DispatchType extends Dispatch<Action> = ThunkDispatch<StateType, unknown, UnknownAction>, ExtraArgument = unknown>(middlewareOptions: CreateListenerMiddlewareOptions<ExtraArgument> = {}) => {\n  const listenerMap = new Map<string, ListenerEntry>();\n  const {\n    extra,\n    onError = defaultErrorHandler\n  } = middlewareOptions;\n  assertFunction(onError, 'onError');\n  const insertEntry = (entry: ListenerEntry) => {\n    entry.unsubscribe = () => listenerMap.delete(entry.id);\n    listenerMap.set(entry.id, entry);\n    return (cancelOptions?: UnsubscribeListenerOptions) => {\n      entry.unsubscribe();\n      if (cancelOptions?.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    };\n  };\n  const startListening = ((options: FallbackAddListenerOptions) => {\n    const entry = findListenerEntry(listenerMap, options) ?? createListenerEntry(options as any);\n    return insertEntry(entry);\n  }) as AddListenerOverloads<any>;\n  assign(startListening, {\n    withTypes: () => startListening\n  });\n  const stopListening = (options: FallbackAddListenerOptions & UnsubscribeListenerOptions): boolean => {\n    const entry = findListenerEntry(listenerMap, options);\n    if (entry) {\n      entry.unsubscribe();\n      if (options.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    }\n    return !!entry;\n  };\n  assign(stopListening, {\n    withTypes: () => stopListening\n  });\n  const notifyListener = async (entry: ListenerEntry<unknown, Dispatch<UnknownAction>>, action: unknown, api: MiddlewareAPI, getOriginalState: () => StateType) => {\n    const internalTaskController = new AbortController();\n    const take = createTakePattern(startListening as AddListenerOverloads<any>, internalTaskController.signal);\n    const autoJoinPromises: Promise<any>[] = [];\n    try {\n      entry.pending.add(internalTaskController);\n      await Promise.resolve(entry.effect(action,\n      // Use assign() rather than ... to avoid extra helper functions added to bundle\n      assign({}, api, {\n        getOriginalState,\n        condition: (predicate: AnyListenerPredicate<any>, timeout?: number) => take(predicate, timeout).then(Boolean),\n        take,\n        delay: createDelay(internalTaskController.signal),\n        pause: createPause<any>(internalTaskController.signal),\n        extra,\n        signal: internalTaskController.signal,\n        fork: createFork(internalTaskController.signal, autoJoinPromises),\n        unsubscribe: entry.unsubscribe,\n        subscribe: () => {\n          listenerMap.set(entry.id, entry);\n        },\n        cancelActiveListeners: () => {\n          entry.pending.forEach((controller, _, set) => {\n            if (controller !== internalTaskController) {\n              abortControllerWithReason(controller, listenerCancelled);\n              set.delete(controller);\n            }\n          });\n        },\n        cancel: () => {\n          abortControllerWithReason(internalTaskController, listenerCancelled);\n          entry.pending.delete(internalTaskController);\n        },\n        throwIfCancelled: () => {\n          validateActive(internalTaskController.signal);\n        }\n      })));\n    } catch (listenerError) {\n      if (!(listenerError instanceof TaskAbortError)) {\n        safelyNotifyError(onError, listenerError, {\n          raisedBy: 'effect'\n        });\n      }\n    } finally {\n      await Promise.all(autoJoinPromises);\n      abortControllerWithReason(internalTaskController, listenerCompleted); // Notify that the task has completed\n      entry.pending.delete(internalTaskController);\n    }\n  };\n  const clearListenerMiddleware = createClearListenerMiddleware(listenerMap);\n  const middleware: ListenerMiddleware<StateType, DispatchType, ExtraArgument> = api => next => action => {\n    if (!isAction(action)) {\n      // we only want to notify listeners for action objects\n      return next(action);\n    }\n    if (addListener.match(action)) {\n      return startListening(action.payload as any);\n    }\n    if (clearAllListeners.match(action)) {\n      clearListenerMiddleware();\n      return;\n    }\n    if (removeListener.match(action)) {\n      return stopListening(action.payload);\n    }\n\n    // Need to get this state _before_ the reducer processes the action\n    let originalState: StateType | typeof INTERNAL_NIL_TOKEN = api.getState();\n\n    // `getOriginalState` can only be called synchronously.\n    // @see https://github.com/reduxjs/redux-toolkit/discussions/1648#discussioncomment-1932820\n    const getOriginalState = (): StateType => {\n      if (originalState === INTERNAL_NIL_TOKEN) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(23) : `${alm}: getOriginalState can only be called synchronously`);\n      }\n      return originalState as StateType;\n    };\n    let result: unknown;\n    try {\n      // Actually forward the action to the reducer before we handle listeners\n      result = next(action);\n      if (listenerMap.size > 0) {\n        const currentState = api.getState();\n        // Work around ESBuild+TS transpilation issue\n        const listenerEntries = Array.from(listenerMap.values());\n        for (const entry of listenerEntries) {\n          let runListener = false;\n          try {\n            runListener = entry.predicate(action, currentState, originalState);\n          } catch (predicateError) {\n            runListener = false;\n            safelyNotifyError(onError, predicateError, {\n              raisedBy: 'predicate'\n            });\n          }\n          if (!runListener) {\n            continue;\n          }\n          notifyListener(entry, action, api, getOriginalState);\n        }\n      }\n    } finally {\n      // Remove `originalState` store from this scope.\n      originalState = INTERNAL_NIL_TOKEN;\n    }\n    return result;\n  };\n  return {\n    middleware,\n    startListening,\n    stopListening,\n    clearListeners: clearListenerMiddleware\n  } as ListenerMiddlewareInstance<StateType, DispatchType, ExtraArgument>;\n};", "import type { SerializedError } from '@reduxjs/toolkit';\nconst task = 'task';\nconst listener = 'listener';\nconst completed = 'completed';\nconst cancelled = 'cancelled';\n\n/* TaskAbortError error codes  */\nexport const taskCancelled = `task-${cancelled}` as const;\nexport const taskCompleted = `task-${completed}` as const;\nexport const listenerCancelled = `${listener}-${cancelled}` as const;\nexport const listenerCompleted = `${listener}-${completed}` as const;\nexport class TaskAbortError implements SerializedError {\n  name = 'TaskAbortError';\n  message: string;\n  constructor(public code: string | undefined) {\n    this.message = `${task} ${cancelled} (reason: ${code})`;\n  }\n}", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport type { AbortSignalWithReason } from './types';\nexport const assertFunction: (func: unknown, expected: string) => asserts func is (...args: unknown[]) => unknown = (func: unknown, expected: string) => {\n  if (typeof func !== 'function') {\n    throw new TypeError(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(32) : `${expected} is not a function`);\n  }\n};\nexport const noop = () => {};\nexport const catchRejection = <T,>(promise: Promise<T>, onError = noop): Promise<T> => {\n  promise.catch(onError);\n  return promise;\n};\nexport const addAbortSignalListener = (abortSignal: AbortSignal, callback: (evt: Event) => void) => {\n  abortSignal.addEventListener('abort', callback, {\n    once: true\n  });\n  return () => abortSignal.removeEventListener('abort', callback);\n};\n\n/**\n * Calls `abortController.abort(reason)` and patches `signal.reason`.\n * if it is not supported.\n *\n * At the time of writing `signal.reason` is available in FF chrome, edge node 17 and deno.\n * @param abortController\n * @param reason\n * @returns\n * @see https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/reason\n */\nexport const abortControllerWithReason = <T,>(abortController: AbortController, reason: T): void => {\n  type Consumer<T> = (val: T) => void;\n  const signal = abortController.signal as AbortSignalWithReason<T>;\n  if (signal.aborted) {\n    return;\n  }\n\n  // Patch `reason` if necessary.\n  // - We use defineProperty here because reason is a getter of `AbortSignal.__proto__`.\n  // - We need to patch 'reason' before calling `.abort()` because listeners to the 'abort'\n  // event are are notified immediately.\n  if (!('reason' in signal)) {\n    Object.defineProperty(signal, 'reason', {\n      enumerable: true,\n      value: reason,\n      configurable: true,\n      writable: true\n    });\n  }\n  ;\n  (abortController.abort as Consumer<typeof reason>)(reason);\n};", "import { TaskAbortError } from './exceptions';\nimport type { AbortSignalWithReason, TaskResult } from './types';\nimport { addAbortSignalListener, catchRejection, noop } from './utils';\n\n/**\n * Synchronously raises {@link TaskAbortError} if the task tied to the input `signal` has been cancelled.\n * @param signal\n * @param reason\n * @see {TaskAbortError}\n */\nexport const validateActive = (signal: AbortSignal): void => {\n  if (signal.aborted) {\n    const {\n      reason\n    } = signal as AbortSignalWithReason<string>;\n    throw new TaskAbortError(reason);\n  }\n};\n\n/**\n * Generates a race between the promise(s) and the AbortSignal\n * This avoids `Promise.race()`-related memory leaks:\n * https://github.com/nodejs/node/issues/17469#issuecomment-349794909\n */\nexport function raceWithSignal<T>(signal: AbortSignalWithReason<string>, promise: Promise<T>): Promise<T> {\n  let cleanup = noop;\n  return new Promise<T>((resolve, reject) => {\n    const notifyRejection = () => reject(new TaskAbortError(signal.reason));\n    if (signal.aborted) {\n      notifyRejection();\n      return;\n    }\n    cleanup = addAbortSignalListener(signal, notifyRejection);\n    promise.finally(() => cleanup()).then(resolve, reject);\n  }).finally(() => {\n    // after this point, replace `cleanup` with a noop, so there is no reference to `signal` any more\n    cleanup = noop;\n  });\n}\n\n/**\n * Runs a task and returns promise that resolves to {@link TaskResult}.\n * Second argument is an optional `cleanUp` function that always runs after task.\n *\n * **Note:** `runTask` runs the executor in the next microtask.\n * @returns\n */\nexport const runTask = async <T,>(task: () => Promise<T>, cleanUp?: () => void): Promise<TaskResult<T>> => {\n  try {\n    await Promise.resolve();\n    const value = await task();\n    return {\n      status: 'ok',\n      value\n    };\n  } catch (error: any) {\n    return {\n      status: error instanceof TaskAbortError ? 'cancelled' : 'rejected',\n      error\n    };\n  } finally {\n    cleanUp?.();\n  }\n};\n\n/**\n * Given an input `AbortSignal` and a promise returns another promise that resolves\n * as soon the input promise is provided or rejects as soon as\n * `AbortSignal.abort` is `true`.\n * @param signal\n * @returns\n */\nexport const createPause = <T,>(signal: AbortSignal) => {\n  return (promise: Promise<T>): Promise<T> => {\n    return catchRejection(raceWithSignal(signal, promise).then(output => {\n      validateActive(signal);\n      return output;\n    }));\n  };\n};\n\n/**\n * Given an input `AbortSignal` and `timeoutMs` returns a promise that resolves\n * after `timeoutMs` or rejects as soon as `AbortSignal.abort` is `true`.\n * @param signal\n * @returns\n */\nexport const createDelay = (signal: AbortSignal) => {\n  const pause = createPause<void>(signal);\n  return (timeoutMs: number): Promise<void> => {\n    return pause(new Promise<void>(resolve => setTimeout(resolve, timeoutMs)));\n  };\n};", "import type { Dispatch, Middleware, UnknownAction } from 'redux';\nimport { compose } from 'redux';\nimport { createAction } from '../createAction';\nimport { isAllOf } from '../matchers';\nimport { nanoid } from '../nanoid';\nimport { getOrInsertComputed } from '../utils';\nimport type { AddMiddleware, DynamicMiddleware, DynamicMiddlewareInstance, MiddlewareEntry, WithMiddleware } from './types';\nexport type { DynamicMiddlewareInstance, GetDispatchType as GetDispatch, MiddlewareApiConfig } from './types';\nconst createMiddlewareEntry = <State = any, DispatchType extends Dispatch<UnknownAction> = Dispatch<UnknownAction>>(middleware: Middleware<any, State, DispatchType>): MiddlewareEntry<State, DispatchType> => ({\n  middleware,\n  applied: new Map()\n});\nconst matchInstance = (instanceId: string) => (action: any): action is {\n  meta: {\n    instanceId: string;\n  };\n} => action?.meta?.instanceId === instanceId;\nexport const createDynamicMiddleware = <State = any, DispatchType extends Dispatch<UnknownAction> = Dispatch<UnknownAction>>(): DynamicMiddlewareInstance<State, DispatchType> => {\n  const instanceId = nanoid();\n  const middlewareMap = new Map<Middleware<any, State, DispatchType>, MiddlewareEntry<State, DispatchType>>();\n  const withMiddleware = Object.assign(createAction('dynamicMiddleware/add', (...middlewares: Middleware<any, State, DispatchType>[]) => ({\n    payload: middlewares,\n    meta: {\n      instanceId\n    }\n  })), {\n    withTypes: () => withMiddleware\n  }) as WithMiddleware<State, DispatchType>;\n  const addMiddleware = Object.assign(function addMiddleware(...middlewares: Middleware<any, State, DispatchType>[]) {\n    middlewares.forEach(middleware => {\n      getOrInsertComputed(middlewareMap, middleware, createMiddlewareEntry);\n    });\n  }, {\n    withTypes: () => addMiddleware\n  }) as AddMiddleware<State, DispatchType>;\n  const getFinalMiddleware: Middleware<{}, State, DispatchType> = api => {\n    const appliedMiddleware = Array.from(middlewareMap.values()).map(entry => getOrInsertComputed(entry.applied, api, entry.middleware));\n    return compose(...appliedMiddleware);\n  };\n  const isWithMiddleware = isAllOf(withMiddleware, matchInstance(instanceId));\n  const middleware: DynamicMiddleware<State, DispatchType> = api => next => action => {\n    if (isWithMiddleware(action)) {\n      addMiddleware(...action.payload);\n      return api.dispatch;\n    }\n    return getFinalMiddleware(api)(next)(action);\n  };\n  return {\n    middleware,\n    addMiddleware,\n    withMiddleware,\n    instanceId\n  };\n};", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2 } from \"@reduxjs/toolkit\";\nimport type { Reducer, StateFromReducersMapObject, UnknownAction } from 'redux';\nimport { combineReducers } from 'redux';\nimport { nanoid } from './nanoid';\nimport type { Id, NonUndefined, Tail, UnionToIntersection, WithOptionalProp } from './tsHelpers';\nimport { getOrInsertComputed } from './utils';\ntype SliceLike<ReducerPath extends string, State> = {\n  reducerPath: ReducerPath;\n  reducer: Reducer<State>;\n};\ntype AnySliceLike = SliceLike<string, any>;\ntype SliceLikeReducerPath<A extends AnySliceLike> = A extends SliceLike<infer ReducerPath, any> ? ReducerPath : never;\ntype SliceLikeState<A extends AnySliceLike> = A extends SliceLike<any, infer State> ? State : never;\nexport type WithSlice<A extends AnySliceLike> = { [Path in SliceLikeReducerPath<A>]: SliceLikeState<A> };\ntype ReducerMap = Record<string, Reducer>;\ntype ExistingSliceLike<DeclaredState> = { [ReducerPath in keyof DeclaredState]: SliceLike<ReducerPath & string, NonUndefined<DeclaredState[ReducerPath]>> }[keyof DeclaredState];\nexport type InjectConfig = {\n  /**\n   * Allow replacing reducer with a different reference. Normally, an error will be thrown if a different reducer instance to the one already injected is used.\n   */\n  overrideExisting?: boolean;\n};\n\n/**\n * A reducer that allows for slices/reducers to be injected after initialisation.\n */\nexport interface CombinedSliceReducer<InitialState, DeclaredState = InitialState> extends Reducer<DeclaredState, UnknownAction, Partial<DeclaredState>> {\n  /**\n   * Provide a type for slices that will be injected lazily.\n   *\n   * One way to do this would be with interface merging:\n   * ```ts\n   *\n   * export interface LazyLoadedSlices {}\n   *\n   * export const rootReducer = combineSlices(stringSlice).withLazyLoadedSlices<LazyLoadedSlices>();\n   *\n   * // elsewhere\n   *\n   * declare module './reducer' {\n   *   export interface LazyLoadedSlices extends WithSlice<typeof booleanSlice> {}\n   * }\n   *\n   * const withBoolean = rootReducer.inject(booleanSlice);\n   *\n   * // elsewhere again\n   *\n   * declare module './reducer' {\n   *   export interface LazyLoadedSlices {\n   *     customName: CustomState\n   *   }\n   * }\n   *\n   * const withCustom = rootReducer.inject({ reducerPath: \"customName\", reducer: customSlice.reducer })\n   * ```\n   */\n  withLazyLoadedSlices<Lazy = {}>(): CombinedSliceReducer<InitialState, Id<DeclaredState & Partial<Lazy>>>;\n\n  /**\n   * Inject a slice.\n   *\n   * Accepts an individual slice, RTKQ API instance, or a \"slice-like\" { reducerPath, reducer } object.\n   *\n   * ```ts\n   * rootReducer.inject(booleanSlice)\n   * rootReducer.inject(baseApi)\n   * rootReducer.inject({ reducerPath: 'boolean' as const, reducer: newReducer }, { overrideExisting: true })\n   * ```\n   *\n   */\n  inject<Sl extends Id<ExistingSliceLike<DeclaredState>>>(slice: Sl, config?: InjectConfig): CombinedSliceReducer<InitialState, Id<DeclaredState & WithSlice<Sl>>>;\n\n  /**\n   * Inject a slice.\n   *\n   * Accepts an individual slice, RTKQ API instance, or a \"slice-like\" { reducerPath, reducer } object.\n   *\n   * ```ts\n   * rootReducer.inject(booleanSlice)\n   * rootReducer.inject(baseApi)\n   * rootReducer.inject({ reducerPath: 'boolean' as const, reducer: newReducer }, { overrideExisting: true })\n   * ```\n   *\n   */\n  inject<ReducerPath extends string, State>(slice: SliceLike<ReducerPath, State & (ReducerPath extends keyof DeclaredState ? never : State)>, config?: InjectConfig): CombinedSliceReducer<InitialState, Id<DeclaredState & WithSlice<SliceLike<ReducerPath, State>>>>;\n\n  /**\n   * Create a selector that guarantees that the slices injected will have a defined value when selector is run.\n   *\n   * ```ts\n   * const selectBooleanWithoutInjection = (state: RootState) => state.boolean;\n   * //                                                                ^? boolean | undefined\n   *\n   * const selectBoolean = rootReducer.inject(booleanSlice).selector((state) => {\n   *   // if action hasn't been dispatched since slice was injected, this would usually be undefined\n   *   // however selector() uses a Proxy around the first parameter to ensure that it evaluates to the initial state instead, if undefined\n   *   return state.boolean;\n   *   //           ^? boolean\n   * })\n   * ```\n   *\n   * If the reducer is nested inside the root state, a selectState callback can be passed to retrieve the reducer's state.\n   *\n   * ```ts\n   *\n   * export interface LazyLoadedSlices {};\n   *\n   * export const innerReducer = combineSlices(stringSlice).withLazyLoadedSlices<LazyLoadedSlices>();\n   *\n   * export const rootReducer = combineSlices({ inner: innerReducer });\n   *\n   * export type RootState = ReturnType<typeof rootReducer>;\n   *\n   * // elsewhere\n   *\n   * declare module \"./reducer.ts\" {\n   *  export interface LazyLoadedSlices extends WithSlice<typeof booleanSlice> {}\n   * }\n   *\n   * const withBool = innerReducer.inject(booleanSlice);\n   *\n   * const selectBoolean = withBool.selector(\n   *   (state) => state.boolean,\n   *   (rootState: RootState) => state.inner\n   * );\n   * //    now expects to be passed RootState instead of innerReducer state\n   *\n   * ```\n   *\n   * Value passed to selectorFn will be a Proxy - use selector.original(proxy) to get original state value (useful for debugging)\n   *\n   * ```ts\n   * const injectedReducer = rootReducer.inject(booleanSlice);\n   * const selectBoolean = injectedReducer.selector((state) => {\n   *   console.log(injectedReducer.selector.original(state).boolean) // possibly undefined\n   *   return state.boolean\n   * })\n   * ```\n   */\n  selector: {\n    /**\n     * Create a selector that guarantees that the slices injected will have a defined value when selector is run.\n     *\n     * ```ts\n     * const selectBooleanWithoutInjection = (state: RootState) => state.boolean;\n     * //                                                                ^? boolean | undefined\n     *\n     * const selectBoolean = rootReducer.inject(booleanSlice).selector((state) => {\n     *   // if action hasn't been dispatched since slice was injected, this would usually be undefined\n     *   // however selector() uses a Proxy around the first parameter to ensure that it evaluates to the initial state instead, if undefined\n     *   return state.boolean;\n     *   //           ^? boolean\n     * })\n     * ```\n     *\n     * Value passed to selectorFn will be a Proxy - use selector.original(proxy) to get original state value (useful for debugging)\n     *\n     * ```ts\n     * const injectedReducer = rootReducer.inject(booleanSlice);\n     * const selectBoolean = injectedReducer.selector((state) => {\n     *   console.log(injectedReducer.selector.original(state).boolean) // undefined\n     *   return state.boolean\n     * })\n     * ```\n     */\n    <Selector extends (state: DeclaredState, ...args: any[]) => unknown>(selectorFn: Selector): (state: WithOptionalProp<Parameters<Selector>[0], Exclude<keyof DeclaredState, keyof InitialState>>, ...args: Tail<Parameters<Selector>>) => ReturnType<Selector>;\n\n    /**\n     * Create a selector that guarantees that the slices injected will have a defined value when selector is run.\n     *\n     * ```ts\n     * const selectBooleanWithoutInjection = (state: RootState) => state.boolean;\n     * //                                                                ^? boolean | undefined\n     *\n     * const selectBoolean = rootReducer.inject(booleanSlice).selector((state) => {\n     *   // if action hasn't been dispatched since slice was injected, this would usually be undefined\n     *   // however selector() uses a Proxy around the first parameter to ensure that it evaluates to the initial state instead, if undefined\n     *   return state.boolean;\n     *   //           ^? boolean\n     * })\n     * ```\n     *\n     * If the reducer is nested inside the root state, a selectState callback can be passed to retrieve the reducer's state.\n     *\n     * ```ts\n     *\n     * interface LazyLoadedSlices {};\n     *\n     * const innerReducer = combineSlices(stringSlice).withLazyLoadedSlices<LazyLoadedSlices>();\n     *\n     * const rootReducer = combineSlices({ inner: innerReducer });\n     *\n     * type RootState = ReturnType<typeof rootReducer>;\n     *\n     * // elsewhere\n     *\n     * declare module \"./reducer.ts\" {\n     *  interface LazyLoadedSlices extends WithSlice<typeof booleanSlice> {}\n     * }\n     *\n     * const withBool = innerReducer.inject(booleanSlice);\n     *\n     * const selectBoolean = withBool.selector(\n     *   (state) => state.boolean,\n     *   (rootState: RootState) => state.inner\n     * );\n     * //    now expects to be passed RootState instead of innerReducer state\n     *\n     * ```\n     *\n     * Value passed to selectorFn will be a Proxy - use selector.original(proxy) to get original state value (useful for debugging)\n     *\n     * ```ts\n     * const injectedReducer = rootReducer.inject(booleanSlice);\n     * const selectBoolean = injectedReducer.selector((state) => {\n     *   console.log(injectedReducer.selector.original(state).boolean) // possibly undefined\n     *   return state.boolean\n     * })\n     * ```\n     */\n    <Selector extends (state: DeclaredState, ...args: any[]) => unknown, RootState>(selectorFn: Selector, selectState: (rootState: RootState, ...args: Tail<Parameters<Selector>>) => WithOptionalProp<Parameters<Selector>[0], Exclude<keyof DeclaredState, keyof InitialState>>): (state: RootState, ...args: Tail<Parameters<Selector>>) => ReturnType<Selector>;\n    /**\n     * Returns the unproxied state. Useful for debugging.\n     * @param state state Proxy, that ensures injected reducers have value\n     * @returns original, unproxied state\n     * @throws if value passed is not a state Proxy\n     */\n    original: (state: DeclaredState) => InitialState & Partial<DeclaredState>;\n  };\n}\ntype InitialState<Slices extends Array<AnySliceLike | ReducerMap>> = UnionToIntersection<Slices[number] extends infer Slice ? Slice extends AnySliceLike ? WithSlice<Slice> : StateFromReducersMapObject<Slice> : never>;\nconst isSliceLike = (maybeSliceLike: AnySliceLike | ReducerMap): maybeSliceLike is AnySliceLike => 'reducerPath' in maybeSliceLike && typeof maybeSliceLike.reducerPath === 'string';\nconst getReducers = (slices: Array<AnySliceLike | ReducerMap>) => slices.flatMap(sliceOrMap => isSliceLike(sliceOrMap) ? [[sliceOrMap.reducerPath, sliceOrMap.reducer] as const] : Object.entries(sliceOrMap));\nconst ORIGINAL_STATE = Symbol.for('rtk-state-proxy-original');\nconst isStateProxy = (value: any) => !!value && !!value[ORIGINAL_STATE];\nconst stateProxyMap = new WeakMap<object, object>();\nconst createStateProxy = <State extends object,>(state: State, reducerMap: Partial<Record<PropertyKey, Reducer>>, initialStateCache: Record<PropertyKey, unknown>) => getOrInsertComputed(stateProxyMap, state, () => new Proxy(state, {\n  get: (target, prop, receiver) => {\n    if (prop === ORIGINAL_STATE) return target;\n    const result = Reflect.get(target, prop, receiver);\n    if (typeof result === 'undefined') {\n      const cached = initialStateCache[prop];\n      if (typeof cached !== 'undefined') return cached;\n      const reducer = reducerMap[prop];\n      if (reducer) {\n        // ensure action type is random, to prevent reducer treating it differently\n        const reducerResult = reducer(undefined, {\n          type: nanoid()\n        });\n        if (typeof reducerResult === 'undefined') {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(24) : `The slice reducer for key \"${prop.toString()}\" returned undefined when called for selector(). ` + `If the state passed to the reducer is undefined, you must ` + `explicitly return the initial state. The initial state may ` + `not be undefined. If you don't want to set a value for this reducer, ` + `you can use null instead of undefined.`);\n        }\n        initialStateCache[prop] = reducerResult;\n        return reducerResult;\n      }\n    }\n    return result;\n  }\n})) as State;\nconst original = (state: any) => {\n  if (!isStateProxy(state)) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(25) : 'original must be used on state Proxy');\n  }\n  return state[ORIGINAL_STATE];\n};\nconst emptyObject = {};\nconst noopReducer: Reducer<Record<string, any>> = (state = emptyObject) => state;\nexport function combineSlices<Slices extends Array<AnySliceLike | ReducerMap>>(...slices: Slices): CombinedSliceReducer<Id<InitialState<Slices>>> {\n  const reducerMap = Object.fromEntries<Reducer>(getReducers(slices));\n  const getReducer = () => Object.keys(reducerMap).length ? combineReducers(reducerMap) : noopReducer;\n  let reducer = getReducer();\n  function combinedReducer(state: Record<string, unknown>, action: UnknownAction) {\n    return reducer(state, action);\n  }\n  combinedReducer.withLazyLoadedSlices = () => combinedReducer;\n  const initialStateCache: Record<PropertyKey, unknown> = {};\n  const inject = (slice: AnySliceLike, config: InjectConfig = {}): typeof combinedReducer => {\n    const {\n      reducerPath,\n      reducer: reducerToInject\n    } = slice;\n    const currentReducer = reducerMap[reducerPath];\n    if (!config.overrideExisting && currentReducer && currentReducer !== reducerToInject) {\n      if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {\n        console.error(`called \\`inject\\` to override already-existing reducer ${reducerPath} without specifying \\`overrideExisting: true\\``);\n      }\n      return combinedReducer;\n    }\n    if (config.overrideExisting && currentReducer !== reducerToInject) {\n      delete initialStateCache[reducerPath];\n    }\n    reducerMap[reducerPath] = reducerToInject;\n    reducer = getReducer();\n    return combinedReducer;\n  };\n  const selector = Object.assign(function makeSelector<State extends object, RootState, Args extends any[]>(selectorFn: (state: State, ...args: Args) => any, selectState?: (rootState: RootState, ...args: Args) => State) {\n    return function selector(state: State, ...args: Args) {\n      return selectorFn(createStateProxy(selectState ? selectState(state as any, ...args) : state, reducerMap, initialStateCache), ...args);\n    };\n  }, {\n    original\n  });\n  return Object.assign(combinedReducer, {\n    inject,\n    selector\n  }) as any;\n}", "/**\r\n * Adapted from React: https://github.com/facebook/react/blob/master/packages/shared/formatProdErrorMessage.js\r\n *\r\n * Do not require this module directly! Use normal throw error calls. These messages will be replaced with error codes\r\n * during build.\r\n * @param {number} code\r\n */\nexport function formatProdErrorMessage(code: number) {\n  return `Minified Redux Toolkit error #${code}; visit https://redux-toolkit.js.org/Errors?code=${code} for the full message or ` + 'use the non-minified dev environment for full errors. ';\n}"], "mappings": ";;;;;ACKA,IAAM,gBAA+B,MAAM,OAAO,WAAW,cAAc,OAAO,cAAc,gBAAgB;AAChH,IAAO,4BAAQ;ACCf,IAAM,eAAe,MAAM,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,CAAC,EAAE,MAAM,EAAE,EAAE,KAAK,GAAG;AACrF,IAAM,cAAc;EAClB,MAAM,eAA8B,aAAa,CAAC;EAClD,SAAS,kBAAiC,aAAa,CAAC;EACxD,sBAAsB,MAAM,+BAA+B,aAAa,CAAC;AAC3E;AACA,IAAO,sBAAQ;ACTA,SAAR,cAA+B,KAAyB;AAC7D,MAAI,OAAO,QAAQ,YAAY,QAAQ;AAAM,WAAO;AACpD,MAAIA,SAAQ;AACZ,SAAO,OAAO,eAAeA,MAAK,MAAM,MAAM;AAC5C,IAAAA,SAAQ,OAAO,eAAeA,MAAK;EACrC;AACA,SAAO,OAAO,eAAe,GAAG,MAAMA,UAAS,OAAO,eAAe,GAAG,MAAM;AAChF;ACVO,SAAS,WAAW,KAAkB;AAC3C,MAAI,QAAQ;AAAQ,WAAO;AAC3B,MAAI,QAAQ;AAAM,WAAO;AACzB,QAAM,OAAO,OAAO;AACpB,UAAQ,MAAM;IACZ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK,YACH;AACE,aAAO;IACT;EACJ;AACA,MAAI,MAAM,QAAQ,GAAG;AAAG,WAAO;AAC/B,MAAI,OAAO,GAAG;AAAG,WAAO;AACxB,MAAI,QAAQ,GAAG;AAAG,WAAO;AACzB,QAAM,kBAAkB,SAAS,GAAG;AACpC,UAAQ,iBAAiB;IACvB,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACH,aAAO;EACX;AAGA,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,EAAE,MAAM,GAAG,EAAE,EAAE,YAAY,EAAE,QAAQ,OAAO,EAAE;AACzF;AACA,SAAS,SAAS,KAAyB;AACzC,SAAO,OAAO,IAAI,gBAAgB,aAAa,IAAI,YAAY,OAAO;AACxE;AACA,SAAS,QAAQ,KAAU;AACzB,SAAO,eAAe,SAAS,OAAO,IAAI,YAAY,YAAY,IAAI,eAAe,OAAO,IAAI,YAAY,oBAAoB;AAClI;AACA,SAAS,OAAO,KAAU;AACxB,MAAI,eAAe;AAAM,WAAO;AAChC,SAAO,OAAO,IAAI,iBAAiB,cAAc,OAAO,IAAI,YAAY,cAAc,OAAO,IAAI,YAAY;AAC/G;AACO,SAAS,OAAO,KAAU;AAC/B,MAAI,YAAoB,OAAO;AAC/B,MAAI,MAAuC;AACzC,gBAAY,WAAW,GAAG;EAC5B;AACA,SAAO;AACT;ACyBO,SAAS,YAAoG,SAAwC,gBAA4E,UAA4F;AAClU,MAAI,OAAO,YAAY,YAAY;AACjC,UAAM,IAAI,MAAM,QAAwC,uBAAwB,CAAC,IAAI,mEAAmE,OAAO,OAAO,CAAC,GAAG;EAC5K;AACA,MAAI,OAAO,mBAAmB,cAAc,OAAO,aAAa,cAAc,OAAO,aAAa,cAAc,OAAO,UAAU,CAAC,MAAM,YAAY;AAClJ,UAAM,IAAI,MAAM,QAAwC,uBAAyB,CAAC,IAAI,kQAA4Q;EACpW;AACA,MAAI,OAAO,mBAAmB,cAAc,OAAO,aAAa,aAAa;AAC3E,eAAY;AACZ,qBAAiB;EACnB;AACA,MAAI,OAAO,aAAa,aAAa;AACnC,QAAI,OAAO,aAAa,YAAY;AAClC,YAAM,IAAI,MAAM,QAAwC,uBAAyB,CAAC,IAAI,+DAA+D,OAAO,QAAQ,CAAC,GAAG;IAC1K;AACA,WAAO,SAAS,WAAW,EAAE,SAAU,cAA6C;EACtF;AACA,MAAI,iBAAiB;AACrB,MAAI,eAAgD;AACpD,MAAI,mBAAyD,oBAAI,IAAI;AACrE,MAAI,gBAAgB;AACpB,MAAI,oBAAoB;AACxB,MAAI,gBAAgB;AASpB,WAAS,+BAA+B;AACtC,QAAI,kBAAkB,kBAAkB;AACtC,sBAAgB,oBAAI,IAAI;AACxB,uBAAiB,QAAQ,CAACC,WAAU,QAAQ;AAC1C,sBAAc,IAAI,KAAKA,SAAQ;MACjC,CAAC;IACH;EACF;AAOA,WAAS,WAAc;AACrB,QAAI,eAAe;AACjB,YAAM,IAAI,MAAM,QAAwC,uBAAyB,CAAC,IAAI,sMAAgN;IACxS;AACA,WAAQ;EACV;AAyBA,WAAS,UAAUA,WAAsB;AACvC,QAAI,OAAOA,cAAa,YAAY;AAClC,YAAM,IAAI,MAAM,QAAwC,uBAAyB,CAAC,IAAI,+DAA+D,OAAOA,SAAQ,CAAC,GAAG;IAC1K;AACA,QAAI,eAAe;AACjB,YAAM,IAAI,MAAM,QAAwC,uBAAyB,CAAC,IAAI,iTAAgU;IACxZ;AACA,QAAI,eAAe;AACnB,iCAA6B;AAC7B,UAAM,aAAa;AACnB,kBAAc,IAAI,YAAYA,SAAQ;AACtC,WAAO,SAAS,cAAc;AAC5B,UAAI,CAAC,cAAc;AACjB;MACF;AACA,UAAI,eAAe;AACjB,cAAM,IAAI,MAAM,QAAwC,uBAAyB,CAAC,IAAI,sJAA2J;MACnP;AACA,qBAAe;AACf,mCAA6B;AAC7B,oBAAc,OAAO,UAAU;AAC/B,yBAAmB;IACrB;EACF;AA2BA,WAAS,SAAS,QAAW;AAC3B,QAAI,CAAC,cAAc,MAAM,GAAG;AAC1B,YAAM,IAAI,MAAM,QAAwC,uBAAyB,CAAC,IAAI,iEAAiE,OAAO,MAAM,CAAC,4UAA4U;IACnf;AACA,QAAI,OAAO,OAAO,SAAS,aAAa;AACtC,YAAM,IAAI,MAAM,QAAwC,uBAAyB,CAAC,IAAI,4GAA4G;IACpM;AACA,QAAI,OAAO,OAAO,SAAS,UAAU;AACnC,YAAM,IAAI,MAAM,QAAwC,uBAA0B,EAAE,IAAI,2EAA2E,OAAO,OAAO,IAAI,CAAC,kBAAkB,OAAO,IAAI,iBAAiB;IACtO;AACA,QAAI,eAAe;AACjB,YAAM,IAAI,MAAM,QAAwC,uBAA0B,CAAC,IAAI,oCAAoC;IAC7H;AACA,QAAI;AACF,sBAAgB;AAChB,qBAAe,eAAe,cAAc,MAAM;IACpD,UAAA;AACE,sBAAgB;IAClB;AACA,UAAM,YAAY,mBAAmB;AACrC,cAAU,QAAQ,CAAAA,cAAY;AAC5B,MAAAA,UAAS;IACX,CAAC;AACD,WAAO;EACT;AAWA,WAAS,eAAe,aAAkC;AACxD,QAAI,OAAO,gBAAgB,YAAY;AACrC,YAAM,IAAI,MAAM,QAAwC,uBAA0B,EAAE,IAAI,kEAAkE,OAAO,WAAW,CAAC,EAAE;IACjL;AACA,qBAAmB;AAMnB,aAAU;MACR,MAAM,oBAAY;IACpB,CAAO;EACT;AAQA,WAAS,aAAa;AACpB,UAAM,iBAAiB;AACvB,WAAO;;;;;;;;;MASL,UAAU,UAAmB;AAC3B,YAAI,OAAO,aAAa,YAAY,aAAa,MAAM;AACrD,gBAAM,IAAI,MAAM,QAAwC,uBAA0B,EAAE,IAAI,8DAA8D,OAAO,QAAQ,CAAC,GAAG;QAC3K;AACA,iBAAS,eAAe;AACtB,gBAAM,qBAAsB;AAC5B,cAAI,mBAAmB,MAAM;AAC3B,+BAAmB,KAAK,SAAS,CAAC;UACpC;QACF;AACA,qBAAa;AACb,cAAM,cAAc,eAAe,YAAY;AAC/C,eAAO;UACL;QACF;MACF;MACA,CAAC,yBAAY,IAAI;AACf,eAAO;MACT;IACF;EACF;AAKA,WAAU;IACR,MAAM,oBAAY;EACpB,CAAO;AACP,QAAM,QAAU;IACd;IACA;IACA;IACA;IACA,CAAC,yBAAY,GAAG;EAClB;AACA,SAAO;AACT;AAgEO,SAAS,mBAA2G,SAAwB,gBAA4E,UAA4F;AACzT,SAAO,YAAY,SAAU,gBAAwB,QAAQ;AAC/D;AC1We,SAAR,QAAyB,SAAuB;AAErD,MAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,UAAU,YAAY;AACzE,YAAQ,MAAM,OAAO;EACvB;AAEA,MAAI;AAIF,UAAM,IAAI,MAAM,OAAO;EACzB,SAAS,GAAG;EAAC;AACf;ACRA,SAAS,sCAAsC,YAAoB,UAEhE,QAAgB,oBAEhB;AACD,QAAM,cAAc,OAAO,KAAK,QAAQ;AACxC,QAAM,eAAe,UAAU,OAAO,SAAS,oBAAY,OAAO,kDAAkD;AACpH,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO;EACT;AACA,MAAI,CAAC,cAAc,UAAU,GAAG;AAC9B,WAAO,OAAO,YAAY,4BAA4B,OAAO,UAAU,CAAC,kEAAuE,YAAY,KAAK,MAAM,CAAC;EACzK;AACA,QAAM,iBAAiB,OAAO,KAAK,UAAU,EAAE,OAAO,CAAA,QAAO,CAAC,SAAS,eAAe,GAAG,KAAK,CAAC,mBAAmB,GAAG,CAAC;AACtH,iBAAe,QAAQ,CAAA,QAAO;AAC5B,uBAAmB,GAAG,IAAI;EAC5B,CAAC;AACD,MAAI,UAAU,OAAO,SAAS,oBAAY;AAAS;AACnD,MAAI,eAAe,SAAS,GAAG;AAC7B,WAAO,cAAc,eAAe,SAAS,IAAI,SAAS,KAAK,KAAU,eAAe,KAAK,MAAM,CAAC,cAAc,YAAY,8DAAwE,YAAY,KAAK,MAAM,CAAC;EAChO;AACF;AACA,SAAS,mBAAmB,UAEzB;AACD,SAAO,KAAK,QAAQ,EAAE,QAAQ,CAAA,QAAO;AACnC,UAAM,UAAU,SAAS,GAAG;AAC5B,UAAM,eAAe,QAAQ,QAAW;MACtC,MAAM,oBAAY;IACpB,CAAC;AACD,QAAI,OAAO,iBAAiB,aAAa;AACvC,YAAM,IAAI,MAAM,QAAwC,uBAAwB,EAAE,IAAI,8BAA8B,GAAG,8QAAkS;IAC3Z;AACA,QAAI,OAAO,QAAQ,QAAW;MAC5B,MAAM,oBAAY,qBAAqB;IACzC,CAAC,MAAM,aAAa;AAClB,YAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,8BAA8B,GAAG,6EAAkF,oBAAY,IAAI,8SAAkU;IAC9hB;EACF,CAAC;AACH;AAqBe,SAAR,gBAAiC,UAErC;AACD,QAAM,cAAc,OAAO,KAAK,QAAQ;AACxC,QAAM,gBAEF,CAAC;AACL,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,UAAM,MAAM,YAAY,CAAC;AACzB,QAAI,MAAuC;AACzC,UAAI,OAAO,SAAS,GAAG,MAAM,aAAa;AACxC,gBAAQ,gCAAgC,GAAG,GAAG;MAChD;IACF;AACA,QAAI,OAAO,SAAS,GAAG,MAAM,YAAY;AACvC,oBAAc,GAAG,IAAI,SAAS,GAAG;IACnC;EACF;AACA,QAAM,mBAAmB,OAAO,KAAK,aAAa;AAIlD,MAAI;AAGJ,MAAI,MAAuC;AACzC,yBAAqB,CAAC;EACxB;AACA,MAAI;AACJ,MAAI;AACF,uBAAmB,aAAa;EAClC,SAAS,GAAG;AACV,0BAAsB;EACxB;AACA,SAAO,SAAS,YAAY,QAAqD,CAAC,GAAG,QAAgB;AACnG,QAAI,qBAAqB;AACvB,YAAM;IACR;AACA,QAAI,MAAuC;AACzC,YAAM,iBAAiB,sCAAsC,OAAO,eAAe,QAAQ,kBAAkB;AAC7G,UAAI,gBAAgB;AAClB,gBAAQ,cAAc;MACxB;IACF;AACA,QAAI,aAAa;AACjB,UAAM,YAAyD,CAAC;AAChE,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAChD,YAAM,MAAM,iBAAiB,CAAC;AAC9B,YAAM,UAAU,cAAc,GAAG;AACjC,YAAM,sBAAsB,MAAM,GAAG;AACrC,YAAM,kBAAkB,QAAQ,qBAAqB,MAAM;AAC3D,UAAI,OAAO,oBAAoB,aAAa;AAC1C,cAAM,aAAa,UAAU,OAAO;AACpC,cAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,sCAAsC,aAAa,IAAI,OAAO,UAAU,CAAC,MAAM,gBAAgB,gCAAgC,GAAG,gLAA0L;MACrZ;AACA,gBAAU,GAAG,IAAI;AACjB,mBAAa,cAAc,oBAAoB;IACjD;AACA,iBAAa,cAAc,iBAAiB,WAAW,OAAO,KAAK,KAAK,EAAE;AAC1E,WAAO,aAAa,YAAY;EAClC;AACF;AC9HA,SAAS,kBAAoC,eAAiC,UAAuB;AACnG,SAAO,YAAwB,MAAa;AAC1C,WAAO,SAAS,cAAc,MAAM,MAAM,IAAI,CAAC;EACjD;AACF;AA2Be,SAAR,mBAAoC,gBAA8D,UAAoB;AAC3H,MAAI,OAAO,mBAAmB,YAAY;AACxC,WAAO,kBAAkB,gBAAgB,QAAQ;EACnD;AACA,MAAI,OAAO,mBAAmB,YAAY,mBAAmB,MAAM;AACjE,UAAM,IAAI,MAAM,QAAwC,uBAAwB,EAAE,IAAI,+EAA+E,OAAO,cAAc,CAAC,6FAAkG;EAC/R;AACA,QAAM,sBAA+C,CAAC;AACtD,aAAW,OAAO,gBAAgB;AAChC,UAAM,gBAAgB,eAAe,GAAG;AACxC,QAAI,OAAO,kBAAkB,YAAY;AACvC,0BAAoB,GAAG,IAAI,kBAAkB,eAAe,QAAQ;IACtE;EACF;AACA,SAAO;AACT;ACvBe,SAAR,WAA4B,OAAmB;AACpD,MAAI,MAAM,WAAW,GAAG;AAEtB,WAAO,CAAK,QAAW;EACzB;AACA,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,MAAM,CAAC;EAChB;AACA,SAAO,MAAM,OAAO,CAAC,GAAG,MAAM,IAAI,SAAc,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC;AAC/D;ACOe,SAAR,mBAAoC,aAA+C;AACxF,SAAO,CAAAC,iBAAe,CAAC,SAAS,mBAAmB;AACjD,UAAM,QAAQA,aAAY,SAAS,cAAc;AACjD,QAAI,WAAqB,MAAM;AAC7B,YAAM,IAAI,MAAM,QAAwC,uBAAwB,EAAE,IAAI,wHAA6H;IACrN;AACA,UAAM,gBAA+B;MACnC,UAAU,MAAM;MAChB,UAAU,CAAC,WAAW,SAAS,SAAS,QAAQ,GAAG,IAAI;IACzD;AACA,UAAM,QAAQ,YAAY,IAAI,CAAA,eAAc,WAAW,aAAa,CAAC;AACrE,eAAW,QAAyB,GAAG,KAAK,EAAE,MAAM,QAAQ;AAC5D,WAAO;MACL,GAAG;MACH;IACF;EACF;AACF;AC1De,SAAR,SAA0B,QAA2C;AAC1E,SAAO,cAAc,MAAM,KAAK,UAAU,UAAU,OAAQ,OAAmC,SAAS;AAC1G;;;ACCO,IAAM,UAAyB,OAAO,IAAI,eAAe;AAUzD,IAAM,YAA2B,OAAO,IAAI,iBAAiB;AAE7D,IAAM,cAA6B,OAAO,IAAI,aAAa;ACjB3D,IAAM,SACZ,OACG;;EAEA,SAAS,QAAgB;AACxB,WAAO,mBAAmB,MAAA,mFAAyF,MAAA;EACpH;EACA,SAAS,OAAe;AACvB,WAAO,sJAAsJ,KAAA;EAC9J;EACA;EACA,SAAS,MAAW;AACnB,WACC,yHACA;EAEF;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,OAAe;AACvB,WAAO,mCAAmC,KAAA;EAC3C;EACA;EACA;EACA;EACA;EACA,SAAS,OAAe;AACvB,WAAO,oCAAoC,KAAA;EAC5C;;;AAGA,IACA,CAAC;AAEE,SAAS,IAAI,UAAkB,MAAoB;AACzD,MAAI,MAAuC;AAC1C,UAAM,IAAI,OAAO,KAAK;AACtB,UAAM,MAAM,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,IAAW,IAAI;AACnE,UAAM,IAAI,MAAM,WAAW,GAAA,EAAK;EACjC;AACA,QAAM,IAAI;IACT,8BAA8B,KAAA;EAC/B;AACD;ACjCO,IAAM,iBAAiB,OAAO;AAI9B,SAAS,QAAQ,OAAqB;AAC5C,SAAO,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,WAAW;AACtC;AAIO,SAAS,YAAY,OAAqB;;AAChD,MAAI,CAAC;AAAO,WAAO;AACnB,SACCC,eAAc,KAAK,KACnB,MAAM,QAAQ,KAAK,KACnB,CAAC,CAAC,MAAM,SAAS,KACjB,CAAC,GAAC,WAAM,gBAAN,mBAAoB,eACtB,MAAM,KAAK,KACX,MAAM,KAAK;AAEb;AAEA,IAAM,mBAAmB,OAAO,UAAU,YAAY,SAAS;AAExD,SAASA,eAAc,OAAqB;AAClD,MAAI,CAAC,SAAS,OAAO,UAAU;AAAU,WAAO;AAChD,QAAMC,SAAQ,eAAe,KAAK;AAClC,MAAIA,WAAU,MAAM;AACnB,WAAO;EACR;AACA,QAAM,OACL,OAAO,eAAe,KAAKA,QAAO,aAAa,KAAKA,OAAM;AAE3D,MAAI,SAAS;AAAQ,WAAO;AAE5B,SACC,OAAO,QAAQ,cACf,SAAS,SAAS,KAAK,IAAI,MAAM;AAEnC;AAKO,SAAS,SAAS,OAA0B;AAClD,MAAI,CAAC,QAAQ,KAAK;AAAG,QAAI,IAAI,KAAK;AAClC,SAAO,MAAM,WAAW,EAAE;AAC3B;AAWO,SAAS,KAAK,KAAU,MAAW;AACzC,MAAI,YAAY,GAAG,MAAA,GAAuB;AACzC,YAAQ,QAAQ,GAAG,EAAE,QAAQ,CAAA,QAAO;AACnC,WAAK,KAAK,IAAI,GAAG,GAAG,GAAG;IACxB,CAAC;EACF,OAAO;AACN,QAAI,QAAQ,CAAC,OAAY,UAAe,KAAK,OAAO,OAAO,GAAG,CAAC;EAChE;AACD;AAGO,SAAS,YAAY,OAAsB;AACjD,QAAM,QAAgC,MAAM,WAAW;AACvD,SAAO,QACJ,MAAM,QACN,MAAM,QAAQ,KAAK,IAAA,IAEnB,MAAM,KAAK,IAAA,IAEX,MAAM,KAAK,IAAA,IAAA;AAGf;AAGO,SAAS,IAAI,OAAY,MAA4B;AAC3D,SAAO,YAAY,KAAK,MAAA,IACrB,MAAM,IAAI,IAAI,IACd,OAAO,UAAU,eAAe,KAAK,OAAO,IAAI;AACpD;AASO,SAAS,IAAI,OAAY,gBAA6B,OAAY;AACxE,QAAM,IAAI,YAAY,KAAK;AAC3B,MAAI,MAAA;AAAoB,UAAM,IAAI,gBAAgB,KAAK;WAC9C,MAAA,GAAoB;AAC5B,UAAM,IAAI,KAAK;EAChB;AAAO,UAAM,cAAc,IAAI;AAChC;AAGO,SAAS,GAAG,GAAQ,GAAiB;AAE3C,MAAI,MAAM,GAAG;AACZ,WAAO,MAAM,KAAK,IAAI,MAAM,IAAI;EACjC,OAAO;AACN,WAAO,MAAM,KAAK,MAAM;EACzB;AACD;AAGO,SAAS,MAAM,QAA+B;AACpD,SAAO,kBAAkB;AAC1B;AAGO,SAAS,MAAM,QAA+B;AACpD,SAAO,kBAAkB;AAC1B;AAEO,SAAS,OAAO,OAAwB;AAC9C,SAAO,MAAM,SAAS,MAAM;AAC7B;AAGO,SAAS,YAAY,MAAW,QAAoB;AAC1D,MAAI,MAAM,IAAI,GAAG;AAChB,WAAO,IAAI,IAAI,IAAI;EACpB;AACA,MAAI,MAAM,IAAI,GAAG;AAChB,WAAO,IAAI,IAAI,IAAI;EACpB;AACA,MAAI,MAAM,QAAQ,IAAI;AAAG,WAAO,MAAM,UAAU,MAAM,KAAK,IAAI;AAE/D,QAAMC,WAAUC,eAAc,IAAI;AAElC,MAAI,WAAW,QAAS,WAAW,gBAAgB,CAACD,UAAU;AAE7D,UAAM,cAAc,OAAO,0BAA0B,IAAI;AACzD,WAAO,YAAY,WAAkB;AACrC,QAAI,OAAO,QAAQ,QAAQ,WAAW;AACtC,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACrC,YAAM,MAAW,KAAK,CAAC;AACvB,YAAM,OAAO,YAAY,GAAG;AAC5B,UAAI,KAAK,aAAa,OAAO;AAC5B,aAAK,WAAW;AAChB,aAAK,eAAe;MACrB;AAIA,UAAI,KAAK,OAAO,KAAK;AACpB,oBAAY,GAAG,IAAI;UAClB,cAAc;UACd,UAAU;;UACV,YAAY,KAAK;UACjB,OAAO,KAAK,GAAG;QAChB;IACF;AACA,WAAO,OAAO,OAAO,eAAe,IAAI,GAAG,WAAW;EACvD,OAAO;AAEN,UAAME,SAAQ,eAAe,IAAI;AACjC,QAAIA,WAAU,QAAQF,UAAS;AAC9B,aAAO,EAAC,GAAG,KAAI;IAChB;AACA,UAAM,MAAM,OAAO,OAAOE,MAAK;AAC/B,WAAO,OAAO,OAAO,KAAK,IAAI;EAC/B;AACD;AAUO,SAAS,OAAU,KAAU,OAAgB,OAAU;AAC7D,MAAI,SAAS,GAAG,KAAK,QAAQ,GAAG,KAAK,CAAC,YAAY,GAAG;AAAG,WAAO;AAC/D,MAAI,YAAY,GAAG,IAAI,GAAoB;AAC1C,QAAI,MAAM,IAAI,MAAM,IAAI,QAAQ,IAAI,SAAS;EAC9C;AACA,SAAO,OAAO,GAAG;AACjB,MAAI;AAGH,WAAO,QAAQ,GAAG,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC;AAClE,SAAO;AACR;AAEA,SAAS,8BAA8B;AACtC,MAAI,CAAC;AACN;AAEO,SAAS,SAAS,KAAmB;AAC3C,SAAO,OAAO,SAAS,GAAG;AAC3B;AC5MA,IAAM,UAoBF,CAAC;AAIE,SAAS,UACf,WACiC;AACjC,QAAM,SAAS,QAAQ,SAAS;AAChC,MAAI,CAAC,QAAQ;AACZ,QAAI,GAAG,SAAS;EACjB;AAEA,SAAO;AACR;ACrBA,IAAI;AAEG,SAAS,kBAAkB;AACjC,SAAO;AACR;AAEA,SAAS,YACR,SACA,QACa;AACb,SAAO;IACN,SAAS,CAAC;IACV;IACA;;;IAGA,gBAAgB;IAChB,oBAAoB;EACrB;AACD;AAEO,SAAS,kBACf,OACA,eACC;AACD,MAAI,eAAe;AAClB,cAAU,SAAS;AACnB,UAAM,WAAW,CAAC;AAClB,UAAM,kBAAkB,CAAC;AACzB,UAAM,iBAAiB;EACxB;AACD;AAEO,SAAS,YAAY,OAAmB;AAC9C,aAAW,KAAK;AAChB,QAAM,QAAQ,QAAQ,WAAW;AAEjC,QAAM,UAAU;AACjB;AAEO,SAAS,WAAW,OAAmB;AAC7C,MAAI,UAAU,cAAc;AAC3B,mBAAe,MAAM;EACtB;AACD;AAEO,SAAS,WAAWC,QAAc;AACxC,SAAQ,eAAe,YAAY,cAAcA,MAAK;AACvD;AAEA,SAAS,YAAY,OAAgB;AACpC,QAAM,QAAoB,MAAM,WAAW;AAC3C,MAAI,MAAM,UAAA,KAA6B,MAAM,UAAA;AAC5C,UAAM,QAAQ;;AACV,UAAM,WAAW;AACvB;AC3DO,SAAS,cAAc,QAAa,OAAmB;AAC7D,QAAM,qBAAqB,MAAM,QAAQ;AACzC,QAAM,YAAY,MAAM,QAAS,CAAC;AAClC,QAAM,aAAa,WAAW,UAAa,WAAW;AACtD,MAAI,YAAY;AACf,QAAI,UAAU,WAAW,EAAE,WAAW;AACrC,kBAAY,KAAK;AACjB,UAAI,CAAC;IACN;AACA,QAAI,YAAY,MAAM,GAAG;AAExB,eAAS,SAAS,OAAO,MAAM;AAC/B,UAAI,CAAC,MAAM;AAAS,oBAAY,OAAO,MAAM;IAC9C;AACA,QAAI,MAAM,UAAU;AACnB,gBAAU,SAAS,EAAE;QACpB,UAAU,WAAW,EAAE;QACvB;QACA,MAAM;QACN,MAAM;MACP;IACD;EACD,OAAO;AAEN,aAAS,SAAS,OAAO,WAAW,CAAC,CAAC;EACvC;AACA,cAAY,KAAK;AACjB,MAAI,MAAM,UAAU;AACnB,UAAM,eAAgB,MAAM,UAAU,MAAM,eAAgB;EAC7D;AACA,SAAO,WAAW,UAAU,SAAS;AACtC;AAEA,SAAS,SAAS,WAAuB,OAAY,MAAkB;AAEtE,MAAI,SAAS,KAAK;AAAG,WAAO;AAE5B,QAAM,QAAoB,MAAM,WAAW;AAE3C,MAAI,CAAC,OAAO;AACX;MAAK;MAAO,CAAC,KAAK,eACjB,iBAAiB,WAAW,OAAO,OAAO,KAAK,YAAY,IAAI;IAChE;AACA,WAAO;EACR;AAEA,MAAI,MAAM,WAAW;AAAW,WAAO;AAEvC,MAAI,CAAC,MAAM,WAAW;AACrB,gBAAY,WAAW,MAAM,OAAO,IAAI;AACxC,WAAO,MAAM;EACd;AAEA,MAAI,CAAC,MAAM,YAAY;AACtB,UAAM,aAAa;AACnB,UAAM,OAAO;AACb,UAAM,SAAS,MAAM;AAKrB,QAAI,aAAa;AACjB,QAAIC,SAAQ;AACZ,QAAI,MAAM,UAAA,GAAwB;AACjC,mBAAa,IAAI,IAAI,MAAM;AAC3B,aAAO,MAAM;AACbA,eAAQ;IACT;AACA;MAAK;MAAY,CAAC,KAAK,eACtB,iBAAiB,WAAW,OAAO,QAAQ,KAAK,YAAY,MAAMA,MAAK;IACxE;AAEA,gBAAY,WAAW,QAAQ,KAAK;AAEpC,QAAI,QAAQ,UAAU,UAAU;AAC/B,gBAAU,SAAS,EAAE;QACpB;QACA;QACA,UAAU;QACV,UAAU;MACX;IACD;EACD;AACA,SAAO,MAAM;AACd;AAEA,SAAS,iBACR,WACA,aACA,cACA,MACA,YACA,UACA,aACC;AACD,MAA6C,eAAe;AAC3D,QAAI,CAAC;AACN,MAAI,QAAQ,UAAU,GAAG;AACxB,UAAM,OACL,YACA,eACA,YAAa,UAAA;IACb,CAAC,IAAK,YAA8C,WAAY,IAAI,IACjE,SAAU,OAAO,IAAI,IACrB;AAEJ,UAAM,MAAM,SAAS,WAAW,YAAY,IAAI;AAChD,QAAI,cAAc,MAAM,GAAG;AAG3B,QAAI,QAAQ,GAAG,GAAG;AACjB,gBAAU,iBAAiB;IAC5B;AAAO;EACR,WAAW,aAAa;AACvB,iBAAa,IAAI,UAAU;EAC5B;AAEA,MAAI,YAAY,UAAU,KAAK,CAAC,SAAS,UAAU,GAAG;AACrD,QAAI,CAAC,UAAU,OAAO,eAAe,UAAU,qBAAqB,GAAG;AAMtE;IACD;AACA,aAAS,WAAW,UAAU;AAI9B,SACE,CAAC,eAAe,CAAC,YAAY,OAAO,YACrC,OAAO,SAAS,YAChB,OAAO,UAAU,qBAAqB,KAAK,cAAc,IAAI;AAE7D,kBAAY,WAAW,UAAU;EACnC;AACD;AAEA,SAAS,YAAY,OAAmB,OAAY,OAAO,OAAO;AAEjE,MAAI,CAAC,MAAM,WAAW,MAAM,OAAO,eAAe,MAAM,gBAAgB;AACvE,WAAO,OAAO,IAAI;EACnB;AACD;ACjHO,SAAS,iBACf,MACA,QACyB;AACzB,QAAM,UAAU,MAAM,QAAQ,IAAI;AAClC,QAAM,QAAoB;IACzB,OAAO,UAAA,IAAA;;IAEP,QAAQ,SAAS,OAAO,SAAS,gBAAgB;;IAEjD,WAAW;;IAEX,YAAY;;IAEZ,WAAW,CAAC;;IAEZ,SAAS;;IAET,OAAO;;IAEP,QAAQ;;;IAER,OAAO;;IAEP,SAAS;IACT,WAAW;EACZ;AAQA,MAAI,SAAY;AAChB,MAAI,QAA2C;AAC/C,MAAI,SAAS;AACZ,aAAS,CAAC,KAAK;AACf,YAAQ;EACT;AAEA,QAAM,EAAC,QAAQ,MAAK,IAAI,MAAM,UAAU,QAAQ,KAAK;AACrD,QAAM,SAAS;AACf,QAAM,UAAU;AAChB,SAAO;AACR;AAKO,IAAM,cAAwC;EACpD,IAAI,OAAO,MAAM;AAChB,QAAI,SAAS;AAAa,aAAO;AAEjC,UAAM,SAAS,OAAO,KAAK;AAC3B,QAAI,CAAC,IAAI,QAAQ,IAAI,GAAG;AAEvB,aAAO,kBAAkB,OAAO,QAAQ,IAAI;IAC7C;AACA,UAAM,QAAQ,OAAO,IAAI;AACzB,QAAI,MAAM,cAAc,CAAC,YAAY,KAAK,GAAG;AAC5C,aAAO;IACR;AAGA,QAAI,UAAU,KAAK,MAAM,OAAO,IAAI,GAAG;AACtC,kBAAY,KAAK;AACjB,aAAQ,MAAM,MAAO,IAAW,IAAI,YAAY,OAAO,KAAK;IAC7D;AACA,WAAO;EACR;EACA,IAAI,OAAO,MAAM;AAChB,WAAO,QAAQ,OAAO,KAAK;EAC5B;EACA,QAAQ,OAAO;AACd,WAAO,QAAQ,QAAQ,OAAO,KAAK,CAAC;EACrC;EACA,IACC,OACA,MACA,OACC;AACD,UAAM,OAAO,uBAAuB,OAAO,KAAK,GAAG,IAAI;AACvD,QAAI,6BAAM,KAAK;AAGd,WAAK,IAAI,KAAK,MAAM,QAAQ,KAAK;AACjC,aAAO;IACR;AACA,QAAI,CAAC,MAAM,WAAW;AAGrB,YAAMC,WAAU,KAAK,OAAO,KAAK,GAAG,IAAI;AAExC,YAAM,eAAiCA,qCAAU;AACjD,UAAI,gBAAgB,aAAa,UAAU,OAAO;AACjD,cAAM,MAAO,IAAI,IAAI;AACrB,cAAM,UAAU,IAAI,IAAI;AACxB,eAAO;MACR;AACA,UAAI,GAAG,OAAOA,QAAO,MAAM,UAAU,UAAa,IAAI,MAAM,OAAO,IAAI;AACtE,eAAO;AACR,kBAAY,KAAK;AACjB,kBAAY,KAAK;IAClB;AAEA,QACE,MAAM,MAAO,IAAI,MAAM;KAEtB,UAAU,UAAa,QAAQ,MAAM;IAEtC,OAAO,MAAM,KAAK,KAAK,OAAO,MAAM,MAAM,MAAO,IAAI,CAAC;AAEvD,aAAO;AAGR,UAAM,MAAO,IAAI,IAAI;AACrB,UAAM,UAAU,IAAI,IAAI;AACxB,WAAO;EACR;EACA,eAAe,OAAO,MAAc;AAEnC,QAAI,KAAK,MAAM,OAAO,IAAI,MAAM,UAAa,QAAQ,MAAM,OAAO;AACjE,YAAM,UAAU,IAAI,IAAI;AACxB,kBAAY,KAAK;AACjB,kBAAY,KAAK;IAClB,OAAO;AAEN,aAAO,MAAM,UAAU,IAAI;IAC5B;AACA,QAAI,MAAM,OAAO;AAChB,aAAO,MAAM,MAAM,IAAI;IACxB;AACA,WAAO;EACR;;;EAGA,yBAAyB,OAAO,MAAM;AACrC,UAAM,QAAQ,OAAO,KAAK;AAC1B,UAAM,OAAO,QAAQ,yBAAyB,OAAO,IAAI;AACzD,QAAI,CAAC;AAAM,aAAO;AAClB,WAAO;MACN,UAAU;MACV,cAAc,MAAM,UAAA,KAA4B,SAAS;MACzD,YAAY,KAAK;MACjB,OAAO,MAAM,IAAI;IAClB;EACD;EACA,iBAAiB;AAChB,QAAI,EAAE;EACP;EACA,eAAe,OAAO;AACrB,WAAO,eAAe,MAAM,KAAK;EAClC;EACA,iBAAiB;AAChB,QAAI,EAAE;EACP;AACD;AAMA,IAAM,aAA8C,CAAC;AACrD,KAAK,aAAa,CAAC,KAAK,OAAO;AAE9B,aAAW,GAAG,IAAI,WAAW;AAC5B,cAAU,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC;AAC7B,WAAO,GAAG,MAAM,MAAM,SAAS;EAChC;AACD,CAAC;AACD,WAAW,iBAAiB,SAAS,OAAO,MAAM;AACjD,MAA6C,MAAM,SAAS,IAAW,CAAC;AACvE,QAAI,EAAE;AAEP,SAAO,WAAW,IAAK,KAAK,MAAM,OAAO,MAAM,MAAS;AACzD;AACA,WAAW,MAAM,SAAS,OAAO,MAAM,OAAO;AAC7C,MAEC,SAAS,YACT,MAAM,SAAS,IAAW,CAAC;AAE3B,QAAI,EAAE;AACP,SAAO,YAAY,IAAK,KAAK,MAAM,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,CAAC,CAAC;AACnE;AAGA,SAAS,KAAK,OAAgB,MAAmB;AAChD,QAAM,QAAQ,MAAM,WAAW;AAC/B,QAAM,SAAS,QAAQ,OAAO,KAAK,IAAI;AACvC,SAAO,OAAO,IAAI;AACnB;AAEA,SAAS,kBAAkB,OAAmB,QAAa,MAAmB;;AAC7E,QAAM,OAAO,uBAAuB,QAAQ,IAAI;AAChD,SAAO,OACJ,WAAW,OACV,KAAK;;;KAGL,UAAK,QAAL,mBAAU,KAAK,MAAM;MACtB;AACJ;AAEA,SAAS,uBACR,QACA,MACiC;AAEjC,MAAI,EAAE,QAAQ;AAAS,WAAO;AAC9B,MAAIC,SAAQ,eAAe,MAAM;AACjC,SAAOA,QAAO;AACb,UAAM,OAAO,OAAO,yBAAyBA,QAAO,IAAI;AACxD,QAAI;AAAM,aAAO;AACjB,IAAAA,SAAQ,eAAeA,MAAK;EAC7B;AACA,SAAO;AACR;AAEO,SAAS,YAAY,OAAmB;AAC9C,MAAI,CAAC,MAAM,WAAW;AACrB,UAAM,YAAY;AAClB,QAAI,MAAM,SAAS;AAClB,kBAAY,MAAM,OAAO;IAC1B;EACD;AACD;AAEO,SAAS,YAAY,OAIzB;AACF,MAAI,CAAC,MAAM,OAAO;AACjB,UAAM,QAAQ;MACb,MAAM;MACN,MAAM,OAAO,OAAO;IACrB;EACD;AACD;AChQO,IAAMC,SAAN,MAAoC;EAI1C,YAAY,QAGT;AANH,SAAA,cAAuB;AACvB,SAAA,wBAAoC;AA+BpC,SAAA,UAAoB,CAAC,MAAW,QAAc,kBAAwB;AAErE,UAAI,OAAO,SAAS,cAAc,OAAO,WAAW,YAAY;AAC/D,cAAM,cAAc;AACpB,iBAAS;AAET,cAAM,OAAO;AACb,eAAO,SAAS,eAEfC,QAAO,gBACJ,MACF;AACD,iBAAO,KAAK,QAAQA,OAAM,CAAC,UAAmB,OAAO,KAAK,MAAM,OAAO,GAAG,IAAI,CAAC;QAChF;MACD;AAEA,UAAI,OAAO,WAAW;AAAY,YAAI,CAAC;AACvC,UAAI,kBAAkB,UAAa,OAAO,kBAAkB;AAC3D,YAAI,CAAC;AAEN,UAAI;AAGJ,UAAI,YAAY,IAAI,GAAG;AACtB,cAAM,QAAQ,WAAW,IAAI;AAC7B,cAAM,QAAQ,YAAY,MAAM,MAAS;AACzC,YAAI,WAAW;AACf,YAAI;AACH,mBAAS,OAAO,KAAK;AACrB,qBAAW;QACZ,UAAA;AAEC,cAAI;AAAU,wBAAY,KAAK;;AAC1B,uBAAW,KAAK;QACtB;AACA,0BAAkB,OAAO,aAAa;AACtC,eAAO,cAAc,QAAQ,KAAK;MACnC,WAAW,CAAC,QAAQ,OAAO,SAAS,UAAU;AAC7C,iBAAS,OAAO,IAAI;AACpB,YAAI,WAAW;AAAW,mBAAS;AACnC,YAAI,WAAW;AAAS,mBAAS;AACjC,YAAI,KAAK;AAAa,iBAAO,QAAQ,IAAI;AACzC,YAAI,eAAe;AAClB,gBAAM,IAAa,CAAC;AACpB,gBAAM,KAAc,CAAC;AACrB,oBAAU,SAAS,EAAE,4BAA4B,MAAM,QAAQ,GAAG,EAAE;AACpE,wBAAc,GAAG,EAAE;QACpB;AACA,eAAO;MACR;AAAO,YAAI,GAAG,IAAI;IACnB;AAEA,SAAA,qBAA0C,CAAC,MAAW,WAAsB;AAE3E,UAAI,OAAO,SAAS,YAAY;AAC/B,eAAO,CAAC,UAAe,SACtB,KAAK,mBAAmB,OAAO,CAAC,UAAe,KAAK,OAAO,GAAG,IAAI,CAAC;MACrE;AAEA,UAAI,SAAkB;AACtB,YAAM,SAAS,KAAK,QAAQ,MAAM,QAAQ,CAAC,GAAY,OAAgB;AACtE,kBAAU;AACV,yBAAiB;MAClB,CAAC;AACD,aAAO,CAAC,QAAQ,SAAU,cAAe;IAC1C;AA1FC,QAAI,QAAO,iCAAQ,gBAAe;AACjC,WAAK,cAAc,OAAQ,UAAU;AACtC,QAAI,QAAO,iCAAQ,0BAAyB;AAC3C,WAAK,wBAAwB,OAAQ,oBAAoB;EAC3D;EAwFA,YAAiC,MAAmB;AACnD,QAAI,CAAC,YAAY,IAAI;AAAG,UAAI,CAAC;AAC7B,QAAI,QAAQ,IAAI;AAAG,aAAO,QAAQ,IAAI;AACtC,UAAM,QAAQ,WAAW,IAAI;AAC7B,UAAM,QAAQ,YAAY,MAAM,MAAS;AACzC,UAAM,WAAW,EAAE,YAAY;AAC/B,eAAW,KAAK;AAChB,WAAO;EACR;EAEA,YACC,OACA,eACuC;AACvC,UAAM,QAAoB,SAAU,MAAc,WAAW;AAC7D,QAAI,CAAC,SAAS,CAAC,MAAM;AAAW,UAAI,CAAC;AACrC,UAAM,EAAC,QAAQ,MAAK,IAAI;AACxB,sBAAkB,OAAO,aAAa;AACtC,WAAO,cAAc,QAAW,KAAK;EACtC;;;;;;EAOA,cAAc,OAAgB;AAC7B,SAAK,cAAc;EACpB;;;;;;EAOA,wBAAwB,OAAmB;AAC1C,SAAK,wBAAwB;EAC9B;EAEA,aAAkC,MAAS,SAA8B;AAGxE,QAAI;AACJ,SAAK,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,YAAM,QAAQ,QAAQ,CAAC;AACvB,UAAI,MAAM,KAAK,WAAW,KAAK,MAAM,OAAO,WAAW;AACtD,eAAO,MAAM;AACb;MACD;IACD;AAGA,QAAI,IAAI,IAAI;AACX,gBAAU,QAAQ,MAAM,IAAI,CAAC;IAC9B;AAEA,UAAM,mBAAmB,UAAU,SAAS,EAAE;AAC9C,QAAI,QAAQ,IAAI,GAAG;AAElB,aAAO,iBAAiB,MAAM,OAAO;IACtC;AAEA,WAAO,KAAK;MAAQ;MAAM,CAAC,UAC1B,iBAAiB,OAAO,OAAO;IAChC;EACD;AACD;AAEO,SAAS,YACf,OACA,QACyB;AAEzB,QAAM,QAAiB,MAAM,KAAK,IAC/B,UAAU,QAAQ,EAAE,UAAU,OAAO,MAAM,IAC3C,MAAM,KAAK,IACX,UAAU,QAAQ,EAAE,UAAU,OAAO,MAAM,IAC3C,iBAAiB,OAAO,MAAM;AAEjC,QAAM,QAAQ,SAAS,OAAO,SAAS,gBAAgB;AACvD,QAAM,QAAQ,KAAK,KAAK;AACxB,SAAO;AACR;AC3MO,SAAS,QAAQ,OAAiB;AACxC,MAAI,CAAC,QAAQ,KAAK;AAAG,QAAI,IAAI,KAAK;AAClC,SAAO,YAAY,KAAK;AACzB;AAEA,SAAS,YAAY,OAAiB;AACrC,MAAI,CAAC,YAAY,KAAK,KAAK,SAAS,KAAK;AAAG,WAAO;AACnD,QAAM,QAAgC,MAAM,WAAW;AACvD,MAAI;AACJ,MAAI,OAAO;AACV,QAAI,CAAC,MAAM;AAAW,aAAO,MAAM;AAEnC,UAAM,aAAa;AACnB,WAAO,YAAY,OAAO,MAAM,OAAO,OAAO,qBAAqB;EACpE,OAAO;AACN,WAAO,YAAY,OAAO,IAAI;EAC/B;AAEA,OAAK,MAAM,CAAC,KAAK,eAAe;AAC/B,QAAI,MAAM,KAAK,YAAY,UAAU,CAAC;EACvC,CAAC;AACD,MAAI,OAAO;AACV,UAAM,aAAa;EACpB;AACA,SAAO;AACR;AGbA,IAAM,QAAQ,IAAIC,OAAM;AAqBjB,IAAM,UAAoB,MAAM;AAMhC,IAAM,qBAA0C,MAAM,mBAAmB;EAC/E;AACD;AAOO,IAAM,gBAAgB,MAAM,cAAc,KAAK,KAAK;AAOpD,IAAM,0BAA0B,MAAM,wBAAwB,KAAK,KAAK;AAOxE,IAAM,eAAe,MAAM,aAAa,KAAK,KAAK;AAMlD,IAAM,cAAc,MAAM,YAAY,KAAK,KAAK;AAUhD,IAAM,cAAc,MAAM,YAAY,KAAK,KAAK;;;ACzEhD,IAAM,2BAA2B,CACtC,YACA,uBACA,yBACG;AACH,MACE,sBAAsB,WAAW,KACjC,sBAAsB,CAAC,MAAM,sBAC7B;AACA,QAAI,sBAAsB;AAC1B,QAAI;AACF,YAAMC,eAAc,CAAC;AACrB,UAAI,WAAWA,YAAW,MAAMA;AAAa,8BAAsB;IACrE,QAAE;IAEF;AACA,QAAI,qBAAqB;AACvB,UAAI,QAA4B;AAChC,UAAI;AACF,cAAM,IAAI,MAAM;MAClB,SAAS,GAAP;AAEA;AAAC,SAAC,EAAE,MAAM,IAAI;MAChB;AACA,cAAQ;QACN;QAIA,EAAE,MAAM;MACV;IACF;EACF;AACF;ACpCO,IAAM,yBAAyB,CACpC,4BAIA,SAMA,sBACG;AACH,QAAM,EAAE,SAAS,eAAe,IAAI;AACpC,QAAM,EAAE,sBAAsB,yBAAyB,IACrD;AACF,QAAM,sBAAsB,QAAQ,OAAO,CAAC,IAAI,GAAG,cAAc;AAEjE,QAAM,+BACJ,oBAAoB,MAAM,MAAM,oBAAoB,MACpD,oBAAoB,MAAM,MAAM,wBAAwB;AAC1D,MAAI,CAAC,8BAA8B;AACjC,QAAI,QAA4B;AAChC,QAAI;AACF,YAAM,IAAI,MAAM;IAClB,SAAS,GAAP;AAEA;AAAC,OAAC,EAAE,MAAM,IAAI;IAChB;AACA,YAAQ;MACN;MAIA;QACE,WAAW;QACX,aAAa;QACb,cAAc;QACd;MACF;IACF;EACF;AACF;ACjDO,IAAM,sBAAqC;EAChD,qBAAqB;EACrB,uBAAuB;AACzB;ACDO,IAAM,YAA4B,OAAO,WAAW;AAWpD,SAAS,iBACd,MACA,eAAe,yCAAyC,OAAO,IAAA,IACjC;AAC9B,MAAI,OAAO,SAAS,YAAY;AAC9B,UAAM,IAAI,UAAU,YAAY;EAClC;AACF;AAUO,SAAS,eACd,QACA,eAAe,wCAAwC,OAAO,MAAA,IAChC;AAC9B,MAAI,OAAO,WAAW,UAAU;AAC9B,UAAM,IAAI,UAAU,YAAY;EAClC;AACF;AAUO,SAAS,yBACd,OACA,eAAe,8EACkB;AACjC,MACE,CAAC,MAAM,MAAM,CAAC,SAA+B,OAAO,SAAS,UAAU,GACvE;AACA,UAAM,YAAY,MACf;MAAI,CAAA,SACH,OAAO,SAAS,aACZ,YAAY,KAAK,QAAQ,SAAA,OACzB,OAAO;IACb,EACC,KAAK,IAAI;AACZ,UAAM,IAAI,UAAU,GAAG,YAAA,IAAgB,SAAA,GAAY;EACrD;AACF;AASO,IAAM,gBAAgB,CAAC,SAAkB;AAC9C,SAAO,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AAC3C;AASO,SAAS,gBAAgB,oBAA+B;AAC7D,QAAM,eAAe,MAAM,QAAQ,mBAAmB,CAAC,CAAC,IACpD,mBAAmB,CAAC,IACpB;AAEJ;IACE;IACA;EACF;AAEA,SAAO;AACT;AASO,SAAS,4BACd,cACA,mBACA;AACA,QAAM,uBAAuB,CAAC;AAC9B,QAAM,EAAE,OAAO,IAAI;AACnB,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAG/B,yBAAqB,KAAK,aAAa,CAAC,EAAE,MAAM,MAAM,iBAAiB,CAAC;EAC1E;AACA,SAAO;AACT;AASO,IAAM,gCAAgC,CAC3C,UACA,kBACG;AACH,QAAM,EAAE,uBAAuB,oBAAoB,IAAI;IACrD,GAAG;IACH,GAAG;EACL;AACA,SAAO;IACL,uBAAuB;MACrB,WACE,0BAA0B,YACzB,0BAA0B,UAAU;MACvC,KAAK;IACP;IACA,qBAAqB;MACnB,WACE,wBAAwB,YACvB,wBAAwB,UAAU;MACrC,KAAK;IACP;EACF;AACF;AG/IO,IAAM,oBAAoB,OAAO;AAIxC,IAAM,QAAQ,OAAO,eAAe,CAAC,CAAC;ACStC,SAAS,qBAAqB,QAA2B;AACvD,MAAI;AACJ,SAAO;IACL,IAAI,KAAc;AAChB,UAAI,SAAS,OAAO,MAAM,KAAK,GAAG,GAAG;AACnC,eAAO,MAAM;MACf;AAEA,aAAO;IACT;IAEA,IAAI,KAAc,OAAgB;AAChC,cAAQ,EAAE,KAAK,MAAM;IACvB;IAEA,aAAa;AACX,aAAO,QAAQ,CAAC,KAAK,IAAI,CAAC;IAC5B;IAEA,QAAQ;AACN,cAAQ;IACV;EACF;AACF;AAEA,SAAS,eAAe,SAAiB,QAA2B;AAClE,MAAI,UAAmB,CAAC;AAExB,WAAS,IAAI,KAAc;AACzB,UAAM,aAAa,QAAQ,UAAU,CAAA,UAAS,OAAO,KAAK,MAAM,GAAG,CAAC;AAGpE,QAAI,aAAa,IAAI;AACnB,YAAM,QAAQ,QAAQ,UAAU;AAGhC,UAAI,aAAa,GAAG;AAClB,gBAAQ,OAAO,YAAY,CAAC;AAC5B,gBAAQ,QAAQ,KAAK;MACvB;AAEA,aAAO,MAAM;IACf;AAGA,WAAO;EACT;AAEA,WAAS,IAAI,KAAc,OAAgB;AACzC,QAAI,IAAI,GAAG,MAAM,WAAW;AAE1B,cAAQ,QAAQ,EAAE,KAAK,MAAM,CAAC;AAC9B,UAAI,QAAQ,SAAS,SAAS;AAC5B,gBAAQ,IAAI;MACd;IACF;EACF;AAEA,WAAS,aAAa;AACpB,WAAO;EACT;AAEA,WAAS,QAAQ;AACf,cAAU,CAAC;EACb;AAEA,SAAO,EAAE,KAAK,KAAK,YAAY,MAAM;AACvC;AAUO,IAAM,yBAAqC,CAAC,GAAG,MAAM,MAAM;AAE3D,SAAS,yBAAyB,eAA2B;AAClE,SAAO,SAAS,2BACd,MACA,MACS;AACT,QAAI,SAAS,QAAQ,SAAS,QAAQ,KAAK,WAAW,KAAK,QAAQ;AACjE,aAAO;IACT;AAGA,UAAM,EAAE,OAAO,IAAI;AACnB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,UAAI,CAAC,cAAc,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG;AACpC,eAAO;MACT;IACF;AAEA,WAAO;EACT;AACF;AAgEO,SAAS,WACd,MACA,wBACA;AACA,QAAM,kBACJ,OAAO,2BAA2B,WAC9B,yBACA,EAAE,eAAe,uBAAuB;AAE9C,QAAM;IACJ,gBAAgB;IAChB,UAAU;IACV;EACF,IAAI;AAEJ,QAAM,aAAa,yBAAyB,aAAa;AAEzD,MAAI,eAAe;AAEnB,QAAM,QACJ,WAAW,IACP,qBAAqB,UAAU,IAC/B,eAAe,SAAS,UAAU;AAExC,WAAS,WAAW;AAClB,QAAI,QAAQ,MAAM,IAAI,SAAS;AAC/B,QAAI,UAAU,WAAW;AAGvB,cAAQ,KAAK,MAAM,MAAM,SAAS;AAClC;AAEA,UAAI,qBAAqB;AACvB,cAAM,UAAU,MAAM,WAAW;AACjC,cAAM,gBAAgB,QAAQ;UAAK,CAAA,UACjC,oBAAoB,MAAM,OAA2B,KAAK;QAC5D;AAEA,YAAI,eAAe;AACjB,kBAAQ,cAAc;AACtB,2BAAiB,KAAK;QACxB;MACF;AAEA,YAAM,IAAI,WAAW,KAAK;IAC5B;AACA,WAAO;EACT;AAEA,WAAS,aAAa,MAAM;AAC1B,UAAM,MAAM;AACZ,aAAS,kBAAkB;EAC7B;AAEA,WAAS,eAAe,MAAM;AAE9B,WAAS,oBAAoB,MAAM;AACjC,mBAAe;EACjB;AAEA,SAAO;AACT;AE9OA,IAAM,YAAN,MAAmB;EACjB,YAAoB,OAAU;AAAV,SAAA,QAAA;EAAW;EAC/B,QAAQ;AACN,WAAO,KAAK;EACd;AACF;AAEA,IAAM,MACJ,OAAO,YAAY,cACf,UACC;AAEP,IAAM,eAAe;AACrB,IAAM,aAAa;AA0CnB,SAAS,kBAAmC;AAC1C,SAAO;IACL,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;EACL;AACF;AAmGO,SAAS,eACd,MACA,UAAmD,CAAC,GACpD;AACA,MAAI,SAAS,gBAAgB;AAC7B,QAAM,EAAE,oBAAoB,IAAI;AAEhC,MAAI;AAEJ,MAAI,eAAe;AAEnB,WAAS,WAAW;;AAClB,QAAI,YAAY;AAChB,UAAM,EAAE,OAAO,IAAI;AACnB,aAAS,IAAI,GAAG,IAAI,QAAQ,IAAI,GAAG,KAAK;AACtC,YAAM,MAAM,UAAU,CAAC;AACvB,UACE,OAAO,QAAQ,cACd,OAAO,QAAQ,YAAY,QAAQ,MACpC;AAEA,YAAI,cAAc,UAAU;AAC5B,YAAI,gBAAgB,MAAM;AACxB,oBAAU,IAAI,cAAc,oBAAI,QAAQ;QAC1C;AACA,cAAM,aAAa,YAAY,IAAI,GAAG;AACtC,YAAI,eAAe,QAAW;AAC5B,sBAAY,gBAAgB;AAC5B,sBAAY,IAAI,KAAK,SAAS;QAChC,OAAO;AACL,sBAAY;QACd;MACF,OAAO;AAEL,YAAI,iBAAiB,UAAU;AAC/B,YAAI,mBAAmB,MAAM;AAC3B,oBAAU,IAAI,iBAAiB,oBAAI,IAAI;QACzC;AACA,cAAM,gBAAgB,eAAe,IAAI,GAAG;AAC5C,YAAI,kBAAkB,QAAW;AAC/B,sBAAY,gBAAgB;AAC5B,yBAAe,IAAI,KAAK,SAAS;QACnC,OAAO;AACL,sBAAY;QACd;MACF;IACF;AAEA,UAAM,iBAAiB;AAEvB,QAAI;AAEJ,QAAI,UAAU,MAAM,YAAY;AAC9B,eAAS,UAAU;IACrB,OAAO;AAEL,eAAS,KAAK,MAAM,MAAM,SAA6B;AACvD;AAEA,UAAI,qBAAqB;AACvB,cAAM,oBAAkB,8CAAY,UAAZ,wCAAyB;AAEjD,YACE,mBAAmB,QACnB,oBAAoB,iBAAqC,MAAM,GAC/D;AACA,mBAAS;AAET,2BAAiB,KAAK;QACxB;AAEA,cAAM,eACH,OAAO,WAAW,YAAY,WAAW,QAC1C,OAAO,WAAW;AAEpB,qBAAa,eAAe,IAAI,IAAI,MAAM,IAAI;MAChD;IACF;AAEA,mBAAe,IAAI;AAEnB,mBAAe,IAAI;AACnB,WAAO;EACT;AAEA,WAAS,aAAa,MAAM;AAC1B,aAAS,gBAAgB;AACzB,aAAS,kBAAkB;EAC7B;AAEA,WAAS,eAAe,MAAM;AAE9B,WAAS,oBAAoB,MAAM;AACjC,mBAAe;EACjB;AAEA,SAAO;AACT;ACaO,SAAS,sBAUd,qBACG,wBAMH;AAEA,QAAM,+BAGF,OAAO,qBAAqB,aAC5B;IACE,SAAS;IACT,gBAAgB;EAClB,IACA;AAEJ,QAAMC,kBAAiB,IAMlB,uBAUA;AACH,QAAI,iBAAiB;AACrB,QAAI,2BAA2B;AAC/B,QAAI;AAKJ,QAAI,wBAKA,CAAC;AAGL,QAAI,aAAa,mBAAmB,IAAI;AAUxC,QAAI,OAAO,eAAe,UAAU;AAClC,8BAAwB;AAExB,mBAAa,mBAAmB,IAAI;IACtC;AAEA;MACE;MACA,8EAA8E,OAAO,UAAA;IACvF;AAIA,UAAM,kBAAkB;MACtB,GAAG;MACH,GAAG;IACL;AAEA,UAAM;MACJ;MACA,iBAAiB,CAAC;MAClB,cAAc;MACd,qBAAqB,CAAC;MACtB,gBAAgB,CAAC;IACnB,IAAI;AAOJ,UAAM,sBAAsB,cAAc,cAAc;AACxD,UAAM,0BAA0B,cAAc,kBAAkB;AAChE,UAAM,eAAe,gBAAgB,kBAAkB;AAEvD,UAAM,qBAAqB,QAAQ,SAAS,uBAAuB;AACjE;AAGA,aAAQ,WAAgD;QACtD;QACA;MACF;IACF,GAAG,GAAG,mBAAmB;AAGzB,QAAI,WAAW;AAGf,UAAM,WAAW,YAAY,SAAS,sBAAsB;AAC1D;AAEA,YAAM,uBAAuB;QAC3B;QACA;MACF;AAIA,mBAAa,mBAAmB,MAAM,MAAM,oBAAoB;AAEhE,UAAI,MAAuC;AACzC,cAAM,EAAE,uBAAuB,oBAAoB,IACjD,8BAA8B,UAAU,aAAa;AACvD,YAAI,sBAAsB,WAAW;AACnC,gCAAsB;YACpB;YACA;YACA;UACF;QACF;AAEA,YAAI,oBAAoB,WAAW;AAEjC,gBAAM,2BAA2B;YAC/B;YACA;UACF;AAEA,8BAAoB;YAClB,EAAE,sBAAsB,yBAAyB;YACjD,EAAE,SAAS,gBAAgB,oBAAoB;YAC/C;UACF;QACF;AAEA,YAAI;AAAU,qBAAW;MAC3B;AAEA,aAAO;IACT,GAAG,GAAG,uBAAuB;AAO7B,WAAO,OAAO,OAAO,UAAU;MAC7B;MACA;MACA;MACA,0BAA0B,MAAM;MAChC,+BAA+B,MAAM;AACnC,mCAA2B;MAC7B;MACA,YAAY,MAAM;MAClB,gBAAgB,MAAM;MACtB,qBAAqB,MAAM;AACzB,yBAAiB;MACnB;MACA;MACA;IACF,CAAC;EAMH;AAEA,SAAO,OAAOA,iBAAgB;IAC5B,WAAW,MAAMA;EACnB,CAAC;AAED,SAAOA;AAIT;AAWO,IAAM,iBACK,sBAAsB,cAAc;AC5E/C,IAAM,2BACX,OAAO;EACL,CAKE,sBACA,kBAGI,mBAID;AACH;MACE;MACA,yHAC2D,OAAO,oBAAA;IACpE;AACA,UAAM,oBAAoB,OAAO,KAAK,oBAAoB;AAC1D,UAAM,eAAe,kBAAkB;MACrC,CAAA,QAAO,qBAAqB,GAAG;IACjC;AACA,UAAM,qBAAqB;MACzB;MACA,IAAI,yBAAgC;AAClC,eAAO,qBAAqB,OAAO,CAAC,aAAa,OAAO,UAAU;AAChE,sBAAY,kBAAkB,KAAK,CAAC,IAAI;AACxC,iBAAO;QACT,GAAG,CAAC,CAAC;MACP;IACF;AACA,WAAO;EACT;EACA,EAAE,WAAW,MAAM,yBAAyB;AAC9C;;;ACpcF,SAAS,sBAAsB,eAAe;AAC5C,QAAM,aAAa,CAAC,EAAE,UAAU,SAAS,MAAM,CAAC,SAAS,CAAC,WAAW;AACnE,QAAI,OAAO,WAAW,YAAY;AAChC,aAAO,OAAO,UAAU,UAAU,aAAa;AAAA,IACjD;AACA,WAAO,KAAK,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AACA,IAAI,QAAQ,sBAAsB;AAClC,IAAI,oBAAoB;;;AETjB,IAAM,iCAA+D,IAAI,SAAoB;AAClG,QAAMC,kBAAkB,sBAA8B,GAAG,IAAI;AAC7D,QAAMC,2BAA0B,OAAO,OAAO,IAAIC,UAAoB;AACpE,UAAM,WAAWF,gBAAe,GAAGE,KAAI;AACvC,UAAM,kBAAkB,CAAC,UAAmB,SAAoB,SAAS,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,OAAO,GAAG,IAAI;AACzH,WAAO,OAAO,iBAAiB,QAAQ;AACvC,WAAO;EACT,GAAG;IACD,WAAW,MAAMD;EACnB,CAAC;AACD,SAAOA;AACT;AASO,IAAM,0BACb,+BAA+B,cAAc;AE4LtC,IAAM,sBAA2C,OAAO,WAAW,eAAgB,OAAe,uCAAwC,OAAe,uCAAuC,WAAY;AACjN,MAAI,UAAU,WAAW,EAAG,QAAO;AACnC,MAAI,OAAO,UAAU,CAAC,MAAM,SAAU,QAAO;AAC7C,SAAO,QAAQ,MAAM,MAAM,SAA8B;AAC3D;AAKO,IAAM,mBAET,OAAO,WAAW,eAAgB,OAAe,+BAAgC,OAAe,+BAA+B,WAAY;AAC7I,SAAO,SAAUE,OAAM;AACrB,WAAOA;EACT;AACF;AG3IO,IAAM,mBAAmB,CAAK,MAA4C;AAC/E,SAAO,KAAK,OAAQ,EAA0B,UAAU;AAC1D;AD4GO,SAAS,aAAa,MAAc,eAA+B;AACxE,WAAS,iBAAiB,MAAa;AACrC,QAAI,eAAe;AACjB,UAAI,WAAW,cAAc,GAAG,IAAI;AACpC,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,QAAwC,uBAAwB,CAAC,IAAI,wCAAwC;MAC/H;AACA,aAAO;QACL;QACA,SAAS,SAAS;QAClB,GAAI,UAAU,YAAY;UACxB,MAAM,SAAS;QACjB;QACA,GAAI,WAAW,YAAY;UACzB,OAAO,SAAS;QAClB;MACF;IACF;AACA,WAAO;MACL;MACA,SAAS,KAAK,CAAC;IACjB;EACF;AACA,gBAAc,WAAW,MAAM,GAAG,IAAI;AACtC,gBAAc,OAAO;AACrB,gBAAc,QAAQ,CAAC,WAA6C,SAAS,MAAM,KAAK,OAAO,SAAS;AACxG,SAAO;AACT;AAKO,SAAS,gBAAgB,QAA0E;AACxG,SAAO,OAAO,WAAW,cAAc,UAAU;EAEjD,iBAAiB,MAAa;AAChC;AAKO,SAAS,MAAM,QAKpB;AACA,SAAO,SAAS,MAAM,KAAK,OAAO,KAAK,MAAM,EAAE,MAAM,UAAU;AACjE;AACA,SAAS,WAAW,KAAa;AAC/B,SAAO,CAAC,QAAQ,WAAW,SAAS,MAAM,EAAE,QAAQ,GAAG,IAAI;AAC7D;AE7OO,SAAS,WAAW,MAAgB;AACzC,QAAM,YAAY,OAAO,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC;AACjD,QAAM,aAAa,UAAU,UAAU,SAAS,CAAC,KAAK;AACtD,SAAO,yCAAyC,QAAQ,SAAS;kFACe,UAAU,+BAA+B,UAAU;AACrI;AACO,SAAS,uCAAuC,UAAmD,CAAC,GAAe;AACxH,MAAI,OAAuC;AACzC,WAAO,MAAM,CAAA,SAAQ,CAAA,WAAU,KAAK,MAAM;EAC5C;AACA,QAAM;IACJ,iBAAAC,mBAAkB;EACpB,IAAI;AACJ,SAAO,MAAM,CAAA,SAAQ,CAAA,WAAU;AAC7B,QAAIA,iBAAgB,MAAM,GAAG;AAC3B,cAAQ,KAAK,WAAW,OAAO,IAAI,CAAC;IACtC;AACA,WAAO,KAAK,MAAM;EACpB;AACF;AC7BO,SAAS,oBAAoB,UAAkB,QAAgB;AACpE,MAAI,UAAU;AACd,SAAO;IACL,YAAe,IAAgB;AAC7B,YAAM,UAAU,KAAK,IAAI;AACzB,UAAI;AACF,eAAO,GAAG;MACZ,UAAA;AACE,cAAM,WAAW,KAAK,IAAI;AAC1B,mBAAW,WAAW;MACxB;IACF;IACA,iBAAiB;AACf,UAAI,UAAU,UAAU;AACtB,gBAAQ,KAAK,GAAG,MAAM,SAAS,OAAO,mDAAmD,QAAQ;;4EAE7B;MACtE;IACF;EACF;AACF;AAIO,IAAM,QAAN,MAAM,eAAyD,MAAqB;EAGzF,eAAe,OAAc;AAC3B,UAAM,GAAG,KAAK;AACd,WAAO,eAAe,MAAM,OAAM,SAAS;EAC7C;EACA,YAAqB,OAAO,OAAO,IAAI;AACrC,WAAO;EACT;EAIS,UAAU,KAAY;AAC7B,WAAO,MAAM,OAAO,MAAM,MAAM,GAAG;EACrC;EAIA,WAAW,KAAY;AACrB,QAAI,IAAI,WAAW,KAAK,MAAM,QAAQ,IAAI,CAAC,CAAC,GAAG;AAC7C,aAAO,IAAI,OAAM,GAAG,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC;IACzC;AACA,WAAO,IAAI,OAAM,GAAG,IAAI,OAAO,IAAI,CAAC;EACtC;AACF;AACO,SAAS,gBAAmB,KAAQ;AACzC,SAAO,YAAY,GAAG,IAAI,QAAgB,KAAK,MAAM;EAAC,CAAC,IAAI;AAC7D;AASO,SAAS,oBAAyC,KAAgC,KAAQ,SAA2B;AAC1H,MAAI,IAAI,IAAI,GAAG,EAAG,QAAO,IAAI,IAAI,GAAG;AACpC,SAAO,IAAI,IAAI,KAAK,QAAQ,GAAG,CAAC,EAAE,IAAI,GAAG;AAC3C;ACtDO,SAAS,mBAAmB,OAAyB;AAC1D,SAAO,OAAO,UAAU,YAAY,SAAS,QAAQ,OAAO,SAAS,KAAK;AAC5E;AACO,SAAS,kBAAkB,aAA8B,aAAsC,KAAU;AAC9G,QAAM,oBAAoB,gBAAgB,aAAa,aAAa,GAAG;AACvE,SAAO;IACL,kBAAkB;AAChB,aAAO,gBAAgB,aAAa,aAAa,mBAAmB,GAAG;IACzE;EACF;AACF;AAKA,SAAS,gBAAgB,aAA8B,cAA2B,CAAC,GAAG,KAA0B,OAAe,IAAI,iBAA2C,oBAAI,IAAI,GAAG;AACvL,QAAM,UAAoC;IACxC,OAAO;EACT;AACA,MAAI,CAAC,YAAY,GAAG,KAAK,CAAC,eAAe,IAAI,GAAG,GAAG;AACjD,mBAAe,IAAI,GAAG;AACtB,YAAQ,WAAW,CAAC;AACpB,eAAW,OAAO,KAAK;AACrB,YAAM,YAAY,OAAO,OAAO,MAAM,MAAM;AAC5C,UAAI,YAAY,UAAU,YAAY,QAAQ,SAAS,MAAM,IAAI;AAC/D;MACF;AACA,cAAQ,SAAS,GAAG,IAAI,gBAAgB,aAAa,aAAa,IAAI,GAAG,GAAG,SAAS;IACvF;EACF;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,aAA8B,eAA4B,CAAC,GAAG,iBAAkC,KAAU,gBAAyB,OAAO,OAAe,IAGhL;AACA,QAAM,UAAU,kBAAkB,gBAAgB,QAAQ;AAC1D,QAAM,UAAU,YAAY;AAC5B,MAAI,iBAAiB,CAAC,WAAW,CAAC,OAAO,MAAM,GAAG,GAAG;AACnD,WAAO;MACL,YAAY;MACZ;IACF;EACF;AACA,MAAI,YAAY,OAAO,KAAK,YAAY,GAAG,GAAG;AAC5C,WAAO;MACL,YAAY;IACd;EACF;AAGA,QAAM,eAAwC,CAAC;AAC/C,WAAS,OAAO,gBAAgB,UAAU;AACxC,iBAAa,GAAG,IAAI;EACtB;AACA,WAAS,OAAO,KAAK;AACnB,iBAAa,GAAG,IAAI;EACtB;AACA,QAAM,kBAAkB,aAAa,SAAS;AAC9C,WAAS,OAAO,cAAc;AAC5B,UAAM,aAAa,OAAO,OAAO,MAAM,MAAM;AAC7C,QAAI,iBAAiB;AACnB,YAAM,aAAa,aAAa,KAAK,CAAA,YAAW;AAC9C,YAAI,mBAAmB,QAAQ;AAC7B,iBAAO,QAAQ,KAAK,UAAU;QAChC;AACA,eAAO,eAAe;MACxB,CAAC;AACD,UAAI,YAAY;AACd;MACF;IACF;AACA,UAAM,SAAS,gBAAgB,aAAa,cAAc,gBAAgB,SAAS,GAAG,GAAG,IAAI,GAAG,GAAG,SAAS,UAAU;AACtH,QAAI,OAAO,YAAY;AACrB,aAAO;IACT;EACF;AACA,SAAO;IACL,YAAY;EACd;AACF;AAmCO,SAAS,wCAAwC,UAAoD,CAAC,GAAe;AAC1H,MAAI,OAAuC;AACzC,WAAO,MAAM,CAAA,SAAQ,CAAA,WAAU,KAAK,MAAM;EAC5C,OAAO;AACL,QAASC,aAAT,SAAmB,KAAU,YAA6B,QAA0B,UAAmC;AACrH,aAAO,KAAK,UAAU,KAAKC,cAAa,YAAY,QAAQ,GAAG,MAAM;IACvE,GACSA,gBAAT,SAAsB,YAA6B,UAA2C;AAC5F,UAAI,QAAe,CAAC,GAClB,OAAc,CAAC;AACjB,UAAI,CAAC,SAAU,YAAW,SAAU,GAAW,OAAY;AACzD,YAAI,MAAM,CAAC,MAAM,MAAO,QAAO;AAC/B,eAAO,iBAAiB,KAAK,MAAM,GAAG,MAAM,QAAQ,KAAK,CAAC,EAAE,KAAK,GAAG,IAAI;MAC1E;AACA,aAAO,SAAqB,KAAa,OAAY;AACnD,YAAI,MAAM,SAAS,GAAG;AACpB,cAAI,UAAU,MAAM,QAAQ,IAAI;AAChC,WAAC,UAAU,MAAM,OAAO,UAAU,CAAC,IAAI,MAAM,KAAK,IAAI;AACtD,WAAC,UAAU,KAAK,OAAO,SAAS,UAAU,GAAG,IAAI,KAAK,KAAK,GAAG;AAC9D,cAAI,CAAC,MAAM,QAAQ,KAAK,EAAG,SAAQ,SAAU,KAAK,MAAM,KAAK,KAAK;QACpE,MAAO,OAAM,KAAK,KAAK;AACvB,eAAO,cAAc,OAAO,QAAQ,WAAW,KAAK,MAAM,KAAK,KAAK;MACtE;IACF;AAnBS,QAAA,YAAAD,YAGA,eAAAC;AAiBT,QAAI;MACF,cAAc;MACd;MACA,YAAY;IACd,IAAI;AACJ,UAAM,QAAQ,kBAAkB,KAAK,MAAM,aAAa,YAAY;AACpE,WAAO,CAAC;MACN;IACF,MAAM;AACJ,UAAI,QAAQ,SAAS;AACrB,UAAI,UAAU,MAAM,KAAK;AACzB,UAAI;AACJ,aAAO,CAAA,SAAQ,CAAA,WAAU;AACvB,cAAM,eAAe,oBAAoB,WAAW,mCAAmC;AACvF,qBAAa,YAAY,MAAM;AAC7B,kBAAQ,SAAS;AACjB,mBAAS,QAAQ,gBAAgB;AAEjC,oBAAU,MAAM,KAAK;AACrB,cAAI,OAAO,YAAY;AACrB,kBAAM,IAAI,MAAM,QAAwC,uBAAwB,EAAE,IAAI,kEAAkE,OAAO,QAAQ,EAAE,2GAA2G;UACtR;QACF,CAAC;AACD,cAAM,mBAAmB,KAAK,MAAM;AACpC,qBAAa,YAAY,MAAM;AAC7B,kBAAQ,SAAS;AACjB,mBAAS,QAAQ,gBAAgB;AAEjC,oBAAU,MAAM,KAAK;AACrB,cAAI,OAAO,YAAY;AACrB,kBAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,iEAAiE,OAAO,QAAQ,EAAE,uDAAuDD,WAAU,MAAM,CAAC,sEAAsE;UACzT;QACF,CAAC;AACD,qBAAa,eAAe;AAC5B,eAAO;MACT;IACF;EACF;AACF;AC/KO,SAAS,QAAQ,KAAU;AAChC,QAAM,OAAO,OAAO;AACpB,SAAO,OAAO,QAAQ,SAAS,YAAY,SAAS,aAAa,SAAS,YAAY,MAAM,QAAQ,GAAG,KAAK,cAAc,GAAG;AAC/H;AAUO,SAAS,yBAAyB,OAAgB,OAAe,IAAI,iBAA8C,SAAS,YAAkD,eAA4B,CAAC,GAAG,OAAuD;AAC1Q,MAAI;AACJ,MAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,WAAO;MACL,SAAS,QAAQ;MACjB;IACF;EACF;AACA,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAC/C,WAAO;EACT;AACA,MAAI,+BAAO,IAAI,OAAQ,QAAO;AAC9B,QAAM,UAAU,cAAc,OAAO,WAAW,KAAK,IAAI,OAAO,QAAQ,KAAK;AAC7E,QAAM,kBAAkB,aAAa,SAAS;AAC9C,aAAW,CAAC,KAAK,WAAW,KAAK,SAAS;AACxC,UAAM,aAAa,OAAO,OAAO,MAAM,MAAM;AAC7C,QAAI,iBAAiB;AACnB,YAAM,aAAa,aAAa,KAAK,CAAA,YAAW;AAC9C,YAAI,mBAAmB,QAAQ;AAC7B,iBAAO,QAAQ,KAAK,UAAU;QAChC;AACA,eAAO,eAAe;MACxB,CAAC;AACD,UAAI,YAAY;AACd;MACF;IACF;AACA,QAAI,CAAC,eAAe,WAAW,GAAG;AAChC,aAAO;QACL,SAAS;QACT,OAAO;MACT;IACF;AACA,QAAI,OAAO,gBAAgB,UAAU;AACnC,gCAA0B,yBAAyB,aAAa,YAAY,gBAAgB,YAAY,cAAc,KAAK;AAC3H,UAAI,yBAAyB;AAC3B,eAAO;MACT;IACF;EACF;AACA,MAAI,SAAS,eAAe,KAAK,EAAG,OAAM,IAAI,KAAK;AACnD,SAAO;AACT;AACO,SAAS,eAAe,OAAe;AAC5C,MAAI,CAAC,OAAO,SAAS,KAAK,EAAG,QAAO;AACpC,aAAW,eAAe,OAAO,OAAO,KAAK,GAAG;AAC9C,QAAI,OAAO,gBAAgB,YAAY,gBAAgB,KAAM;AAC7D,QAAI,CAAC,eAAe,WAAW,EAAG,QAAO;EAC3C;AACA,SAAO;AACT;AAwEO,SAAS,2CAA2C,UAAuD,CAAC,GAAe;AAChI,MAAI,OAAuC;AACzC,WAAO,MAAM,CAAA,SAAQ,CAAA,WAAU,KAAK,MAAM;EAC5C,OAAO;AACL,UAAM;MACJ,iBAAiB;MACjB;MACA,iBAAiB,CAAC;MAClB,qBAAqB,CAAC,YAAY,oBAAoB;MACtD,eAAe,CAAC;MAChB,YAAY;MACZ,cAAc;MACd,gBAAgB;MAChB,eAAe;IACjB,IAAI;AACJ,UAAM,QAAqC,CAAC,gBAAgB,UAAU,oBAAI,QAAQ,IAAI;AACtF,WAAO,CAAA,aAAY,CAAA,SAAQ,CAAA,WAAU;AACnC,UAAI,CAACE,SAAS,MAAM,GAAG;AACrB,eAAO,KAAK,MAAM;MACpB;AACA,YAAM,SAAS,KAAK,MAAM;AAC1B,YAAM,eAAe,oBAAoB,WAAW,sCAAsC;AAC1F,UAAI,CAAC,iBAAiB,EAAE,eAAe,UAAU,eAAe,QAAQ,OAAO,IAAW,MAAM,KAAK;AACnG,qBAAa,YAAY,MAAM;AAC7B,gBAAM,kCAAkC,yBAAyB,QAAQ,IAAI,gBAAgB,YAAY,oBAAoB,KAAK;AAClI,cAAI,iCAAiC;AACnC,kBAAM;cACJ;cACA;YACF,IAAI;AACJ,oBAAQ,MAAM,sEAAsE,OAAO,cAAc,OAAO,4DAA4D,QAAQ,yIAAyI,6HAA6H;UAC5b;QACF,CAAC;MACH;AACA,UAAI,CAAC,aAAa;AAChB,qBAAa,YAAY,MAAM;AAC7B,gBAAM,QAAQ,SAAS,SAAS;AAChC,gBAAM,iCAAiC,yBAAyB,OAAO,IAAI,gBAAgB,YAAY,cAAc,KAAK;AAC1H,cAAI,gCAAgC;AAClC,kBAAM;cACJ;cACA;YACF,IAAI;AACJ,oBAAQ,MAAM,sEAAsE,OAAO,cAAc,OAAO;2DACjE,OAAO,IAAI;+HACyD;UACrH;QACF,CAAC;AACD,qBAAa,eAAe;MAC9B;AACA,aAAO;IACT;EACF;AACF;AN3LA,SAAS,UAAU,GAAsB;AACvC,SAAO,OAAO,MAAM;AACtB;AAuBO,IAAM,4BAA4B,MAAyC,SAAS,qBAAqB,SAAS;AACvH,QAAM;IACJ,OAAAC,SAAQ;IACR,iBAAiB;IACjB,oBAAoB;IACpB,qBAAqB;EACvB,IAAI,WAAW,CAAC;AAChB,MAAI,kBAAkB,IAAI,MAAoB;AAC9C,MAAIA,QAAO;AACT,QAAI,UAAUA,MAAK,GAAG;AACpB,sBAAgB,KAAK,KAAe;IACtC,OAAO;AACL,sBAAgB,KAAK,kBAAkBA,OAAM,aAAa,CAAC;IAC7D;EACF;AACA,MAAI,MAAuC;AACzC,QAAI,gBAAgB;AAElB,UAAI,mBAA6D,CAAC;AAClE,UAAI,CAAC,UAAU,cAAc,GAAG;AAC9B,2BAAmB;MACrB;AACA,sBAAgB,QAAQ,wCAAwC,gBAAgB,CAAC;IAEnF;AACA,QAAI,mBAAmB;AACrB,UAAI,sBAAmE,CAAC;AACxE,UAAI,CAAC,UAAU,iBAAiB,GAAG;AACjC,8BAAsB;MACxB;AACA,sBAAgB,KAAK,2CAA2C,mBAAmB,CAAC;IACtF;AACA,QAAI,oBAAoB;AACtB,UAAI,uBAAgE,CAAC;AACrE,UAAI,CAAC,UAAU,kBAAkB,GAAG;AAClC,+BAAuB;MACzB;AACA,sBAAgB,QAAQ,uCAAuC,oBAAoB,CAAC;IACtF;EACF;AACA,SAAO;AACT;AO/EO,IAAM,mBAAmB;AACzB,IAAM,qBAAqB,MAAU,CAAC,aAGvC;EACJ;EACA,MAAM;IACJ,CAAC,gBAAgB,GAAG;EACtB;AACF;AACA,IAAM,uBAAuB,CAAC,YAAoB;AAChD,SAAO,CAAC,WAAuB;AAC7B,eAAW,QAAQ,OAAO;EAC5B;AACF;AAmCO,IAAM,oBAAoB,CAAC,UAA4B;EAC5D,MAAM;AACR,MAAqB,CAAA,SAAQ,IAAI,SAAS;AACxC,QAAM,QAAQ,KAAK,GAAG,IAAI;AAC1B,MAAI,YAAY;AAChB,MAAI,0BAA0B;AAC9B,MAAI,qBAAqB;AACzB,QAAM,YAAY,oBAAI,IAAgB;AACtC,QAAM,gBAAgB,QAAQ,SAAS,SAAS,iBAAiB,QAAQ,SAAS;;IAElF,OAAO,WAAW,eAAe,OAAO,wBAAwB,OAAO,wBAAwB,qBAAqB,EAAE;MAAI,QAAQ,SAAS,aAAa,QAAQ,oBAAoB,qBAAqB,QAAQ,OAAO;AACxN,QAAM,kBAAkB,MAAM;AAG5B,yBAAqB;AACrB,QAAI,yBAAyB;AAC3B,gCAA0B;AAC1B,gBAAU,QAAQ,CAAA,MAAK,EAAE,CAAC;IAC5B;EACF;AACA,SAAO,OAAO,OAAO,CAAC,GAAG,OAAO;;;IAG9B,UAAUC,WAAsB;AAK9B,YAAM,kBAAmC,MAAM,aAAaA,UAAS;AACrE,YAAM,cAAc,MAAM,UAAU,eAAe;AACnD,gBAAU,IAAIA,SAAQ;AACtB,aAAO,MAAM;AACX,oBAAY;AACZ,kBAAU,OAAOA,SAAQ;MAC3B;IACF;;;IAGA,SAAS,QAAa;;AACpB,UAAI;AAGF,oBAAY,GAAC,sCAAQ,SAAR,mBAAe;AAG5B,kCAA0B,CAAC;AAC3B,YAAI,yBAAyB;AAI3B,cAAI,CAAC,oBAAoB;AACvB,iCAAqB;AACrB,0BAAc,eAAe;UAC/B;QACF;AAOA,eAAO,MAAM,SAAS,MAAM;MAC9B,UAAA;AAEE,oBAAY;MACd;IACF;EACF,CAAC;AACH;AC1GO,IAAM,2BAA2B,CAA8B,uBAEvC,SAAS,oBAAoB,SAAS;AACnE,QAAM;IACJ,YAAY;EACd,IAAI,WAAW,CAAC;AAChB,MAAI,gBAAgB,IAAI,MAAuB,kBAAkB;AACjE,MAAI,WAAW;AACb,kBAAc,KAAK,kBAAkB,OAAO,cAAc,WAAW,YAAY,MAAS,CAAC;EAC7F;AACA,SAAO;AACT;AV8DO,SAAS,eAEY,SAAuE;AACjG,QAAM,uBAAuB,0BAA6B;AAC1D,QAAM;IACJ,UAAU;IACV;IACA,WAAW;IACX,2BAA2B;IAC3B,iBAAiB;IACjB,YAAY;EACd,IAAI,WAAW,CAAC;AAChB,MAAI;AACJ,MAAI,OAAO,YAAY,YAAY;AACjC,kBAAc;EAChB,WAAWC,cAAc,OAAO,GAAG;AACjC,kBAAc,gBAAgB,OAAO;EACvC,OAAO;AACL,UAAM,IAAI,MAAM,QAAwC,uBAAwB,CAAC,IAAI,0HAA0H;EACjN;AACA,MAA6C,cAAc,OAAO,eAAe,YAAY;AAC3F,UAAM,IAAI,MAAM,QAAwC,uBAAyB,CAAC,IAAI,uCAAuC;EAC/H;AACA,MAAI;AACJ,MAAI,OAAO,eAAe,YAAY;AACpC,sBAAkB,WAAW,oBAAoB;AACjD,QAA6C,CAAC,MAAM,QAAQ,eAAe,GAAG;AAC5E,YAAM,IAAI,MAAM,QAAwC,uBAAyB,CAAC,IAAI,mFAAmF;IAC3K;EACF,OAAO;AACL,sBAAkB,qBAAqB;EACzC;AACA,MAA6C,gBAAgB,KAAK,CAAC,SAAc,OAAO,SAAS,UAAU,GAAG;AAC5G,UAAM,IAAI,MAAM,QAAwC,uBAAyB,CAAC,IAAI,+DAA+D;EACvJ;AACA,MAA6C,0BAA0B;AACrE,QAAI,uBAAuB,oBAAI,IAAwB;AACvD,oBAAgB,QAAQ,CAAAC,gBAAc;AACpC,UAAI,qBAAqB,IAAIA,WAAU,GAAG;AACxC,cAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,mHAAmH;MAC5M;AACA,2BAAqB,IAAIA,WAAU;IACrC,CAAC;EACH;AACA,MAAI,eAAeC;AACnB,MAAI,UAAU;AACZ,mBAAe,oBAAoB;;MAEjC,OAAO;MACP,GAAI,OAAO,aAAa,YAAY;IACtC,CAAC;EACH;AACA,QAAM,qBAAqB,gBAAgB,GAAG,eAAe;AAC7D,QAAM,sBAAsB,yBAA4B,kBAAkB;AAC1E,MAA6C,aAAa,OAAO,cAAc,YAAY;AACzF,UAAM,IAAI,MAAM,QAAwC,uBAAyB,CAAC,IAAI,sCAAsC;EAC9H;AACA,MAAI,iBAAiB,OAAO,cAAc,aAAa,UAAU,mBAAmB,IAAI,oBAAoB;AAC5G,MAA6C,CAAC,MAAM,QAAQ,cAAc,GAAG;AAC3E,UAAM,IAAI,MAAM,QAAwC,uBAAyB,CAAC,IAAI,2CAA2C;EACnI;AACA,MAA6C,eAAe,KAAK,CAAC,SAAc,OAAO,SAAS,UAAU,GAAG;AAC3G,UAAM,IAAI,MAAM,QAAwC,uBAAyB,CAAC,IAAI,6DAA6D;EACrJ;AACA,MAA6C,gBAAgB,UAAU,CAAC,eAAe,SAAS,kBAAkB,GAAG;AACnH,YAAQ,MAAM,kIAAkI;EAClJ;AACA,QAAM,mBAAuC,aAAa,GAAG,cAAc;AAC3E,SAAO,YAAY,aAAa,gBAAqB,gBAAgB;AACvE;AYhDO,SAAS,8BAAiC,iBAAmK;AAClN,QAAM,aAAmC,CAAC;AAC1C,QAAM,iBAAwD,CAAC;AAC/D,MAAI;AACJ,QAAM,UAAU;IACd,QAAQ,qBAAuD,SAAyB;AACtF,UAAI,MAAuC;AAMzC,YAAI,eAAe,SAAS,GAAG;AAC7B,gBAAM,IAAI,MAAM,QAAwC,uBAAwB,EAAE,IAAI,6EAA6E;QACrK;AACA,YAAI,oBAAoB;AACtB,gBAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,iFAAiF;QAC1K;MACF;AACA,YAAM,OAAO,OAAO,wBAAwB,WAAW,sBAAsB,oBAAoB;AACjG,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,8DAA8D;MACvJ;AACA,UAAI,QAAQ,YAAY;AACtB,cAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,oFAAuF,IAAI,GAAG;MACvL;AACA,iBAAW,IAAI,IAAI;AACnB,aAAO;IACT;IACA,WAAc,SAAuB,SAA4D;AAC/F,UAAI,MAAuC;AACzC,YAAI,oBAAoB;AACtB,gBAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,oFAAoF;QAC7K;MACF;AACA,qBAAe,KAAK;QAClB;QACA;MACF,CAAC;AACD,aAAO;IACT;IACA,eAAe,SAAiC;AAC9C,UAAI,MAAuC;AACzC,YAAI,oBAAoB;AACtB,gBAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,kDAAkD;QAC3I;MACF;AACA,2BAAqB;AACrB,aAAO;IACT;EACF;AACA,kBAAgB,OAAO;AACvB,SAAO,CAAC,YAAY,gBAAgB,kBAAkB;AACxD;ADzGA,SAAS,gBAAmB,GAA0B;AACpD,SAAO,OAAO,MAAM;AACtB;AAqEO,SAAS,cAA0C,cAA6B,sBAAiG;AACtL,MAAI,MAAuC;AACzC,QAAI,OAAO,yBAAyB,UAAU;AAC5C,YAAM,IAAI,MAAM,QAAwC,uBAAwB,CAAC,IAAI,8JAA8J;IACrP;EACF;AACA,MAAI,CAAC,YAAY,qBAAqB,uBAAuB,IAAI,8BAA8B,oBAAoB;AAGnH,MAAI;AACJ,MAAI,gBAAgB,YAAY,GAAG;AACjC,sBAAkB,MAAM,gBAAgB,aAAa,CAAC;EACxD,OAAO;AACL,UAAM,qBAAqB,gBAAgB,YAAY;AACvD,sBAAkB,MAAM;EAC1B;AACA,WAAS,QAAQ,QAAQ,gBAAgB,GAAG,QAAgB;AAC1D,QAAI,eAAe,CAAC,WAAW,OAAO,IAAI,GAAG,GAAG,oBAAoB,OAAO,CAAC;MAC1E;IACF,MAAM,QAAQ,MAAM,CAAC,EAAE,IAAI,CAAC;MAC1B,SAAAC;IACF,MAAMA,QAAO,CAAC;AACd,QAAI,aAAa,OAAO,CAAA,OAAM,CAAC,CAAC,EAAE,EAAE,WAAW,GAAG;AAChD,qBAAe,CAAC,uBAAuB;IACzC;AACA,WAAO,aAAa,OAAO,CAAC,eAAe,gBAAmB;AAC5D,UAAI,aAAa;AACf,YAAIC,QAAQ,aAAa,GAAG;AAI1B,gBAAM,QAAQ;AACd,gBAAM,SAAS,YAAY,OAAO,MAAM;AACxC,cAAI,WAAW,QAAW;AACxB,mBAAO;UACT;AACA,iBAAO;QACT,WAAW,CAACC,YAAY,aAAa,GAAG;AAGtC,gBAAM,SAAS,YAAY,eAAsB,MAAM;AACvD,cAAI,WAAW,QAAW;AACxB,gBAAI,kBAAkB,MAAM;AAC1B,qBAAO;YACT;AACA,kBAAM,MAAM,mEAAmE;UACjF;AACA,iBAAO;QACT,OAAO;AAIL,iBAAOC,QAAgB,eAAe,CAAC,UAAoB;AACzD,mBAAO,YAAY,OAAO,MAAM;UAClC,CAAC;QACH;MACF;AACA,aAAO;IACT,GAAG,KAAK;EACV;AACA,UAAQ,kBAAkB;AAC1B,SAAO;AACT;AElLA,IAAM,UAAU,CAAC,SAAuB,WAAgB;AACtD,MAAI,iBAAiB,OAAO,GAAG;AAC7B,WAAO,QAAQ,MAAM,MAAM;EAC7B,OAAO;AACL,WAAO,QAAQ,MAAM;EACvB;AACF;AAWO,SAAS,WAA4C,UAAoB;AAC9E,SAAO,CAAC,WAAyD;AAC/D,WAAO,SAAS,KAAK,CAAA,YAAW,QAAQ,SAAS,MAAM,CAAC;EAC1D;AACF;AAWO,SAAS,WAA4C,UAAoB;AAC9E,SAAO,CAAC,WAAyD;AAC/D,WAAO,SAAS,MAAM,CAAA,YAAW,QAAQ,SAAS,MAAM,CAAC;EAC3D;AACF;AAQO,SAAS,2BAA2B,QAAa,aAAgC;AACtF,MAAI,CAAC,UAAU,CAAC,OAAO,KAAM,QAAO;AACpC,QAAM,oBAAoB,OAAO,OAAO,KAAK,cAAc;AAC3D,QAAM,wBAAwB,YAAY,QAAQ,OAAO,KAAK,aAAa,IAAI;AAC/E,SAAO,qBAAqB;AAC9B;AACA,SAAS,kBAAkB,GAAkD;AAC3E,SAAO,OAAO,EAAE,CAAC,MAAM,cAAc,aAAa,EAAE,CAAC,KAAK,eAAe,EAAE,CAAC,KAAK,cAAc,EAAE,CAAC;AACpG;AA2BO,SAAS,aAAsE,aAAkC;AACtH,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO,CAAC,WAAgB,2BAA2B,QAAQ,CAAC,SAAS,CAAC;EACxE;AACA,MAAI,CAAC,kBAAkB,WAAW,GAAG;AACnC,WAAO,UAAU,EAAE,YAAY,CAAC,CAAC;EACnC;AACA,SAAO,QAAQ,GAAG,YAAY,IAAI,CAAA,eAAc,WAAW,OAAO,CAAC;AACrE;AA2BO,SAAS,cAAuE,aAAkC;AACvH,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO,CAAC,WAAgB,2BAA2B,QAAQ,CAAC,UAAU,CAAC;EACzE;AACA,MAAI,CAAC,kBAAkB,WAAW,GAAG;AACnC,WAAO,WAAW,EAAE,YAAY,CAAC,CAAC;EACpC;AACA,SAAO,QAAQ,GAAG,YAAY,IAAI,CAAA,eAAc,WAAW,QAAQ,CAAC;AACtE;AA+BO,SAAS,uBAAgF,aAAkC;AAChI,QAAM,UAAU,CAAC,WAA+B;AAC9C,WAAO,UAAU,OAAO,QAAQ,OAAO,KAAK;EAC9C;AACA,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO,QAAQ,WAAW,GAAG,WAAW,GAAG,OAAO;EACpD;AACA,MAAI,CAAC,kBAAkB,WAAW,GAAG;AACnC,WAAO,oBAAoB,EAAE,YAAY,CAAC,CAAC;EAC7C;AACA,SAAO,QAAQ,WAAW,GAAG,WAAW,GAAG,OAAO;AACpD;AA2BO,SAAS,eAAwE,aAAkC;AACxH,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO,CAAC,WAAgB,2BAA2B,QAAQ,CAAC,WAAW,CAAC;EAC1E;AACA,MAAI,CAAC,kBAAkB,WAAW,GAAG;AACnC,WAAO,YAAY,EAAE,YAAY,CAAC,CAAC;EACrC;AACA,SAAO,QAAQ,GAAG,YAAY,IAAI,CAAA,eAAc,WAAW,SAAS,CAAC;AACvE;AAoCO,SAAS,sBAA+E,aAAkC;AAC/H,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO,CAAC,WAAgB,2BAA2B,QAAQ,CAAC,WAAW,aAAa,UAAU,CAAC;EACjG;AACA,MAAI,CAAC,kBAAkB,WAAW,GAAG;AACnC,WAAO,mBAAmB,EAAE,YAAY,CAAC,CAAC;EAC5C;AACA,SAAO,QAAQ,GAAG,YAAY,QAAQ,CAAA,eAAc,CAAC,WAAW,SAAS,WAAW,UAAU,WAAW,SAAS,CAAC,CAAC;AACtH;ACzPA,IAAI,cAAc;AAMX,IAAI,SAAS,CAAC,OAAO,OAAO;AACjC,MAAI,KAAK;AAET,MAAI,IAAI;AACR,SAAO,KAAK;AAEV,UAAM,YAAY,KAAK,OAAO,IAAI,KAAK,CAAC;EAC1C;AACA,SAAO;AACT;ACSA,IAAM,mBAAiD,CAAC,QAAQ,WAAW,SAAS,MAAM;AAC1F,IAAM,kBAAN,MAA6C;EAM3C,YAA4B,SAAkC,MAAoB;AADjE;;;;;AACW,SAAA,UAAA;AAAkC,SAAA,OAAA;EAAqB;AACrF;AACA,IAAM,kBAAN,MAA8C;EAM5C,YAA4B,SAAkC,MAAqB;AADlE;;;;;AACW,SAAA,UAAA;AAAkC,SAAA,OAAA;EAAsB;AACtF;AAQO,IAAM,qBAAqB,CAAC,UAAgC;AACjE,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAC/C,UAAM,cAA+B,CAAC;AACtC,eAAW,YAAY,kBAAkB;AACvC,UAAI,OAAO,MAAM,QAAQ,MAAM,UAAU;AACvC,oBAAY,QAAQ,IAAI,MAAM,QAAQ;MACxC;IACF;AACA,WAAO;EACT;AACA,SAAO;IACL,SAAS,OAAO,KAAK;EACvB;AACF;AA4MA,IAAM,uBAAuB;AACtB,IAAM,oBAAmC,MAAM;AACpD,WAASC,kBAA8E,YAAoB,gBAA8E,SAAuG;AAK9R,UAAM,YAAkF,aAAa,aAAa,cAAc,CAAC,SAAmB,WAAmB,KAAe,UAA0B;MAC9M;MACA,MAAM;QACJ,GAAI,QAAe,CAAC;QACpB;QACA;QACA,eAAe;MACjB;IACF,EAAE;AACF,UAAM,UAAoE,aAAa,aAAa,YAAY,CAAC,WAAmB,KAAe,UAAwB;MACzK,SAAS;MACT,MAAM;QACJ,GAAI,QAAe,CAAC;QACpB;QACA;QACA,eAAe;MACjB;IACF,EAAE;AACF,UAAM,WAAsE,aAAa,aAAa,aAAa,CAAC,OAAqB,WAAmB,KAAe,SAAyB,UAAyB;MAC3N;MACA,QAAQ,WAAW,QAAQ,kBAAkB,oBAAoB,SAAS,UAAU;MACpF,MAAM;QACJ,GAAI,QAAe,CAAC;QACpB;QACA;QACA,mBAAmB,CAAC,CAAC;QACrB,eAAe;QACf,UAAS,+BAAO,UAAS;QACzB,YAAW,+BAAO,UAAS;MAC7B;IACF,EAAE;AACF,aAAS,cAAc,KAAe;MACpC;IACF,IAA8B,CAAC,GAAmE;AAChG,aAAO,CAAC,UAAU,UAAU,UAAU;AACpC,cAAM,aAAY,mCAAS,eAAc,QAAQ,YAAY,GAAG,IAAI,OAAO;AAC3E,cAAM,kBAAkB,IAAI,gBAAgB;AAC5C,YAAI;AACJ,YAAI;AACJ,iBAAS,MAAM,QAAiB;AAC9B,wBAAc;AACd,0BAAgB,MAAM;QACxB;AACA,YAAI,QAAQ;AACV,cAAI,OAAO,SAAS;AAClB,kBAAM,oBAAoB;UAC5B,OAAO;AACL,mBAAO,iBAAiB,SAAS,MAAM,MAAM,oBAAoB,GAAG;cAClE,MAAM;YACR,CAAC;UACH;QACF;AACA,cAAM,UAAU,iBAAkB;;AAChC,cAAI;AACJ,cAAI;AACF,gBAAI,mBAAkB,wCAAS,cAAT,iCAAqB,KAAK;cAC9C;cACA;YACF;AACA,gBAAI,WAAW,eAAe,GAAG;AAC/B,gCAAkB,MAAM;YAC1B;AACA,gBAAI,oBAAoB,SAAS,gBAAgB,OAAO,SAAS;AAE/D,oBAAM;gBACJ,MAAM;gBACN,SAAS;cACX;YACF;AACA,kBAAM,iBAAiB,IAAI,QAAe,CAAC,GAAG,WAAW;AACvD,6BAAe,MAAM;AACnB,uBAAO;kBACL,MAAM;kBACN,SAAS,eAAe;gBAC1B,CAAC;cACH;AACA,8BAAgB,OAAO,iBAAiB,SAAS,YAAY;YAC/D,CAAC;AACD,qBAAS,QAAQ,WAAW,MAAK,wCAAS,mBAAT,iCAA0B;cACzD;cACA;YACF,GAAG;cACD;cACA;YACF,EAAE,CAAQ;AACV,0BAAc,MAAM,QAAQ,KAAK,CAAC,gBAAgB,QAAQ,QAAQ,eAAe,KAAK;cACpF;cACA;cACA;cACA;cACA,QAAQ,gBAAgB;cACxB;cACA,iBAAkB,CAAC,OAAsB,SAAwB;AAC/D,uBAAO,IAAI,gBAAgB,OAAO,IAAI;cACxC;cACA,kBAAmB,CAAC,OAAgB,SAAyB;AAC3D,uBAAO,IAAI,gBAAgB,OAAO,IAAI;cACxC;YACF,CAAC,CAAC,EAAE,KAAK,CAAA,WAAU;AACjB,kBAAI,kBAAkB,iBAAiB;AACrC,sBAAM;cACR;AACA,kBAAI,kBAAkB,iBAAiB;AACrC,uBAAO,UAAU,OAAO,SAAS,WAAW,KAAK,OAAO,IAAI;cAC9D;AACA,qBAAO,UAAU,QAAe,WAAW,GAAG;YAChD,CAAC,CAAC,CAAC;UACL,SAAS,KAAK;AACZ,0BAAc,eAAe,kBAAkB,SAAS,MAAM,WAAW,KAAK,IAAI,SAAS,IAAI,IAAI,IAAI,SAAS,KAAY,WAAW,GAAG;UAC5I,UAAA;AACE,gBAAI,cAAc;AAChB,8BAAgB,OAAO,oBAAoB,SAAS,YAAY;YAClE;UACF;AAMA,gBAAM,eAAe,WAAW,CAAC,QAAQ,8BAA8B,SAAS,MAAM,WAAW,KAAM,YAAoB,KAAK;AAChI,cAAI,CAAC,cAAc;AACjB,qBAAS,WAAkB;UAC7B;AACA,iBAAO;QACT,EAAE;AACF,eAAO,OAAO,OAAO,SAA6B;UAChD;UACA;UACA;UACA,SAAS;AACP,mBAAO,QAAQ,KAAU,YAAY;UACvC;QACF,CAAC;MACH;IACF;AACA,WAAO,OAAO,OAAO,eAA8E;MACjG;MACA;MACA;MACA,SAAS,QAAQ,UAAU,SAAS;MACpC;IACF,CAAC;EACH;AACAA,oBAAiB,YAAY,MAAMA;AACnC,SAAOA;AACT,GAAG;AAaI,SAAS,aAA0C,QAAsC;AAC9F,MAAI,OAAO,QAAQ,OAAO,KAAK,mBAAmB;AAChD,UAAM,OAAO;EACf;AACA,MAAI,OAAO,OAAO;AAChB,UAAM,OAAO;EACf;AACA,SAAO,OAAO;AAChB;AAEA,SAAS,WAAW,OAAuC;AACzD,SAAO,UAAU,QAAQ,OAAO,UAAU,YAAY,OAAO,MAAM,SAAS;AAC9E;AC/aA,IAAM,mBAAkC,OAAO,IAAI,4BAA4B;AAExE,IAAM,oBAET;EACF,CAAC,gBAAgB,GAAG;AACtB;AAwLO,IAAK,eAAL,CAAKC,iBAAL;AACLA,eAAA,SAAA,IAAU;AACVA,eAAA,oBAAA,IAAqB;AACrBA,eAAA,YAAA,IAAa;AAHH,SAAAA;AAAA,GAAA,eAAA,CAAA,CAAA;AAoIZ,SAAS,QAAQ,OAAe,WAA2B;AACzD,SAAO,GAAG,KAAK,IAAI,SAAS;AAC9B;AAMO,SAAS,iBAAiB;EAC/B;AACF,IAA4B,CAAC,GAAG;;AAC9B,QAAM,OAAM,0CAAU,eAAV,mBAAuB;AACnC,SAAO,SAASC,aAAmK,SAA0I;AAC3T,UAAM;MACJ;MACA,cAAc;IAChB,IAAI;AACJ,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,MAAM,QAAwC,uBAAwB,EAAE,IAAI,6CAA6C;IACrI;AACA,QAAI,OAAO,YAAY,eAAe,MAAwC;AAC5E,UAAI,QAAQ,iBAAiB,QAAW;AACtC,gBAAQ,MAAM,0GAA0G;MAC1H;IACF;AACA,UAAM,YAAY,OAAO,QAAQ,aAAa,aAAa,QAAQ,SAAS,qBAA4B,CAAC,IAAI,QAAQ,aAAa,CAAC;AACnI,UAAM,eAAe,OAAO,KAAK,QAAQ;AACzC,UAAM,UAAyC;MAC7C,yBAAyB,CAAC;MAC1B,yBAAyB,CAAC;MAC1B,gBAAgB,CAAC;MACjB,eAAe,CAAC;IAClB;AACA,UAAM,iBAAuD;MAC3D,QAAQ,qBAAuDN,UAA6B;AAC1F,cAAM,OAAO,OAAO,wBAAwB,WAAW,sBAAsB,oBAAoB;AACjG,YAAI,CAAC,MAAM;AACT,gBAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,8DAA8D;QACvJ;AACA,YAAI,QAAQ,QAAQ,yBAAyB;AAC3C,gBAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,oFAAoF,IAAI;QACjL;AACA,gBAAQ,wBAAwB,IAAI,IAAIA;AACxC,eAAO;MACT;MACA,WAAW,SAASA,UAAS;AAC3B,gBAAQ,cAAc,KAAK;UACzB;UACA,SAAAA;QACF,CAAC;AACD,eAAO;MACT;MACA,aAAaO,OAAM,eAAe;AAChC,gBAAQ,eAAeA,KAAI,IAAI;AAC/B,eAAO;MACT;MACA,kBAAkBA,OAAMP,UAAS;AAC/B,gBAAQ,wBAAwBO,KAAI,IAAIP;AACxC,eAAO;MACT;IACF;AACA,iBAAa,QAAQ,CAAA,gBAAe;AAClC,YAAM,oBAAoB,SAAS,WAAW;AAC9C,YAAM,iBAAiC;QACrC;QACA,MAAM,QAAQ,MAAM,WAAW;QAC/B,gBAAgB,OAAO,QAAQ,aAAa;MAC9C;AACA,UAAI,mCAA0C,iBAAiB,GAAG;AAChE,yCAAiC,gBAAgB,mBAAmB,gBAAgB,GAAG;MACzF,OAAO;AACL,sCAAqC,gBAAgB,mBAA0B,cAAc;MAC/F;IACF,CAAC;AACD,aAAS,eAAe;AACtB,UAAI,MAAuC;AACzC,YAAI,OAAO,QAAQ,kBAAkB,UAAU;AAC7C,gBAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,wKAAwK;QACjQ;MACF;AACA,YAAM,CAAC,gBAAgB,CAAC,GAAG,iBAAiB,CAAC,GAAG,qBAAqB,MAAS,IAAI,OAAO,QAAQ,kBAAkB,aAAa,8BAA8B,QAAQ,aAAa,IAAI,CAAC,QAAQ,aAAa;AAC7M,YAAM,oBAAoB;QACxB,GAAG;QACH,GAAG,QAAQ;MACb;AACA,aAAO,cAAc,QAAQ,cAAc,CAAA,YAAW;AACpD,iBAAS,OAAO,mBAAmB;AACjC,kBAAQ,QAAQ,KAAK,kBAAkB,GAAG,CAAqB;QACjE;AACA,iBAAS,MAAM,QAAQ,eAAe;AACpC,kBAAQ,WAAW,GAAG,SAAS,GAAG,OAAO;QAC3C;AACA,iBAAS,KAAK,gBAAgB;AAC5B,kBAAQ,WAAW,EAAE,SAAS,EAAE,OAAO;QACzC;AACA,YAAI,oBAAoB;AACtB,kBAAQ,eAAe,kBAAkB;QAC3C;MACF,CAAC;IACH;AACA,UAAM,aAAa,CAAC,UAAiB;AACrC,UAAM,wBAAwB,oBAAI,IAAsG;AACxI,UAAM,qBAAqB,oBAAI,QAA0C;AACzE,QAAI;AACJ,aAAS,QAAQ,OAA0B,QAAuB;AAChE,UAAI,CAAC,SAAU,YAAW,aAAa;AACvC,aAAO,SAAS,OAAO,MAAM;IAC/B;AACA,aAAS,kBAAkB;AACzB,UAAI,CAAC,SAAU,YAAW,aAAa;AACvC,aAAO,SAAS,gBAAgB;IAClC;AACA,aAAS,kBAAmEQ,cAAiC,WAAW,OAA4I;AAClQ,eAAS,YAAY,OAA6C;AAChE,YAAI,aAAa,MAAMA,YAAW;AAClC,YAAI,OAAO,eAAe,aAAa;AACrC,cAAI,UAAU;AACZ,yBAAa,oBAAoB,oBAAoB,aAAa,eAAe;UACnF,WAAW,MAAuC;AAChD,kBAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,gEAAgE;UACzJ;QACF;AACA,eAAO;MACT;AACA,eAAS,aAAa,cAAyC,YAAY;AACzE,cAAM,gBAAgB,oBAAoB,uBAAuB,UAAU,MAAM,oBAAI,QAAQ,CAAC;AAC9F,eAAO,oBAAoB,eAAe,aAAa,MAAM;AAC3D,gBAAM,MAA0C,CAAC;AACjD,qBAAW,CAACD,OAAM,QAAQ,KAAK,OAAO,QAAQ,QAAQ,aAAa,CAAC,CAAC,GAAG;AACtE,gBAAIA,KAAI,IAAI,aAAa,UAAU,aAAa,MAAM,oBAAoB,oBAAoB,aAAa,eAAe,GAAG,QAAQ;UACvI;AACA,iBAAO;QACT,CAAC;MACH;AACA,aAAO;QACL,aAAAC;QACA;QACA,IAAI,YAAY;AACd,iBAAO,aAAa,WAAW;QACjC;QACA;MACF;IACF;AACA,UAAM,QAAkE;MACtE;MACA;MACA,SAAS,QAAQ;MACjB,cAAc,QAAQ;MACtB;MACA,GAAG,kBAAkB,WAAW;MAChC,WAAW,YAAY;QACrB,aAAa;QACb,GAAG;MACL,IAAI,CAAC,GAAG;AACN,cAAM,iBAAiB,WAAW;AAClC,mBAAW,OAAO;UAChB,aAAa;UACb;QACF,GAAG,MAAM;AACT,eAAO;UACL,GAAG;UACH,GAAG,kBAAkB,gBAAgB,IAAI;QAC3C;MACF;IACF;AACA,WAAO;EACT;AACF;AACA,SAAS,aAAyD,UAAa,aAAwC,iBAA8B,UAAoB;AACvK,WAAS,QAAQ,cAAwB,MAAa;AACpD,QAAI,aAAa,YAAY,SAAS;AACtC,QAAI,OAAO,eAAe,aAAa;AACrC,UAAI,UAAU;AACZ,qBAAa,gBAAgB;MAC/B,WAAW,MAAuC;AAChD,cAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,gEAAgE;MACzJ;IACF;AACA,WAAO,SAAS,YAAY,GAAG,IAAI;EACrC;AACA,UAAQ,YAAY;AACpB,SAAO;AACT;AAUO,IAAM,cAA6B,iBAAiB;AAkE3D,SAAS,uBAAsD;AAC7D,WAAS,WAAW,gBAAoD,QAAgG;AACtK,WAAO;MACL,wBAAwB;MACxB;MACA,GAAG;IACL;EACF;AACA,aAAW,YAAY,MAAM;AAC7B,SAAO;IACL,QAAQ,aAAsC;AAC5C,aAAO,OAAO,OAAO;;;QAGnB,CAAC,YAAY,IAAI,KAAK,MAAsC;AAC1D,iBAAO,YAAY,GAAG,IAAI;QAC5B;MACF,EAAE,YAAY,IAAI,GAAG;QACnB,wBAAwB;;MAC1B,CAAU;IACZ;IACA,gBAAgB,SAAS,SAAS;AAChC,aAAO;QACL,wBAAwB;QACxB;QACA;MACF;IACF;IACA;EACF;AACF;AACA,SAAS,8BAAqC;EAC5C;EACA;EACA;AACF,GAAmB,yBAGuD,SAA+C;AACvH,MAAI;AACJ,MAAI;AACJ,MAAI,aAAa,yBAAyB;AACxC,QAAI,kBAAkB,CAAC,mCAAmC,uBAAuB,GAAG;AAClF,YAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,2GAA2G;IACpM;AACA,kBAAc,wBAAwB;AACtC,sBAAkB,wBAAwB;EAC5C,OAAO;AACL,kBAAc;EAChB;AACA,UAAQ,QAAQ,MAAM,WAAW,EAAE,kBAAkB,aAAa,WAAW,EAAE,aAAa,aAAa,kBAAkB,aAAa,MAAM,eAAe,IAAI,aAAa,IAAI,CAAC;AACrL;AACA,SAAS,mCAA0C,mBAAqG;AACtJ,SAAO,kBAAkB,2BAA2B;AACtD;AACA,SAAS,mCAA0C,mBAA2F;AAC5I,SAAO,kBAAkB,2BAA2B;AACtD;AACA,SAAS,iCAAwC;EAC/C;EACA;AACF,GAAmB,mBAA2E,SAA+C,KAA2C;AACtL,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,wLAA6L;EACtR;AACA,QAAM;IACJ;IACA;IACA;IACA;IACA;IACA;EACF,IAAI;AACJ,QAAMb,SAAQ,IAAI,MAAM,gBAAgB,OAAc;AACtD,UAAQ,aAAa,aAAaA,MAAK;AACvC,MAAI,WAAW;AACb,YAAQ,QAAQA,OAAM,WAAW,SAAS;EAC5C;AACA,MAAI,SAAS;AACX,YAAQ,QAAQA,OAAM,SAAS,OAAO;EACxC;AACA,MAAI,UAAU;AACZ,YAAQ,QAAQA,OAAM,UAAU,QAAQ;EAC1C;AACA,MAAI,SAAS;AACX,YAAQ,WAAWA,OAAM,SAAS,OAAO;EAC3C;AACA,UAAQ,kBAAkB,aAAa;IACrC,WAAW,aAAa;IACxB,SAAS,WAAW;IACpB,UAAU,YAAY;IACtB,SAAS,WAAW;EACtB,CAAC;AACH;AACA,SAAS,OAAO;AAAC;AC/qBV,SAAS,wBAAoE;AAClF,SAAO;IACL,KAAK,CAAC;IACN,UAAU,CAAC;EACb;AACF;AACO,SAAS,0BAAkD,cAAoE;AAGpI,WAAS,gBAAgB,kBAAuB,CAAC,GAAG,UAA8C;AAChG,UAAM,QAAQ,OAAO,OAAO,sBAAsB,GAAG,eAAe;AACpE,WAAO,WAAW,aAAa,OAAO,OAAO,QAAQ,IAAI;EAC3D;AACA,SAAO;IACL;EACF;AACF;ACTO,SAAS,yBAAiD;AAG/D,WAAS,aAAgB,aAAgD,UAA+B,CAAC,GAAgC;AACvI,UAAM;MACJ,gBAAAR,kBAAiB;IACnB,IAAI;AACJ,UAAM,YAAY,CAAC,UAA8B,MAAM;AACvD,UAAM,iBAAiB,CAAC,UAA8B,MAAM;AAC5D,UAAM,YAAYA,gBAAe,WAAW,gBAAgB,CAAC,KAAK,aAAkB,IAAI,IAAI,CAAA,OAAM,SAAS,EAAE,CAAE,CAAC;AAChH,UAAM,WAAW,CAAC,GAAY,OAAW;AACzC,UAAM,aAAa,CAAC,UAAyB,OAAW,SAAS,EAAE;AACnE,UAAM,cAAcA,gBAAe,WAAW,CAAA,QAAO,IAAI,MAAM;AAC/D,QAAI,CAAC,aAAa;AAChB,aAAO;QACL;QACA;QACA;QACA;QACA,YAAYA,gBAAe,gBAAgB,UAAU,UAAU;MACjE;IACF;AACA,UAAM,2BAA2BA,gBAAe,aAAgD,cAAc;AAC9G,WAAO;MACL,WAAWA,gBAAe,aAAa,SAAS;MAChD,gBAAgB;MAChB,WAAWA,gBAAe,aAAa,SAAS;MAChD,aAAaA,gBAAe,aAAa,WAAW;MACpD,YAAYA,gBAAe,0BAA0B,UAAU,UAAU;IAC3E;EACF;AACA,SAAO;IACL;EACF;AACF;ACrCO,IAAM,eAAec;AACrB,SAAS,kCAA0D,SAAuD;AAC/H,QAAM,WAAW,oBAAoB,CAAC,GAAc,UAAuC,QAAQ,KAAK,CAAC;AACzG,SAAO,SAAS,UAAiD,OAAgC;AAC/F,WAAO,SAAS,OAAY,MAAS;EACvC;AACF;AACO,SAAS,oBAA+C,SAA+D;AAC5H,SAAO,SAAS,UAAiD,OAAU,KAA8B;AACvG,aAAS,wBAAwBQ,MAAoD;AACnF,aAAO,MAAMA,IAAG;IAClB;AACA,UAAM,aAAa,CAAC,UAAuC;AACzD,UAAI,wBAAwB,GAAG,GAAG;AAChC,gBAAQ,IAAI,SAAS,KAAK;MAC5B,OAAO;AACL,gBAAQ,KAAK,KAAK;MACpB;IACF;AACA,QAAI,aAA0C,KAAK,GAAG;AAIpD,iBAAW,KAAK;AAGhB,aAAO;IACT;AACA,WAAON,QAAgB,OAAO,UAAU;EAC1C;AACF;AChCO,SAAS,cAAsC,QAAW,UAA6B;AAC5F,QAAM,MAAM,SAAS,MAAM;AAC3B,MAA6C,QAAQ,QAAW;AAC9D,YAAQ,KAAK,0EAA0E,mEAAmE,+BAA+B,QAAQ,kCAAkC,SAAS,SAAS,CAAC;EACxP;AACA,SAAO;AACT;AACO,SAAS,oBAA4C,UAAsD;AAChH,MAAI,CAAC,MAAM,QAAQ,QAAQ,GAAG;AAC5B,eAAW,OAAO,OAAO,QAAQ;EACnC;AACA,SAAO;AACT;AACO,SAAS,WAAc,OAAwB;AACpD,SAAQF,QAAQ,KAAK,IAAIS,QAAQ,KAAK,IAAI;AAC5C;AACO,SAAS,0BAAkD,aAA2C,UAA6B,OAAkE;AAC1M,gBAAc,oBAAoB,WAAW;AAC7C,QAAM,mBAAmB,WAAW,MAAM,GAAG;AAC7C,QAAM,cAAc,IAAI,IAAQ,gBAAgB;AAChD,QAAM,QAAa,CAAC;AACpB,QAAM,WAAW,oBAAI,IAAQ,CAAC,CAAC;AAC/B,QAAM,UAA2B,CAAC;AAClC,aAAW,UAAU,aAAa;AAChC,UAAM,KAAK,cAAc,QAAQ,QAAQ;AACzC,QAAI,YAAY,IAAI,EAAE,KAAK,SAAS,IAAI,EAAE,GAAG;AAC3C,cAAQ,KAAK;QACX;QACA,SAAS;MACX,CAAC;IACH,OAAO;AACL,eAAS,IAAI,EAAE;AACf,YAAM,KAAK,MAAM;IACnB;EACF;AACA,SAAO,CAAC,OAAO,SAAS,gBAAgB;AAC1C;ACnCO,SAAS,2BAAmD,UAAwD;AAEzH,WAAS,cAAc,QAAW,OAAgB;AAChD,UAAM,MAAM,cAAc,QAAQ,QAAQ;AAC1C,QAAI,OAAO,MAAM,UAAU;AACzB;IACF;AACA,UAAM,IAAI,KAAK,GAAqB;AACnC,UAAM,SAA2B,GAAG,IAAI;EAC3C;AACA,WAAS,eAAe,aAA2C,OAAgB;AACjF,kBAAc,oBAAoB,WAAW;AAC7C,eAAW,UAAU,aAAa;AAChC,oBAAc,QAAQ,KAAK;IAC7B;EACF;AACA,WAAS,cAAc,QAAW,OAAgB;AAChD,UAAM,MAAM,cAAc,QAAQ,QAAQ;AAC1C,QAAI,EAAE,OAAO,MAAM,WAAW;AAC5B,YAAM,IAAI,KAAK,GAAqB;IACtC;AACA;AACC,UAAM,SAA2B,GAAG,IAAI;EAC3C;AACA,WAAS,eAAe,aAA2C,OAAgB;AACjF,kBAAc,oBAAoB,WAAW;AAC7C,eAAW,UAAU,aAAa;AAChC,oBAAc,QAAQ,KAAK;IAC7B;EACF;AACA,WAAS,cAAc,aAA2C,OAAgB;AAChF,kBAAc,oBAAoB,WAAW;AAC7C,UAAM,MAAM,CAAC;AACb,UAAM,WAAW,CAAC;AAClB,mBAAe,aAAa,KAAK;EACnC;AACA,WAAS,iBAAiB,KAAS,OAAgB;AACjD,WAAO,kBAAkB,CAAC,GAAG,GAAG,KAAK;EACvC;AACA,WAAS,kBAAkB,MAAqB,OAAgB;AAC9D,QAAI,YAAY;AAChB,SAAK,QAAQ,CAAA,QAAO;AAClB,UAAI,OAAO,MAAM,UAAU;AACzB,eAAQ,MAAM,SAA2B,GAAG;AAC5C,oBAAY;MACd;IACF,CAAC;AACD,QAAI,WAAW;AACb,YAAM,MAAO,MAAM,IAAa,OAAO,CAAA,OAAM,MAAM,MAAM,QAAQ;IACnE;EACF;AACA,WAAS,iBAAiB,OAAgB;AACxC,WAAO,OAAO,OAAO;MACnB,KAAK,CAAC;MACN,UAAU,CAAC;IACb,CAAC;EACH;AACA,WAAS,WAAW,MAEjB,QAAuB,OAAmB;AAC3C,UAAMC,YAA2B,MAAM,SAA2B,OAAO,EAAE;AAC3E,QAAIA,cAAa,QAAW;AAC1B,aAAO;IACT;AACA,UAAM,UAAa,OAAO,OAAO,CAAC,GAAGA,WAAU,OAAO,OAAO;AAC7D,UAAM,SAAS,cAAc,SAAS,QAAQ;AAC9C,UAAM,YAAY,WAAW,OAAO;AACpC,QAAI,WAAW;AACb,WAAK,OAAO,EAAE,IAAI;AAClB,aAAQ,MAAM,SAA2B,OAAO,EAAE;IACpD;AACA;AACC,UAAM,SAA2B,MAAM,IAAI;AAC5C,WAAO;EACT;AACA,WAAS,iBAAiB,QAAuB,OAAgB;AAC/D,WAAO,kBAAkB,CAAC,MAAM,GAAG,KAAK;EAC1C;AACA,WAAS,kBAAkB,SAAuC,OAAgB;AAChF,UAAM,UAEF,CAAC;AACL,UAAM,mBAEF,CAAC;AACL,YAAQ,QAAQ,CAAA,WAAU;;AAExB,UAAI,OAAO,MAAM,MAAM,UAAU;AAE/B,yBAAiB,OAAO,EAAE,IAAI;UAC5B,IAAI,OAAO;;;UAGX,SAAS;YACP,IAAG,sBAAiB,OAAO,EAAE,MAA1B,mBAA6B;YAChC,GAAG,OAAO;UACZ;QACF;MACF;IACF,CAAC;AACD,cAAU,OAAO,OAAO,gBAAgB;AACxC,UAAM,oBAAoB,QAAQ,SAAS;AAC3C,QAAI,mBAAmB;AACrB,YAAM,eAAe,QAAQ,OAAO,CAAA,WAAU,WAAW,SAAS,QAAQ,KAAK,CAAC,EAAE,SAAS;AAC3F,UAAI,cAAc;AAChB,cAAM,MAAM,OAAO,OAAO,MAAM,QAAQ,EAAE,IAAI,CAAA,MAAK,cAAc,GAAQ,QAAQ,CAAC;MACpF;IACF;EACF;AACA,WAAS,iBAAiB,QAAW,OAAgB;AACnD,WAAO,kBAAkB,CAAC,MAAM,GAAG,KAAK;EAC1C;AACA,WAAS,kBAAkB,aAA2C,OAAgB;AACpF,UAAM,CAAC,OAAO,OAAO,IAAI,0BAAiC,aAAa,UAAU,KAAK;AACtF,mBAAe,OAAO,KAAK;AAC3B,sBAAkB,SAAS,KAAK;EAClC;AACA,SAAO;IACL,WAAW,kCAAkC,gBAAgB;IAC7D,QAAQ,oBAAoB,aAAa;IACzC,SAAS,oBAAoB,cAAc;IAC3C,QAAQ,oBAAoB,aAAa;IACzC,SAAS,oBAAoB,cAAc;IAC3C,QAAQ,oBAAoB,aAAa;IACzC,WAAW,oBAAoB,gBAAgB;IAC/C,YAAY,oBAAoB,iBAAiB;IACjD,WAAW,oBAAoB,gBAAgB;IAC/C,YAAY,oBAAoB,iBAAiB;IACjD,WAAW,oBAAoB,gBAAgB;IAC/C,YAAY,oBAAoB,iBAAiB;EACnD;AACF;ACjIO,SAAS,gBAAmB,aAAkB,MAAS,oBAAyC;AACrG,MAAI,WAAW;AACf,MAAI,YAAY,YAAY;AAC5B,SAAO,WAAW,WAAW;AAC3B,QAAI,cAAc,WAAW,cAAc;AAC3C,UAAM,cAAc,YAAY,WAAW;AAC3C,UAAM,MAAM,mBAAmB,MAAM,WAAW;AAChD,QAAI,OAAO,GAAG;AACZ,iBAAW,cAAc;IAC3B,OAAO;AACL,kBAAY;IACd;EACF;AACA,SAAO;AACT;AACO,SAAS,OAAU,aAAkB,MAAS,oBAAsC;AACzF,QAAM,gBAAgB,gBAAgB,aAAa,MAAM,kBAAkB;AAC3E,cAAY,OAAO,eAAe,GAAG,IAAI;AACzC,SAAO;AACT;AACO,SAAS,yBAAiD,UAA6B,UAAkD;AAE9I,QAAM;IACJ;IACA;IACA;EACF,IAAI,2BAA2B,QAAQ;AACvC,WAAS,cAAc,QAAW,OAAgB;AAChD,WAAO,eAAe,CAAC,MAAM,GAAG,KAAK;EACvC;AACA,WAAS,eAAe,aAA2C,OAAU,aAA0B;AACrG,kBAAc,oBAAoB,WAAW;AAC7C,UAAM,eAAe,IAAI,IAAQ,eAAe,WAAW,MAAM,GAAG,CAAC;AACrE,UAAM,SAAS,YAAY,OAAO,CAAA,UAAS,CAAC,aAAa,IAAI,cAAc,OAAO,QAAQ,CAAC,CAAC;AAC5F,QAAI,OAAO,WAAW,GAAG;AACvB,oBAAc,OAAO,MAAM;IAC7B;EACF;AACA,WAAS,cAAc,QAAW,OAAgB;AAChD,WAAO,eAAe,CAAC,MAAM,GAAG,KAAK;EACvC;AACA,WAAS,eAAe,aAA2C,OAAgB;AACjF,kBAAc,oBAAoB,WAAW;AAC7C,QAAI,YAAY,WAAW,GAAG;AAC5B,iBAAW,QAAQ,aAAa;AAC9B,eAAQ,MAAM,SAA2B,SAAS,IAAI,CAAC;MACzD;AACA,oBAAc,OAAO,WAAW;IAClC;EACF;AACA,WAAS,cAAc,aAA2C,OAAgB;AAChF,kBAAc,oBAAoB,WAAW;AAC7C,UAAM,WAAW,CAAC;AAClB,UAAM,MAAM,CAAC;AACb,mBAAe,aAAa,OAAO,CAAC,CAAC;EACvC;AACA,WAAS,iBAAiB,QAAuB,OAAgB;AAC/D,WAAO,kBAAkB,CAAC,MAAM,GAAG,KAAK;EAC1C;AACA,WAAS,kBAAkB,SAAuC,OAAgB;AAChF,QAAI,iBAAiB;AACrB,QAAI,cAAc;AAClB,aAAS,UAAU,SAAS;AAC1B,YAAM,SAAyB,MAAM,SAA2B,OAAO,EAAE;AACzE,UAAI,CAAC,QAAQ;AACX;MACF;AACA,uBAAiB;AACjB,aAAO,OAAO,QAAQ,OAAO,OAAO;AACpC,YAAM,QAAQ,SAAS,MAAM;AAC7B,UAAI,OAAO,OAAO,OAAO;AAGvB,sBAAc;AACd,eAAQ,MAAM,SAA2B,OAAO,EAAE;AAClD,cAAM,WAAY,MAAM,IAAa,QAAQ,OAAO,EAAE;AACtD,cAAM,IAAI,QAAQ,IAAI;AACrB,cAAM,SAA2B,KAAK,IAAI;MAC7C;IACF;AACA,QAAI,gBAAgB;AAClB,oBAAc,OAAO,CAAC,GAAG,gBAAgB,WAAW;IACtD;EACF;AACA,WAAS,iBAAiB,QAAW,OAAgB;AACnD,WAAO,kBAAkB,CAAC,MAAM,GAAG,KAAK;EAC1C;AACA,WAAS,kBAAkB,aAA2C,OAAgB;AACpF,UAAM,CAAC,OAAO,SAAS,gBAAgB,IAAI,0BAAiC,aAAa,UAAU,KAAK;AACxG,QAAI,MAAM,QAAQ;AAChB,qBAAe,OAAO,OAAO,gBAAgB;IAC/C;AACA,QAAI,QAAQ,QAAQ;AAClB,wBAAkB,SAAS,KAAK;IAClC;EACF;AACA,WAAS,eAAe,GAAuB,GAAuB;AACpE,QAAI,EAAE,WAAW,EAAE,QAAQ;AACzB,aAAO;IACT;AACA,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,UAAI,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG;AACjB;MACF;AACA,aAAO;IACT;AACA,WAAO;EACT;AAEA,QAAM,gBAA+B,CAAC,OAAO,YAAY,gBAAgB,gBAAgB;AACvF,UAAM,kBAAkB,WAAW,MAAM,QAAQ;AACjD,UAAM,aAAa,WAAW,MAAM,GAAG;AACvC,UAAM,gBAAgB,MAAM;AAC5B,QAAI,MAAoB;AACxB,QAAI,aAAa;AACf,YAAM,IAAI,IAAI,UAAU;IAC1B;AACA,QAAI,iBAAsB,CAAC;AAC3B,eAAW,MAAM,KAAK;AACpB,YAAM,SAAS,gBAAgB,EAAE;AACjC,UAAI,QAAQ;AACV,uBAAe,KAAK,MAAM;MAC5B;IACF;AACA,UAAM,qBAAqB,eAAe,WAAW;AAGrD,eAAW,QAAQ,YAAY;AAC7B,oBAAc,SAAS,IAAI,CAAC,IAAI;AAChC,UAAI,CAAC,oBAAoB;AAEvB,eAAO,gBAAgB,MAAM,QAAQ;MACvC;IACF;AACA,QAAI,oBAAoB;AAEtB,uBAAiB,WAAW,MAAM,EAAE,KAAK,QAAQ;IACnD,WAAW,gBAAgB;AAEzB,qBAAe,KAAK,QAAQ;IAC9B;AACA,UAAM,eAAe,eAAe,IAAI,QAAQ;AAChD,QAAI,CAAC,eAAe,YAAY,YAAY,GAAG;AAC7C,YAAM,MAAM;IACd;EACF;AACA,SAAO;IACL;IACA;IACA;IACA,QAAQ,oBAAoB,aAAa;IACzC,WAAW,oBAAoB,gBAAgB;IAC/C,WAAW,oBAAoB,gBAAgB;IAC/C,QAAQ,oBAAoB,aAAa;IACzC,SAAS,oBAAoB,cAAc;IAC3C,QAAQ,oBAAoB,aAAa;IACzC,SAAS,oBAAoB,cAAc;IAC3C,YAAY,oBAAoB,iBAAiB;IACjD,YAAY,oBAAoB,iBAAiB;EACnD;AACF;ACrJO,SAAS,oBAAuB,UAA6C,CAAC,GAA+B;AAClH,QAAM;IACJ;IACA;EACF,IAAiD;IAC/C,cAAc;IACd,UAAU,CAAC,aAAkB,SAAS;IACtC,GAAG;EACL;AACA,QAAM,eAAe,eAAe,yBAAyB,UAAU,YAAY,IAAI,2BAA2B,QAAQ;AAC1H,QAAM,eAAe,0BAA0B,YAAY;AAC3D,QAAM,mBAAmB,uBAAoC;AAC7D,SAAO;IACL;IACA;IACA,GAAG;IACH,GAAG;IACH,GAAG;EACL;AACF;AEnCA,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,YAAY;AAClB,IAAM,YAAY;AAGX,IAAM,gBAAgB,QAAQ,SAAS;AACvC,IAAM,gBAAgB,QAAQ,SAAS;AACvC,IAAM,oBAAoB,GAAG,QAAQ,IAAI,SAAS;AAClD,IAAM,oBAAoB,GAAG,QAAQ,IAAI,SAAS;AAClD,IAAM,iBAAN,MAAgD;EAGrD,YAAmB,MAA0B;AAF7C,gCAAO;AACP;AACmB,SAAA,OAAA;AACjB,SAAK,UAAU,GAAG,IAAI,IAAI,SAAS,aAAa,IAAI;EACtD;AACF;ACfO,IAAM,iBAAuG,CAAC,MAAe,aAAqB;AACvJ,MAAI,OAAO,SAAS,YAAY;AAC9B,UAAM,IAAI,UAAU,QAAwC,uBAAwB,EAAE,IAAI,GAAG,QAAQ,oBAAoB;EAC3H;AACF;AACO,IAAMrB,QAAO,MAAM;AAAC;AACpB,IAAM,iBAAiB,CAAK,SAAqB,UAAUA,UAAqB;AACrF,UAAQ,MAAM,OAAO;AACrB,SAAO;AACT;AACO,IAAM,yBAAyB,CAAC,aAA0B,aAAmC;AAClG,cAAY,iBAAiB,SAAS,UAAU;IAC9C,MAAM;EACR,CAAC;AACD,SAAO,MAAM,YAAY,oBAAoB,SAAS,QAAQ;AAChE;AAYO,IAAM,4BAA4B,CAAK,iBAAkC,WAAoB;AAElG,QAAM,SAAS,gBAAgB;AAC/B,MAAI,OAAO,SAAS;AAClB;EACF;AAMA,MAAI,EAAE,YAAY,SAAS;AACzB,WAAO,eAAe,QAAQ,UAAU;MACtC,YAAY;MACZ,OAAO;MACP,cAAc;MACd,UAAU;IACZ,CAAC;EACH;AACA;AACC,kBAAgB,MAAkC,MAAM;AAC3D;ACxCO,IAAM,iBAAiB,CAAC,WAA8B;AAC3D,MAAI,OAAO,SAAS;AAClB,UAAM;MACJ;IACF,IAAI;AACJ,UAAM,IAAI,eAAe,MAAM;EACjC;AACF;AAOO,SAAS,eAAkB,QAAuC,SAAiC;AACxG,MAAI,UAAUA;AACd,SAAO,IAAI,QAAW,CAAC,SAAS,WAAW;AACzC,UAAM,kBAAkB,MAAM,OAAO,IAAI,eAAe,OAAO,MAAM,CAAC;AACtE,QAAI,OAAO,SAAS;AAClB,sBAAgB;AAChB;IACF;AACA,cAAU,uBAAuB,QAAQ,eAAe;AACxD,YAAQ,QAAQ,MAAM,QAAQ,CAAC,EAAE,KAAK,SAAS,MAAM;EACvD,CAAC,EAAE,QAAQ,MAAM;AAEf,cAAUA;EACZ,CAAC;AACH;AASO,IAAM,UAAU,OAAWsB,OAAwB,YAAiD;AACzG,MAAI;AACF,UAAM,QAAQ,QAAQ;AACtB,UAAM,QAAQ,MAAMA,MAAK;AACzB,WAAO;MACL,QAAQ;MACR;IACF;EACF,SAAS,OAAY;AACnB,WAAO;MACL,QAAQ,iBAAiB,iBAAiB,cAAc;MACxD;IACF;EACF,UAAA;AACE;EACF;AACF;AASO,IAAM,cAAc,CAAK,WAAwB;AACtD,SAAO,CAAC,YAAoC;AAC1C,WAAO,eAAe,eAAe,QAAQ,OAAO,EAAE,KAAK,CAAA,WAAU;AACnE,qBAAe,MAAM;AACrB,aAAO;IACT,CAAC,CAAC;EACJ;AACF;AAQO,IAAM,cAAc,CAAC,WAAwB;AAClD,QAAM,QAAQ,YAAkB,MAAM;AACtC,SAAO,CAAC,cAAqC;AAC3C,WAAO,MAAM,IAAI,QAAc,CAAA,YAAW,WAAW,SAAS,SAAS,CAAC,CAAC;EAC3E;AACF;AH9EA,IAAM;EACJ;AACF,IAAI;AAIJ,IAAM,qBAAqB,CAAC;AAC5B,IAAM,MAAM;AACZ,IAAM,aAAa,CAAC,mBAAmD,2BAA2C;AAChH,QAAM,kBAAkB,CAAC,eAAgC,uBAAuB,mBAAmB,MAAM,0BAA0B,YAAY,kBAAkB,MAAM,CAAC;AACxK,SAAO,CAAK,cAAqC,SAAsC;AACrF,mBAAe,cAAc,cAAc;AAC3C,UAAM,uBAAuB,IAAI,gBAAgB;AACjD,oBAAgB,oBAAoB;AACpC,UAAM,SAAS,QAAW,YAAwB;AAChD,qBAAe,iBAAiB;AAChC,qBAAe,qBAAqB,MAAM;AAC1C,YAAMC,UAAU,MAAM,aAAa;QACjC,OAAO,YAAY,qBAAqB,MAAM;QAC9C,OAAO,YAAY,qBAAqB,MAAM;QAC9C,QAAQ,qBAAqB;MAC/B,CAAC;AACD,qBAAe,qBAAqB,MAAM;AAC1C,aAAOA;IACT,GAAG,MAAM,0BAA0B,sBAAsB,aAAa,CAAC;AACvE,QAAI,6BAAM,UAAU;AAClB,6BAAuB,KAAK,OAAO,MAAMvB,KAAI,CAAC;IAChD;AACA,WAAO;MACL,QAAQ,YAA2B,iBAAiB,EAAE,MAAM;MAC5D,SAAS;AACP,kCAA0B,sBAAsB,aAAa;MAC/D;IACF;EACF;AACF;AACA,IAAM,oBAAoB,CAAK,gBAAwE,WAAwC;AAQ7I,QAAM,OAAO,OAA2C,WAAc,YAAgC;AACpG,mBAAe,MAAM;AAGrB,QAAI,cAAmC,MAAM;IAAC;AAC9C,UAAM,eAAe,IAAI,QAAwB,CAAC,SAAS,WAAW;AAEpE,UAAI,gBAAgB,eAAe;QACjC;QACA,QAAQ,CAAC,QAAQ,gBAAsB;AAErC,sBAAY,YAAY;AAExB,kBAAQ,CAAC,QAAQ,YAAY,SAAS,GAAG,YAAY,iBAAiB,CAAC,CAAC;QAC1E;MACF,CAAC;AACD,oBAAc,MAAM;AAClB,sBAAc;AACd,eAAO;MACT;IACF,CAAC;AACD,UAAM,WAAwD,CAAC,YAAY;AAC3E,QAAI,WAAW,MAAM;AACnB,eAAS,KAAK,IAAI,QAAc,CAAA,YAAW,WAAW,SAAS,SAAS,IAAI,CAAC,CAAC;IAChF;AACA,QAAI;AACF,YAAM,SAAS,MAAM,eAAe,QAAQ,QAAQ,KAAK,QAAQ,CAAC;AAClE,qBAAe,MAAM;AACrB,aAAO;IACT,UAAA;AAEE,kBAAY;IACd;EACF;AACA,SAAQ,CAAC,WAAoC,YAAgC,eAAe,KAAK,WAAW,OAAO,CAAC;AACtH;AACA,IAAM,4BAA4B,CAAC,YAAwC;AACzE,MAAI;IACF;IACA;IACA;IACA;IACA;EACF,IAAI;AACJ,MAAI,MAAM;AACR,gBAAY,aAAa,IAAI,EAAE;EACjC,WAAW,eAAe;AACxB,WAAO,cAAe;AACtB,gBAAY,cAAc;EAC5B,WAAW,SAAS;AAClB,gBAAY;EACd,WAAW,WAAW;EAEtB,OAAO;AACL,UAAM,IAAI,MAAM,QAAwC,uBAAwB,EAAE,IAAI,yFAAyF;EACjL;AACA,iBAAe,QAAQ,kBAAkB;AACzC,SAAO;IACL;IACA;IACA;EACF;AACF;AAGO,IAAM,sBAAwE,OAAO,CAAC,YAAwC;AACnI,QAAM;IACJ;IACA;IACA;EACF,IAAI,0BAA0B,OAAO;AACrC,QAAM,QAAgC;IACpC,IAAI,OAAO;IACX;IACA;IACA;IACA,SAAS,oBAAI,IAAqB;IAClC,aAAa,MAAM;AACjB,YAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,6BAA6B;IACtH;EACF;AACA,SAAO;AACT,GAAG;EACD,WAAW,MAAM;AACnB,CAAC;AACD,IAAM,oBAAoB,CAAC,aAAyC,YAAwC;AAC1G,QAAM;IACJ;IACA;IACA;EACF,IAAI,0BAA0B,OAAO;AACrC,SAAO,MAAM,KAAK,YAAY,OAAO,CAAC,EAAE,KAAK,CAAA,UAAS;AACpD,UAAM,uBAAuB,OAAO,SAAS,WAAW,MAAM,SAAS,OAAO,MAAM,cAAc;AAClG,WAAO,wBAAwB,MAAM,WAAW;EAClD,CAAC;AACH;AACA,IAAM,wBAAwB,CAAC,UAA2D;AACxF,QAAM,QAAQ,QAAQ,CAAA,eAAc;AAClC,8BAA0B,YAAY,iBAAiB;EACzD,CAAC;AACH;AACA,IAAM,gCAAgC,CAAC,gBAA4C;AACjF,SAAO,MAAM;AACX,gBAAY,QAAQ,qBAAqB;AACzC,gBAAY,MAAM;EACpB;AACF;AASA,IAAM,oBAAoB,CAAC,cAAoC,eAAwB,cAAuC;AAC5H,MAAI;AACF,iBAAa,eAAe,SAAS;EACvC,SAAS,mBAAmB;AAG1B,eAAW,MAAM;AACf,YAAM;IACR,GAAG,CAAC;EACN;AACF;AAKO,IAAM,cAA6B,OAAsB,aAAa,GAAG,GAAG,MAAM,GAAG;EAC1F,WAAW,MAAM;AACnB,CAAC;AAKM,IAAM,oBAAmC,aAAa,GAAG,GAAG,YAAY;AAKxE,IAAM,iBAAgC,OAAsB,aAAa,GAAG,GAAG,SAAS,GAAG;EAChG,WAAW,MAAM;AACnB,CAAC;AACD,IAAM,sBAA4C,IAAI,SAAoB;AACxE,UAAQ,MAAM,GAAG,GAAG,UAAU,GAAG,IAAI;AACvC;AAKO,IAAM,2BAA2B,CAAyI,oBAAoE,CAAC,MAAM;AAC1P,QAAM,cAAc,oBAAI,IAA2B;AACnD,QAAM;IACJ;IACA,UAAU;EACZ,IAAI;AACJ,iBAAe,SAAS,SAAS;AACjC,QAAM,cAAc,CAAC,UAAyB;AAC5C,UAAM,cAAc,MAAM,YAAY,OAAO,MAAM,EAAE;AACrD,gBAAY,IAAI,MAAM,IAAI,KAAK;AAC/B,WAAO,CAAC,kBAA+C;AACrD,YAAM,YAAY;AAClB,UAAI,+CAAe,cAAc;AAC/B,8BAAsB,KAAK;MAC7B;IACF;EACF;AACA,QAAM,iBAAkB,CAAC,YAAwC;AAC/D,UAAM,QAAQ,kBAAkB,aAAa,OAAO,KAAK,oBAAoB,OAAc;AAC3F,WAAO,YAAY,KAAK;EAC1B;AACA,SAAO,gBAAgB;IACrB,WAAW,MAAM;EACnB,CAAC;AACD,QAAM,gBAAgB,CAAC,YAA8E;AACnG,UAAM,QAAQ,kBAAkB,aAAa,OAAO;AACpD,QAAI,OAAO;AACT,YAAM,YAAY;AAClB,UAAI,QAAQ,cAAc;AACxB,8BAAsB,KAAK;MAC7B;IACF;AACA,WAAO,CAAC,CAAC;EACX;AACA,SAAO,eAAe;IACpB,WAAW,MAAM;EACnB,CAAC;AACD,QAAM,iBAAiB,OAAO,OAAwD,QAAiB,KAAoB,qBAAsC;AAC/J,UAAM,yBAAyB,IAAI,gBAAgB;AACnD,UAAM,OAAO,kBAAkB,gBAA6C,uBAAuB,MAAM;AACzG,UAAM,mBAAmC,CAAC;AAC1C,QAAI;AACF,YAAM,QAAQ,IAAI,sBAAsB;AACxC,YAAM,QAAQ,QAAQ,MAAM;QAAO;;QAEnC,OAAO,CAAC,GAAG,KAAK;UACd;UACA,WAAW,CAAC,WAAsC,YAAqB,KAAK,WAAW,OAAO,EAAE,KAAK,OAAO;UAC5G;UACA,OAAO,YAAY,uBAAuB,MAAM;UAChD,OAAO,YAAiB,uBAAuB,MAAM;UACrD;UACA,QAAQ,uBAAuB;UAC/B,MAAM,WAAW,uBAAuB,QAAQ,gBAAgB;UAChE,aAAa,MAAM;UACnB,WAAW,MAAM;AACf,wBAAY,IAAI,MAAM,IAAI,KAAK;UACjC;UACA,uBAAuB,MAAM;AAC3B,kBAAM,QAAQ,QAAQ,CAAC,YAAY,GAAGwB,SAAQ;AAC5C,kBAAI,eAAe,wBAAwB;AACzC,0CAA0B,YAAY,iBAAiB;AACvD,gBAAAA,KAAI,OAAO,UAAU;cACvB;YACF,CAAC;UACH;UACA,QAAQ,MAAM;AACZ,sCAA0B,wBAAwB,iBAAiB;AACnE,kBAAM,QAAQ,OAAO,sBAAsB;UAC7C;UACA,kBAAkB,MAAM;AACtB,2BAAe,uBAAuB,MAAM;UAC9C;QACF,CAAC;MAAC,CAAC;IACL,SAAS,eAAe;AACtB,UAAI,EAAE,yBAAyB,iBAAiB;AAC9C,0BAAkB,SAAS,eAAe;UACxC,UAAU;QACZ,CAAC;MACH;IACF,UAAA;AACE,YAAM,QAAQ,IAAI,gBAAgB;AAClC,gCAA0B,wBAAwB,iBAAiB;AACnE,YAAM,QAAQ,OAAO,sBAAsB;IAC7C;EACF;AACA,QAAM,0BAA0B,8BAA8B,WAAW;AACzE,QAAM,aAAyE,CAAA,QAAO,CAAA,SAAQ,CAAA,WAAU;AACtG,QAAI,CAACpB,SAAS,MAAM,GAAG;AAErB,aAAO,KAAK,MAAM;IACpB;AACA,QAAI,YAAY,MAAM,MAAM,GAAG;AAC7B,aAAO,eAAe,OAAO,OAAc;IAC7C;AACA,QAAI,kBAAkB,MAAM,MAAM,GAAG;AACnC,8BAAwB;AACxB;IACF;AACA,QAAI,eAAe,MAAM,MAAM,GAAG;AAChC,aAAO,cAAc,OAAO,OAAO;IACrC;AAGA,QAAI,gBAAuD,IAAI,SAAS;AAIxE,UAAM,mBAAmB,MAAiB;AACxC,UAAI,kBAAkB,oBAAoB;AACxC,cAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,GAAG,GAAG,qDAAqD;MACpJ;AACA,aAAO;IACT;AACA,QAAI;AACJ,QAAI;AAEF,eAAS,KAAK,MAAM;AACpB,UAAI,YAAY,OAAO,GAAG;AACxB,cAAM,eAAe,IAAI,SAAS;AAElC,cAAM,kBAAkB,MAAM,KAAK,YAAY,OAAO,CAAC;AACvD,mBAAW,SAAS,iBAAiB;AACnC,cAAI,cAAc;AAClB,cAAI;AACF,0BAAc,MAAM,UAAU,QAAQ,cAAc,aAAa;UACnE,SAAS,gBAAgB;AACvB,0BAAc;AACd,8BAAkB,SAAS,gBAAgB;cACzC,UAAU;YACZ,CAAC;UACH;AACA,cAAI,CAAC,aAAa;AAChB;UACF;AACA,yBAAe,OAAO,QAAQ,KAAK,gBAAgB;QACrD;MACF;IACF,UAAA;AAEE,sBAAgB;IAClB;AACA,WAAO;EACT;AACA,SAAO;IACL;IACA;IACA;IACA,gBAAgB;EAClB;AACF;AIhWA,IAAM,wBAAwB,CAAsF,gBAA4F;EAC9M;EACA,SAAS,oBAAI,IAAI;AACnB;AACA,IAAM,gBAAgB,CAAC,eAAuB,CAAC,WAAA;;AAI1C,iDAAQ,SAAR,mBAAc,gBAAe;;AAC3B,IAAM,0BAA0B,MAA2I;AAChL,QAAM,aAAa,OAAO;AAC1B,QAAM,gBAAgB,oBAAI,IAAgF;AAC1G,QAAM,iBAAiB,OAAO,OAAO,aAAa,yBAAyB,IAAI,iBAAyD;IACtI,SAAS;IACT,MAAM;MACJ;IACF;EACF,EAAE,GAAG;IACH,WAAW,MAAM;EACnB,CAAC;AACD,QAAM,gBAAgB,OAAO,OAAO,SAASqB,kBAAiB,aAAqD;AACjH,gBAAY,QAAQ,CAAAjB,gBAAc;AAChC,0BAAoB,eAAeA,aAAY,qBAAqB;IACtE,CAAC;EACH,GAAG;IACD,WAAW,MAAM;EACnB,CAAC;AACD,QAAM,qBAA0D,CAAA,QAAO;AACrE,UAAM,oBAAoB,MAAM,KAAK,cAAc,OAAO,CAAC,EAAE,IAAI,CAAA,UAAS,oBAAoB,MAAM,SAAS,KAAK,MAAM,UAAU,CAAC;AACnI,WAAOC,QAAQ,GAAG,iBAAiB;EACrC;AACA,QAAM,mBAAmB,QAAQ,gBAAgB,cAAc,UAAU,CAAC;AAC1E,QAAM,aAAqD,CAAA,QAAO,CAAA,SAAQ,CAAA,WAAU;AAClF,QAAI,iBAAiB,MAAM,GAAG;AAC5B,oBAAc,GAAG,OAAO,OAAO;AAC/B,aAAO,IAAI;IACb;AACA,WAAO,mBAAmB,GAAG,EAAE,IAAI,EAAE,MAAM;EAC7C;AACA,SAAO;IACL;IACA;IACA;IACA;EACF;AACF;ACkLA,IAAM,cAAc,CAAC,mBAA8E,iBAAiB,kBAAkB,OAAO,eAAe,gBAAgB;AAC5K,IAAM,cAAc,CAAC,WAA6C,OAAO,QAAQ,CAAA,eAAc,YAAY,UAAU,IAAI,CAAC,CAAC,WAAW,aAAa,WAAW,OAAO,CAAU,IAAI,OAAO,QAAQ,UAAU,CAAC;AAC7M,IAAM,iBAAiB,OAAO,IAAI,0BAA0B;AAC5D,IAAM,eAAe,CAAC,UAAe,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,cAAc;AACtE,IAAM,gBAAgB,oBAAI,QAAwB;AAClD,IAAM,mBAAmB,CAAwB,OAAc,YAAmD,sBAAoD,oBAAoB,eAAe,OAAO,MAAM,IAAI,MAAM,OAAO;EACrO,KAAK,CAAC,QAAQ,MAAM,aAAa;AAC/B,QAAI,SAAS,eAAgB,QAAO;AACpC,UAAM,SAAS,QAAQ,IAAI,QAAQ,MAAM,QAAQ;AACjD,QAAI,OAAO,WAAW,aAAa;AACjC,YAAM,SAAS,kBAAkB,IAAI;AACrC,UAAI,OAAO,WAAW,YAAa,QAAO;AAC1C,YAAM,UAAU,WAAW,IAAI;AAC/B,UAAI,SAAS;AAEX,cAAM,gBAAgB,QAAQ,QAAW;UACvC,MAAM,OAAO;QACf,CAAC;AACD,YAAI,OAAO,kBAAkB,aAAa;AACxC,gBAAM,IAAI,MAAM,QAAwC,uBAAwB,EAAE,IAAI,8BAA8B,KAAK,SAAS,CAAC,mRAAuS;QAC5a;AACA,0BAAkB,IAAI,IAAI;AAC1B,eAAO;MACT;IACF;AACA,WAAO;EACT;AACF,CAAC,CAAC;AACF,IAAMY,YAAW,CAAC,UAAe;AAC/B,MAAI,CAAC,aAAa,KAAK,GAAG;AACxB,UAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,sCAAsC;EAC/H;AACA,SAAO,MAAM,cAAc;AAC7B;AACA,IAAM,cAAc,CAAC;AACrB,IAAM,cAA4C,CAAC,QAAQ,gBAAgB;AACpE,SAAS,iBAAkE,QAAgE;AAChJ,QAAM,aAAa,OAAO,YAAqB,YAAY,MAAM,CAAC;AAClE,QAAM,aAAa,MAAM,OAAO,KAAK,UAAU,EAAE,SAASK,gBAAgB,UAAU,IAAI;AACxF,MAAI,UAAU,WAAW;AACzB,WAAS,gBAAgB,OAAgC,QAAuB;AAC9E,WAAO,QAAQ,OAAO,MAAM;EAC9B;AACA,kBAAgB,uBAAuB,MAAM;AAC7C,QAAM,oBAAkD,CAAC;AACzD,QAAM,SAAS,CAAC,OAAqB,SAAuB,CAAC,MAA8B;AACzF,UAAM;MACJ;MACA,SAAS;IACX,IAAI;AACJ,UAAM,iBAAiB,WAAW,WAAW;AAC7C,QAAI,CAAC,OAAO,oBAAoB,kBAAkB,mBAAmB,iBAAiB;AACpF,UAAI,OAAO,YAAY,eAAe,MAAwC;AAC5E,gBAAQ,MAAM,0DAA0D,WAAW,gDAAgD;MACrI;AACA,aAAO;IACT;AACA,QAAI,OAAO,oBAAoB,mBAAmB,iBAAiB;AACjE,aAAO,kBAAkB,WAAW;IACtC;AACA,eAAW,WAAW,IAAI;AAC1B,cAAU,WAAW;AACrB,WAAO;EACT;AACA,QAAM,WAAW,OAAO,OAAO,SAAS,aAAkE,YAAkD,aAA8D;AACxN,WAAO,SAASC,UAAS,UAAiB,MAAY;AACpD,aAAO,WAAW,iBAAiB,cAAc,YAAY,OAAc,GAAG,IAAI,IAAI,OAAO,YAAY,iBAAiB,GAAG,GAAG,IAAI;IACtI;EACF,GAAG;IACD,UAAAN;EACF,CAAC;AACD,SAAO,OAAO,OAAO,iBAAiB;IACpC;IACA;EACF,CAAC;AACH;AC3SO,SAAS,uBAAuB,MAAc;AACnD,SAAO,iCAAiC,IAAI,oDAAoD,IAAI;AACtG;", "names": ["proto", "listener", "createStore", "isPlainObject", "proto", "<PERSON><PERSON><PERSON>", "isPlainObject", "proto", "immer", "isSet", "current", "proto", "Immer", "base", "Immer", "emptyObject", "createSelector", "createSelector", "createDraftSafeSelector", "args", "noop", "isActionCreator", "stringify", "getSerialize", "isAction", "thunk", "listener", "isPlainObject", "middleware", "compose", "reducer", "isDraft", "isDraftable", "createNextState", "createAsyncThunk", "ReducerType", "createSlice", "name", "reducerPath", "arg", "current", "original", "task", "result", "set", "addMiddleware", "combineReducers", "selector"]}