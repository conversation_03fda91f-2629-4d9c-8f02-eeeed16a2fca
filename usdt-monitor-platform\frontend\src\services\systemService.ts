import { SystemSettings, Alert, PaginatedResponse } from '@/types'
import apiClient from './apiClient'

class SystemService {
  // 系统设置
  async getSettings(): Promise<SystemSettings> {
    return await apiClient.get<SystemSettings>('/system/settings')
  }

  async updateSettings(settings: Partial<SystemSettings>): Promise<SystemSettings> {
    return await apiClient.put<SystemSettings>('/system/settings', settings)
  }

  async resetSettings(): Promise<SystemSettings> {
    return await apiClient.post<SystemSettings>('/system/settings/reset')
  }

  // 告警管理
  async getAlerts(params: {
    page?: number
    pageSize?: number
    type?: string
    severity?: string
    unreadOnly?: boolean
    startDate?: string
    endDate?: string
  }): Promise<PaginatedResponse<Alert>> {
    const queryParams = new URLSearchParams()
    
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.pageSize) queryParams.append('page_size', params.pageSize.toString())
    if (params.type) queryParams.append('type', params.type)
    if (params.severity) queryParams.append('severity', params.severity)
    if (params.unreadOnly) queryParams.append('unread_only', params.unreadOnly.toString())
    if (params.startDate) queryParams.append('start_date', params.startDate)
    if (params.endDate) queryParams.append('end_date', params.endDate)

    return await apiClient.get<PaginatedResponse<Alert>>(`/system/alerts?${queryParams.toString()}`)
  }

  async markAlertAsRead(alertId: string): Promise<void> {
    return await apiClient.patch<void>(`/system/alerts/${alertId}/read`)
  }

  async markAllAlertsAsRead(): Promise<void> {
    return await apiClient.patch<void>('/system/alerts/read-all')
  }

  async deleteAlert(alertId: string): Promise<void> {
    return await apiClient.delete<void>(`/system/alerts/${alertId}`)
  }

  async createAlert(alertData: {
    type: string
    severity: string
    message: string
    userId?: string
  }): Promise<Alert> {
    return await apiClient.post<Alert>('/system/alerts', alertData)
  }

  // 系统监控
  async getSystemStatus(): Promise<{
    status: 'healthy' | 'warning' | 'error'
    uptime: number
    version: string
    environment: string
    services: Array<{
      name: string
      status: 'running' | 'stopped' | 'error'
      uptime: number
      memory: number
      cpu: number
    }>
  }> {
    return await apiClient.get('/system/status')
  }

  async getSystemMetrics(period: '1h' | '24h' | '7d'): Promise<{
    cpu: Array<{ timestamp: string; value: number }>
    memory: Array<{ timestamp: string; value: number }>
    disk: Array<{ timestamp: string; value: number }>
    network: Array<{ timestamp: string; in: number; out: number }>
  }> {
    return await apiClient.get(`/system/metrics?period=${period}`)
  }

  // 日志管理
  async getLogs(params: {
    level?: 'debug' | 'info' | 'warning' | 'error'
    service?: string
    startDate?: string
    endDate?: string
    page?: number
    pageSize?: number
  }): Promise<PaginatedResponse<{
    id: string
    timestamp: string
    level: string
    service: string
    message: string
    metadata?: any
  }>> {
    const queryParams = new URLSearchParams()
    
    if (params.level) queryParams.append('level', params.level)
    if (params.service) queryParams.append('service', params.service)
    if (params.startDate) queryParams.append('start_date', params.startDate)
    if (params.endDate) queryParams.append('end_date', params.endDate)
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.pageSize) queryParams.append('page_size', params.pageSize.toString())

    return await apiClient.get(`/system/logs?${queryParams.toString()}`)
  }

  // 备份和恢复
  async createBackup(): Promise<{
    id: string
    filename: string
    size: number
    createdAt: string
  }> {
    return await apiClient.post('/system/backup')
  }

  async getBackups(): Promise<Array<{
    id: string
    filename: string
    size: number
    createdAt: string
  }>> {
    return await apiClient.get('/system/backups')
  }

  async downloadBackup(backupId: string): Promise<Blob> {
    const response = await apiClient.getInstance().get(`/system/backups/${backupId}/download`, {
      responseType: 'blob',
    })
    return response.data
  }

  async deleteBackup(backupId: string): Promise<void> {
    return await apiClient.delete(`/system/backups/${backupId}`)
  }

  async restoreBackup(backupId: string): Promise<void> {
    return await apiClient.post(`/system/backups/${backupId}/restore`)
  }

  // 系统维护
  async restartService(serviceName: string): Promise<void> {
    return await apiClient.post(`/system/services/${serviceName}/restart`)
  }

  async clearCache(): Promise<void> {
    return await apiClient.post('/system/cache/clear')
  }

  async optimizeDatabase(): Promise<void> {
    return await apiClient.post('/system/database/optimize')
  }
}

export const systemService = new SystemService()
export default systemService
