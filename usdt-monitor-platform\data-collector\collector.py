import asyncio
import uuid
import time
import requests
from datetime import datetime
from typing import Optional, Dict, Any
import sys
import os

# 添加后端路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.usdt_data import USDTData
from app.schemas.usdt_data import USDTDataCreate


class USDTDataCollector:
    def __init__(self):
        self.last_price = None
        self.session_factory = SessionLocal
        
    def get_high_precision_price(self) -> Optional[float]:
        """尝试从高精度数据源获取USDT价格"""
        # 优先使用Coinbase API（已验证有效）
        try:
            coinbase_url = "https://api.coinbase.com/v2/exchange-rates?currency=USDT"
            response = requests.get(coinbase_url, timeout=5)
            if response.status_code == 200:
                data = response.json()
                usd_rate = data.get('data', {}).get('rates', {}).get('USD')
                if usd_rate:
                    price = 1.0 / float(usd_rate)
                    if 0.99 <= price <= 1.01:  # 合理范围检查
                        print(f"🎯 获取到高精度价格: ${price:.10f} (来源: Coinbase)")
                        return price
        except Exception as e:
            print(f"⚠️ Coinbase API错误: {e}")
        
        # 备用：尝试其他交易所API
        try:
            # 尝试Kraken API
            kraken_url = "https://api.kraken.com/0/public/Ticker?pair=USDTUSD"
            response = requests.get(kraken_url, timeout=5)
            if response.status_code == 200:
                data = response.json()
                result = data.get('result', {})
                if result:
                    pair_data = list(result.values())[0]
                    price = float(pair_data.get('c', [1.0])[0])  # 最新价格
                    if 0.99 <= price <= 1.01:
                        print(f"🎯 获取到高精度价格: ${price:.10f} (来源: Kraken)")
                        return price
        except Exception as e:
            print(f"⚠️ Kraken API错误: {e}")
        
        print("❌ 无法获取高精度价格，将使用估算价格")
        return None

    def get_coingecko_data(self) -> Optional[Dict[str, Any]]:
        """从CoinGecko获取USDT数据"""
        try:
            url = "https://api.coingecko.com/api/v3/coins/tether"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ CoinGecko API错误: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 获取CoinGecko数据失败: {e}")
            return None

    def process_data(self, market_data: Dict[str, Any]) -> Optional[USDTDataCreate]:
        """处理市场数据"""
        try:
            # 获取高精度价格
            high_precision_price = self.get_high_precision_price()
            
            # 基础价格
            base_price = market_data.get('market_data', {}).get('current_price', {}).get('usd', 1.0)
            
            # 强制使用高精度价格，如果没有则基于24h变化计算
            price_change_24h = market_data.get('market_data', {}).get('price_change_24h', 0)
            
            if high_precision_price:
                current_price = high_precision_price
                price_precision = f"{current_price:.10f}"
                print(f"✅ 使用高精度价格: ${price_precision}")
            elif price_change_24h and price_change_24h != 0:
                # 基于24h变化计算真实价格（这是关键修复）
                current_price = 1.0 + float(price_change_24h)
                price_precision = f"{current_price:.10f}"
                print(f"🔧 基于24h变化计算价格: ${price_precision} (变化: {price_change_24h})")
            else:
                current_price = base_price
                price_precision = f"{current_price:.10f}"
                print(f"⚠️ 使用基础价格: ${price_precision}")
            
            # 计算价格变化
            price_change_from_last = 0
            if self.last_price is not None:
                price_change_from_last = ((current_price - self.last_price) / self.last_price) * 100
            
            # 更新最后价格
            self.last_price = current_price
            
            # 获取市场数据
            market_info = market_data.get('market_data', {})
            
            # 创建数据记录
            timestamp = datetime.utcnow()
            unix_timestamp = int(timestamp.timestamp())
            
            usdt_data = USDTDataCreate(
                timestamp=timestamp,
                unix_timestamp=unix_timestamp,
                name=market_data.get('name', 'Tether'),
                symbol=market_data.get('symbol', 'USDT').upper(),
                current_price_usd=current_price,
                price_precision=price_precision,
                market_cap_usd=market_info.get('market_cap', {}).get('usd'),
                market_cap_rank=market_data.get('market_cap_rank'),
                total_volume_usd=market_info.get('total_volume', {}).get('usd'),
                circulating_supply=market_info.get('circulating_supply'),
                total_supply=market_info.get('total_supply'),
                price_change_24h=market_info.get('price_change_24h'),
                price_change_percentage_24h=market_info.get('price_change_percentage_24h'),
                price_change_from_last=price_change_from_last,
                market_cap_change_24h=market_info.get('market_cap_change_24h'),
                market_cap_change_percentage_24h=market_info.get('market_cap_change_percentage_24h'),
                data_source="coingecko",
                last_updated=market_info.get('last_updated')
            )
            
            return usdt_data
            
        except Exception as e:
            print(f"❌ 数据处理失败: {e}")
            return None

    def save_to_database(self, usdt_data: USDTDataCreate) -> bool:
        """保存数据到数据库"""
        try:
            db = self.session_factory()
            
            # 创建数据库记录
            db_data = USDTData(
                id=str(uuid.uuid4()),
                **usdt_data.dict()
            )
            
            db.add(db_data)
            db.commit()
            db.refresh(db_data)
            
            print(f"💾 数据已保存到数据库: {db_data.id}")
            return True
            
        except Exception as e:
            print(f"❌ 数据库保存失败: {e}")
            if db:
                db.rollback()
            return False
        finally:
            if db:
                db.close()

    def display_data(self, usdt_data: USDTDataCreate):
        """显示数据"""
        timestamp_str = usdt_data.timestamp.strftime("%Y-%m-%d %H:%M:%S")
        
        print(f"\n📊 USDT市场数据 - {timestamp_str}")
        print(f"💰 当前价格: ${usdt_data.price_precision}")
        
        if usdt_data.price_change_percentage_24h:
            change_icon = "📈" if usdt_data.price_change_percentage_24h >= 0 else "📉"
            print(f"{change_icon} 24h变化: {usdt_data.price_change_percentage_24h:+.6f}%")
        
        if usdt_data.price_change_from_last and usdt_data.price_change_from_last != 0:
            change_icon = "📈" if usdt_data.price_change_from_last >= 0 else "📉"
            print(f"{change_icon} 即时变化: {usdt_data.price_change_from_last:+.8f}%")
        
        if usdt_data.market_cap_usd:
            market_cap_b = usdt_data.market_cap_usd / 1e9
            print(f"🏦 市值: ${market_cap_b:.2f}B")
        
        if usdt_data.total_volume_usd:
            volume_b = usdt_data.total_volume_usd / 1e9
            print(f"📊 24h交易量: ${volume_b:.2f}B")
        
        print(f"🔗 数据源: {usdt_data.data_source}")

    async def collect_data(self):
        """收集数据的主循环"""
        print("🚀 USDT数据收集器启动")
        
        while True:
            try:
                # 获取市场数据
                market_data = self.get_coingecko_data()
                
                if market_data:
                    # 处理数据
                    usdt_data = self.process_data(market_data)
                    
                    if usdt_data:
                        # 显示数据
                        self.display_data(usdt_data)
                        
                        # 保存到数据库
                        self.save_to_database(usdt_data)
                    else:
                        print("❌ 数据处理失败")
                else:
                    print("❌ 无法获取市场数据")
                
                # 等待30秒
                print(f"⏰ 等待30秒后继续...")
                await asyncio.sleep(30)
                
            except KeyboardInterrupt:
                print("\n👋 收集器已停止")
                break
            except Exception as e:
                print(f"❌ 收集过程中出错: {e}")
                await asyncio.sleep(10)  # 出错后等待10秒再重试


async def main():
    """主函数"""
    collector = USDTDataCollector()
    await collector.collect_data()


if __name__ == "__main__":
    asyncio.run(main())
