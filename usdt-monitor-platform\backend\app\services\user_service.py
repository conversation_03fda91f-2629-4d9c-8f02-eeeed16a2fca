import uuid
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.models.user import User, Role, Permission
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import get_password_hash


class UserService:
    def get_user_by_id(self, db: Session, user_id: str) -> Optional[User]:
        """根据ID获取用户"""
        return db.query(User).filter(User.id == user_id).first()

    def get_user_by_username(self, db: Session, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        return db.query(User).filter(User.username == username).first()

    def get_user_by_email(self, db: Session, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        return db.query(User).filter(User.email == email).first()

    def get_users(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        role_id: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> tuple[List[User], int]:
        """获取用户列表"""
        query = db.query(User)
        
        # 搜索过滤
        if search:
            query = query.filter(
                or_(
                    User.username.ilike(f"%{search}%"),
                    User.email.ilike(f"%{search}%"),
                    User.full_name.ilike(f"%{search}%")
                )
            )
        
        # 角色过滤
        if role_id:
            query = query.join(User.roles).filter(Role.id == role_id)
        
        # 状态过滤
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
        
        total = query.count()
        users = query.offset(skip).limit(limit).all()
        
        return users, total

    def create_user(self, db: Session, user_create: UserCreate) -> User:
        """创建用户"""
        # 检查用户名和邮箱是否已存在
        if self.get_user_by_username(db, user_create.username):
            raise ValueError("用户名已存在")
        if self.get_user_by_email(db, user_create.email):
            raise ValueError("邮箱已存在")
        
        # 创建用户
        db_user = User(
            id=str(uuid.uuid4()),
            username=user_create.username,
            email=user_create.email,
            full_name=user_create.full_name,
            hashed_password=get_password_hash(user_create.password),
            is_active=user_create.is_active,
        )
        
        # 添加角色
        if user_create.role_ids:
            roles = db.query(Role).filter(Role.id.in_(user_create.role_ids)).all()
            db_user.roles = roles
        
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        return db_user

    def update_user(self, db: Session, user_id: str, user_update: UserUpdate) -> Optional[User]:
        """更新用户"""
        db_user = self.get_user_by_id(db, user_id)
        if not db_user:
            return None
        
        # 检查用户名和邮箱冲突
        if user_update.username and user_update.username != db_user.username:
            if self.get_user_by_username(db, user_update.username):
                raise ValueError("用户名已存在")
            db_user.username = user_update.username
        
        if user_update.email and user_update.email != db_user.email:
            if self.get_user_by_email(db, user_update.email):
                raise ValueError("邮箱已存在")
            db_user.email = user_update.email
        
        # 更新其他字段
        if user_update.full_name is not None:
            db_user.full_name = user_update.full_name
        if user_update.is_active is not None:
            db_user.is_active = user_update.is_active
        
        # 更新角色
        if user_update.role_ids is not None:
            roles = db.query(Role).filter(Role.id.in_(user_update.role_ids)).all()
            db_user.roles = roles
        
        db.commit()
        db.refresh(db_user)
        return db_user

    def delete_user(self, db: Session, user_id: str) -> bool:
        """删除用户"""
        db_user = self.get_user_by_id(db, user_id)
        if not db_user:
            return False
        
        db.delete(db_user)
        db.commit()
        return True

    def activate_user(self, db: Session, user_id: str) -> Optional[User]:
        """激活用户"""
        db_user = self.get_user_by_id(db, user_id)
        if not db_user:
            return None
        
        db_user.is_active = True
        db.commit()
        db.refresh(db_user)
        return db_user

    def deactivate_user(self, db: Session, user_id: str) -> Optional[User]:
        """停用用户"""
        db_user = self.get_user_by_id(db, user_id)
        if not db_user:
            return None
        
        db_user.is_active = False
        db.commit()
        db.refresh(db_user)
        return db_user

    # 角色管理
    def get_roles(self, db: Session) -> List[Role]:
        """获取所有角色"""
        return db.query(Role).filter(Role.is_active == True).all()

    def get_role_by_id(self, db: Session, role_id: str) -> Optional[Role]:
        """根据ID获取角色"""
        return db.query(Role).filter(Role.id == role_id).first()

    def create_role(self, db: Session, name: str, description: str = None, permission_ids: List[str] = None) -> Role:
        """创建角色"""
        # 检查角色名是否已存在
        if db.query(Role).filter(Role.name == name).first():
            raise ValueError("角色名已存在")
        
        db_role = Role(
            id=str(uuid.uuid4()),
            name=name,
            description=description,
        )
        
        # 添加权限
        if permission_ids:
            permissions = db.query(Permission).filter(Permission.id.in_(permission_ids)).all()
            db_role.permissions = permissions
        
        db.add(db_role)
        db.commit()
        db.refresh(db_role)
        return db_role

    # 权限管理
    def get_permissions(self, db: Session) -> List[Permission]:
        """获取所有权限"""
        return db.query(Permission).all()

    def get_user_permissions(self, db: Session, user_id: str) -> List[Permission]:
        """获取用户权限"""
        user = self.get_user_by_id(db, user_id)
        if not user:
            return []
        
        permissions = []
        for role in user.roles:
            permissions.extend(role.permissions)
        
        # 去重
        unique_permissions = {}
        for perm in permissions:
            unique_permissions[perm.id] = perm
        
        return list(unique_permissions.values())


user_service = UserService()
