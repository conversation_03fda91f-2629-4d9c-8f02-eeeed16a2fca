"""
USDT Dashboard Flask应用
"""
import os
import logging
from flask import Flask, render_template, jsonify
from flask_socketio import Socket<PERSON>, emit
import threading
import time
from datetime import datetime

from config import config
from data_service import USDTDataService

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_app(config_name='default'):
    """应用工厂函数"""
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化SocketIO
    socketio = SocketIO(app, cors_allowed_origins="*", async_mode=app.config['SOCKETIO_ASYNC_MODE'])
    
    # 初始化数据服务
    data_service = USDTDataService(app.config['DATA_FILE_PATH'])
    
    @app.route('/')
    def dashboard():
        """仪表板主页"""
        return render_template('dashboard.html', 
                             title=app.config['DASHBOARD_TITLE'],
                             update_interval=app.config['UPDATE_INTERVAL'])
    
    @app.route('/api/data')
    def get_data():
        """获取数据API"""
        try:
            data = data_service.get_dashboard_data()
            return jsonify({
                'success': True,
                'data': data,
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            logger.error(f"获取数据失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/history/<int:hours>')
    def get_history(hours):
        """获取历史数据API"""
        try:
            data = data_service.get_recent_data(hours)
            return jsonify({
                'success': True,
                'data': data,
                'count': len(data)
            })
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @socketio.on('connect')
    def handle_connect():
        """WebSocket连接处理"""
        logger.info('客户端已连接')
        # 发送初始数据
        try:
            data = data_service.get_dashboard_data()
            emit('data_update', data)
        except Exception as e:
            logger.error(f"发送初始数据失败: {e}")
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """WebSocket断开处理"""
        logger.info('客户端已断开')
    
    @socketio.on('request_data')
    def handle_data_request():
        """处理数据请求"""
        try:
            data = data_service.get_dashboard_data()
            emit('data_update', data)
        except Exception as e:
            logger.error(f"处理数据请求失败: {e}")
    
    def background_data_updater():
        """后台数据更新线程"""
        while True:
            try:
                time.sleep(app.config['UPDATE_INTERVAL'])
                data = data_service.get_dashboard_data()
                socketio.emit('data_update', data, broadcast=True)
                logger.info('数据已广播更新')
            except Exception as e:
                logger.error(f"后台更新失败: {e}")
    
    # 启动后台更新线程
    def start_background_updater():
        thread = threading.Thread(target=background_data_updater, daemon=True)
        thread.start()
        logger.info('后台数据更新线程已启动')
    
    # 注册应用和socketio到全局
    app.socketio = socketio
    app.data_service = data_service
    app.start_background_updater = start_background_updater
    
    return app

if __name__ == '__main__':
    app = create_app('development')
    app.start_background_updater()
    app.socketio.run(app, host='0.0.0.0', port=5000, debug=True)
