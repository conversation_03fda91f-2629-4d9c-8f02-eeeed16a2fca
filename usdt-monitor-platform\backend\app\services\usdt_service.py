import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, func
import statistics

from app.models.usdt_data import USDTData
from app.schemas.usdt_data import USDTDataCreate, USDTStats


class USDTService:
    def get_current_data(self, db: Session) -> Optional[USDTData]:
        """获取最新的USDT数据"""
        return db.query(USDTData).order_by(desc(USDTData.timestamp)).first()

    def get_data_by_id(self, db: Session, data_id: str) -> Optional[USDTData]:
        """根据ID获取USDT数据"""
        return db.query(USDTData).filter(USDTData.id == data_id).first()

    def get_historical_data(
        self,
        db: Session,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: Optional[int] = None,
        skip: int = 0
    ) -> <PERSON><PERSON>[List[USDTData], int]:
        """获取历史数据"""
        query = db.query(USDTData)
        
        # 时间范围过滤
        if start_date:
            query = query.filter(USDTData.timestamp >= start_date)
        if end_date:
            query = query.filter(USDTData.timestamp <= end_date)
        
        # 按时间倒序排列
        query = query.order_by(desc(USDTData.timestamp))
        
        total = query.count()
        
        # 分页
        if skip:
            query = query.offset(skip)
        if limit:
            query = query.limit(limit)
        
        data = query.all()
        return data, total

    def create_data(self, db: Session, data_create: USDTDataCreate) -> USDTData:
        """创建USDT数据记录"""
        db_data = USDTData(
            id=str(uuid.uuid4()),
            **data_create.dict()
        )
        
        db.add(db_data)
        db.commit()
        db.refresh(db_data)
        return db_data

    def get_stats(self, db: Session) -> USDTStats:
        """获取统计数据"""
        # 获取最新数据
        latest = self.get_current_data(db)
        
        # 获取24小时内的数据
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=24)
        
        price_history, _ = self.get_historical_data(
            db, start_date=start_time, end_date=end_time, limit=1000
        )
        
        # 计算统计指标
        volatility_24h = 0.0
        average_price_24h = 0.0
        max_price_24h = 0.0
        min_price_24h = 0.0
        
        if price_history:
            prices = [data.current_price_usd for data in price_history]
            average_price_24h = statistics.mean(prices)
            max_price_24h = max(prices)
            min_price_24h = min(prices)
            
            # 计算波动率（标准差）
            if len(prices) > 1:
                volatility_24h = statistics.stdev(prices)
        
        # 获取总记录数
        total_records = db.query(USDTData).count()
        
        return USDTStats(
            latest=latest,
            price_history=price_history[:100],  # 返回最近100条记录
            volatility_24h=volatility_24h,
            average_price_24h=average_price_24h,
            max_price_24h=max_price_24h,
            min_price_24h=min_price_24h,
            total_records=total_records
        )

    def get_price_history(
        self,
        db: Session,
        period: str = "24h",
        interval: str = "1h"
    ) -> List[USDTData]:
        """获取价格历史数据"""
        end_time = datetime.utcnow()
        
        # 根据周期确定开始时间
        if period == "1h":
            start_time = end_time - timedelta(hours=1)
        elif period == "24h":
            start_time = end_time - timedelta(hours=24)
        elif period == "7d":
            start_time = end_time - timedelta(days=7)
        elif period == "30d":
            start_time = end_time - timedelta(days=30)
        else:
            start_time = end_time - timedelta(hours=24)
        
        # 根据间隔确定采样数量
        if interval == "1m":
            limit = 60 if period == "1h" else 1440
        elif interval == "5m":
            limit = 12 if period == "1h" else 288
        elif interval == "15m":
            limit = 4 if period == "1h" else 96
        elif interval == "1h":
            limit = 1 if period == "1h" else 24
        elif interval == "1d":
            limit = 30 if period == "30d" else 7
        else:
            limit = 100
        
        data, _ = self.get_historical_data(
            db, start_date=start_time, end_date=end_time, limit=limit
        )
        
        return data

    def get_volatility_data(self, db: Session, period: str = "24h") -> dict:
        """获取波动率数据"""
        end_time = datetime.utcnow()
        
        if period == "24h":
            start_time = end_time - timedelta(hours=24)
        elif period == "7d":
            start_time = end_time - timedelta(days=7)
        elif period == "30d":
            start_time = end_time - timedelta(days=30)
        else:
            start_time = end_time - timedelta(hours=24)
        
        data, _ = self.get_historical_data(
            db, start_date=start_time, end_date=end_time, limit=1000
        )
        
        if not data:
            return {
                "volatility": 0.0,
                "max_price": 0.0,
                "min_price": 0.0,
                "average_price": 0.0,
                "price_range": 0.0
            }
        
        prices = [item.current_price_usd for item in data]
        
        volatility = statistics.stdev(prices) if len(prices) > 1 else 0.0
        max_price = max(prices)
        min_price = min(prices)
        average_price = statistics.mean(prices)
        price_range = max_price - min_price
        
        return {
            "volatility": volatility,
            "max_price": max_price,
            "min_price": min_price,
            "average_price": average_price,
            "price_range": price_range
        }

    def cleanup_old_data(self, db: Session, retention_days: int = 30) -> int:
        """清理旧数据"""
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        
        deleted_count = db.query(USDTData).filter(
            USDTData.timestamp < cutoff_date
        ).delete()
        
        db.commit()
        return deleted_count

    def get_data_sources_status(self, db: Session) -> dict:
        """获取数据源状态"""
        # 获取最近1小时的数据源统计
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=1)
        
        sources_data = db.query(
            USDTData.data_source,
            func.count(USDTData.id).label('count'),
            func.max(USDTData.timestamp).label('last_update')
        ).filter(
            USDTData.timestamp >= start_time
        ).group_by(USDTData.data_source).all()
        
        sources = []
        for source_data in sources_data:
            source_name = source_data.data_source or "unknown"
            count = source_data.count
            last_update = source_data.last_update
            
            # 判断状态
            if last_update and (datetime.utcnow() - last_update).seconds < 300:  # 5分钟内
                status = "active"
            else:
                status = "inactive"
            
            # 计算可靠性（基于数据频率）
            reliability = min(100, (count / 60) * 100)  # 假设每分钟1条数据为100%
            
            sources.append({
                "name": source_name,
                "status": status,
                "last_update": last_update.isoformat() if last_update else None,
                "reliability": reliability
            })
        
        return {"sources": sources}


usdt_service = USDTService()
