from sqlalchemy import Column, DateTime, Float, Integer, String, Text, BigInteger
from sqlalchemy.sql import func

from app.core.database import Base


class USDTData(Base):
    __tablename__ = "usdt_data"

    id = Column(String, primary_key=True, index=True)
    
    # 时间戳
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    unix_timestamp = Column(BigInteger, nullable=False, index=True)
    
    # 基本信息
    name = Column(String, default="Tether")
    symbol = Column(String, default="USDT")
    
    # 价格信息
    current_price_usd = Column(Float, nullable=False)
    price_precision = Column(String, nullable=True)  # 高精度价格字符串
    
    # 市场数据
    market_cap_usd = Column(BigInteger, nullable=True)
    market_cap_rank = Column(Integer, nullable=True)
    total_volume_usd = Column(BigInteger, nullable=True)
    circulating_supply = Column(Float, nullable=True)
    total_supply = Column(Float, nullable=True)
    
    # 价格变化
    price_change_24h = Column(Float, nullable=True)
    price_change_percentage_24h = Column(Float, nullable=True)
    price_change_from_last = Column(Float, nullable=True)
    
    # 市值变化
    market_cap_change_24h = Column(BigInteger, nullable=True)
    market_cap_change_percentage_24h = Column(Float, nullable=True)
    
    # 数据源信息
    data_source = Column(String, nullable=True)  # 'coingecko', 'coinbase', 'binance'
    last_updated = Column(String, nullable=True)  # API返回的最后更新时间
    
    # 系统时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    def __repr__(self):
        return f"<USDTData(id={self.id}, price={self.current_price_usd}, timestamp={self.timestamp})>"
