import uuid
from datetime import datetime, timedelta
from typing import Optional
from sqlalchemy.orm import Session

from app.models.user import User
from app.schemas.auth import LoginRequest, RegisterRequest
from app.schemas.user import UserCreate
from app.core.security import (
    verify_password,
    get_password_hash,
    create_access_token,
    create_refresh_token,
    verify_refresh_token
)
from app.core.config import settings
from app.services.user_service import user_service


class AuthService:
    def authenticate_user(self, db: Session, username: str, password: str) -> Optional[User]:
        """验证用户凭据"""
        user = user_service.get_user_by_username(db, username)
        if not user:
            user = user_service.get_user_by_email(db, username)
        
        if not user or not verify_password(password, user.hashed_password):
            return None
        
        if not user.is_active:
            raise ValueError("用户账户已被禁用")
        
        return user

    def login(self, db: Session, login_data: LoginRequest) -> dict:
        """用户登录"""
        user = self.authenticate_user(db, login_data.username, login_data.password)
        if not user:
            raise ValueError("用户名或密码错误")
        
        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        db.commit()
        
        # 生成令牌
        access_token = create_access_token(subject=user.id)
        refresh_token = create_refresh_token(subject=user.id)
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user": user
        }

    def register(self, db: Session, register_data: RegisterRequest) -> dict:
        """用户注册"""
        # 验证密码确认
        if register_data.password != register_data.confirm_password:
            raise ValueError("密码确认不匹配")
        
        # 检查用户名和邮箱是否已存在
        if user_service.get_user_by_username(db, register_data.username):
            raise ValueError("用户名已存在")
        if user_service.get_user_by_email(db, register_data.email):
            raise ValueError("邮箱已存在")
        
        # 创建用户
        user_create = UserCreate(
            username=register_data.username,
            email=register_data.email,
            password=register_data.password,
            full_name=register_data.full_name,
            role_ids=[]  # 默认无角色，需要管理员分配
        )
        
        user = user_service.create_user(db, user_create)
        
        # 生成令牌
        access_token = create_access_token(subject=user.id)
        refresh_token = create_refresh_token(subject=user.id)
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user": user
        }

    def refresh_token(self, db: Session, refresh_token: str) -> dict:
        """刷新访问令牌"""
        user_id = verify_refresh_token(refresh_token)
        if not user_id:
            raise ValueError("无效的刷新令牌")
        
        user = user_service.get_user_by_id(db, user_id)
        if not user or not user.is_active:
            raise ValueError("用户不存在或已被禁用")
        
        # 生成新的令牌
        access_token = create_access_token(subject=user.id)
        new_refresh_token = create_refresh_token(subject=user.id)
        
        return {
            "access_token": access_token,
            "refresh_token": new_refresh_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user": user
        }

    def change_password(self, db: Session, user_id: str, current_password: str, new_password: str) -> bool:
        """修改密码"""
        user = user_service.get_user_by_id(db, user_id)
        if not user:
            raise ValueError("用户不存在")
        
        if not verify_password(current_password, user.hashed_password):
            raise ValueError("当前密码错误")
        
        user.hashed_password = get_password_hash(new_password)
        db.commit()
        return True

    def check_username_availability(self, db: Session, username: str) -> bool:
        """检查用户名是否可用"""
        return user_service.get_user_by_username(db, username) is None

    def check_email_availability(self, db: Session, email: str) -> bool:
        """检查邮箱是否可用"""
        return user_service.get_user_by_email(db, email) is None


auth_service = AuthService()
