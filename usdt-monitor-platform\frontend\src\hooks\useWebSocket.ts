import { useEffect, useRef } from 'react'
import { io, Socket } from 'socket.io-client'
import { WebSocketMessage } from '@/types'

interface UseWebSocketOptions {
  onMessage?: (data: WebSocketMessage) => void
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (error: any) => void
}

export const useWebSocket = (options: UseWebSocketOptions) => {
  const socketRef = useRef<Socket | null>(null)
  const { onMessage, onConnect, onDisconnect, onError } = options

  useEffect(() => {
    // 创建WebSocket连接
    const socket = io(import.meta.env.VITE_WS_URL || 'http://localhost:8000', {
      transports: ['websocket'],
      autoConnect: true,
    })

    socketRef.current = socket

    // 连接事件
    socket.on('connect', () => {
      console.log('WebSocket connected')
      onConnect?.()
    })

    socket.on('disconnect', () => {
      console.log('WebSocket disconnected')
      onDisconnect?.()
    })

    socket.on('error', (error) => {
      console.error('WebSocket error:', error)
      onError?.(error)
    })

    // 监听消息
    socket.on('usdt_update', (data) => {
      onMessage?.({
        type: 'usdt_update',
        data,
        timestamp: new Date().toISOString(),
      })
    })

    socket.on('alert', (data) => {
      onMessage?.({
        type: 'alert',
        data,
        timestamp: new Date().toISOString(),
      })
    })

    socket.on('system_status', (data) => {
      onMessage?.({
        type: 'system_status',
        data,
        timestamp: new Date().toISOString(),
      })
    })

    // 清理函数
    return () => {
      socket.disconnect()
    }
  }, [onMessage, onConnect, onDisconnect, onError])

  // 发送消息的方法
  const sendMessage = (event: string, data: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(event, data)
    }
  }

  return {
    socket: socketRef.current,
    sendMessage,
  }
}
