import React, { useEffect, useState } from 'react'
import { Row, Col, Card, Statistic, Typography, Button, Spin, message } from 'antd'
import { DollarOutlined, ReloadOutlined } from '@ant-design/icons'
import { usdtService } from '@/services/usdtService'
import { USDTData } from '@/types'

const { Title } = Typography

const DashboardPage: React.FC = () => {
  const [currentData, setCurrentData] = useState<USDTData | null>(null)
  const [loading, setLoading] = useState(false)

  const fetchCurrentData = async (showMessage = false) => {
    setLoading(true)
    try {
      const data = await usdtService.getCurrentData()
      setCurrentData(data)
      if (showMessage) {
        message.success('数据更新成功')
      }
    } catch (error: any) {
      if (showMessage) {
        message.error('获取数据失败: ' + (error.message || '未知错误'))
      }
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCurrentData()
  }, [])

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2}>USDT 监控仪表板</Title>
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          loading={loading}
          onClick={() => fetchCurrentData(true)}
        >
          刷新数据
        </Button>
      </div>

      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="当前价格"
              value={currentData?.current_price_usd || 0}
              precision={6}
              prefix={<DollarOutlined />}
              suffix="USD"
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="24h变化"
              value={currentData?.price_change_percentage_24h || 0}
              precision={4}
              suffix="%"
              valueStyle={{
                color: (currentData?.price_change_percentage_24h || 0) >= 0 ? '#3f8600' : '#cf1322'
              }}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="市值"
              value={currentData?.market_cap_usd ? currentData.market_cap_usd / 1e9 : 0}
              precision={2}
              suffix="B USD"
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="24h交易量"
              value={currentData?.total_volume_usd ? currentData.total_volume_usd / 1e9 : 0}
              precision={2}
              suffix="B USD"
            />
          </Card>
        </Col>
      </Row>

      {currentData && (
        <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
          <Col span={24}>
            <Card title="详细信息">
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <p><strong>名称:</strong> {currentData.name}</p>
                  <p><strong>符号:</strong> {currentData.symbol}</p>
                  <p><strong>市值排名:</strong> #{currentData.market_cap_rank}</p>
                </Col>
                <Col span={8}>
                  <p><strong>流通供应量:</strong> {currentData.circulating_supply?.toLocaleString()}</p>
                  <p><strong>总供应量:</strong> {currentData.total_supply?.toLocaleString()}</p>
                  <p><strong>数据源:</strong> {currentData.data_source}</p>
                </Col>
                <Col span={8}>
                  <p><strong>最后更新:</strong> {new Date(currentData.timestamp).toLocaleString()}</p>
                  <p><strong>精确价格:</strong> ${currentData.price_precision}</p>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      )}

      {loading && (
        <div style={{ textAlign: 'center', marginTop: '50px' }}>
          <Spin size="large" />
        </div>
      )}
    </div>
  )
}

export default DashboardPage