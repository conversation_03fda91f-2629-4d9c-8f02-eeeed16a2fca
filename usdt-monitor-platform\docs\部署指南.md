# USDT监控平台部署指南

## 1. 环境准备

### 1.1 系统要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Windows 10+ / macOS 10.15+
- **Python**: 3.8+
- **Node.js**: 16+
- **内存**: 最低2GB，推荐4GB+
- **存储**: 最低10GB可用空间
- **网络**: 稳定的互联网连接

### 1.2 依赖软件安装

#### 1.2.1 Ubuntu/Debian系统
```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装Python和pip
sudo apt install python3 python3-pip python3-venv -y

# 安装Node.js和npm
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install nodejs -y

# 安装Git
sudo apt install git -y

# 安装PostgreSQL (生产环境)
sudo apt install postgresql postgresql-contrib -y
```

#### 1.2.2 CentOS/RHEL系统
```bash
# 更新系统包
sudo yum update -y

# 安装Python和pip
sudo yum install python3 python3-pip -y

# 安装Node.js和npm
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install nodejs -y

# 安装Git
sudo yum install git -y

# 安装PostgreSQL (生产环境)
sudo yum install postgresql postgresql-server postgresql-contrib -y
```

#### 1.2.3 Windows系统
1. 下载并安装Python 3.8+ (https://python.org)
2. 下载并安装Node.js 16+ (https://nodejs.org)
3. 下载并安装Git (https://git-scm.com)
4. 安装PostgreSQL (可选，生产环境使用)

## 2. 源码获取

### 2.1 克隆项目
```bash
# 克隆项目到本地
git clone https://github.com/your-repo/usdt-monitor-platform.git
cd usdt-monitor-platform
```

### 2.2 项目结构
```
usdt-monitor-platform/
├── backend/                 # 后端代码
│   ├── app/                # 应用代码
│   ├── requirements.txt    # Python依赖
│   └── .env               # 环境配置
├── frontend/               # 前端代码
│   ├── src/               # 源代码
│   ├── package.json       # Node.js依赖
│   └── vite.config.ts     # 构建配置
├── data-collector/         # 数据收集器
│   └── collector.py       # 收集器脚本
├── docs/                  # 文档
└── docker-compose.yml     # Docker配置
```

## 3. 开发环境部署

### 3.1 后端部署

#### 3.1.1 创建虚拟环境
```bash
cd backend
python3 -m venv venv

# 激活虚拟环境
# Linux/macOS:
source venv/bin/activate
# Windows:
venv\Scripts\activate
```

#### 3.1.2 安装依赖
```bash
pip install -r requirements.txt
```

#### 3.1.3 配置环境变量
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

配置内容示例:
```env
# 应用配置
PROJECT_NAME=USDT监控平台
VERSION=1.0.0
DEBUG=true
HOST=0.0.0.0
PORT=8000

# 安全配置
SECRET_KEY=your-super-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# 数据库配置
DATABASE_URL=sqlite:///./usdt_monitor.db

# CORS配置
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:5173"]

# 超级用户配置
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=admin123
```

#### 3.1.4 初始化数据库
```bash
python -m app.utils.init_db
```

#### 3.1.5 启动后端服务
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 3.2 前端部署

#### 3.2.1 安装依赖
```bash
cd frontend
npm install
```

#### 3.2.2 配置环境变量
```bash
# 创建环境配置文件
echo "VITE_API_BASE_URL=http://localhost:8000/api/v1" > .env.local
```

#### 3.2.3 启动前端服务
```bash
npm run dev
```

### 3.3 数据收集器部署

#### 3.3.1 启动数据收集器
```bash
cd data-collector
python collector.py
```

### 3.4 验证部署
1. 访问前端: http://localhost:5173
2. 访问API文档: http://localhost:8000/api/v1/docs
3. 使用默认账户登录测试功能

## 4. 生产环境部署

### 4.1 数据库配置

#### 4.1.1 PostgreSQL安装和配置
```bash
# 安装PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# 启动PostgreSQL服务
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 创建数据库和用户
sudo -u postgres psql
```

在PostgreSQL命令行中执行:
```sql
CREATE DATABASE usdt_monitor;
CREATE USER usdt_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE usdt_monitor TO usdt_user;
\q
```

#### 4.1.2 更新数据库配置
```env
DATABASE_URL=***********************************************************
```

### 4.2 使用Docker部署

#### 4.2.1 安装Docker
```bash
# Ubuntu系统
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 4.2.2 Docker配置文件
创建 `docker-compose.yml`:
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: usdt_monitor
      POSTGRES_USER: usdt_user
      POSTGRES_PASSWORD: your_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  backend:
    build: ./backend
    environment:
      DATABASE_URL: **********************************************************
      SECRET_KEY: your-production-secret-key
      DEBUG: false
    ports:
      - "8000:8000"
    depends_on:
      - postgres
    volumes:
      - ./backend:/app

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend

  data-collector:
    build: ./data-collector
    depends_on:
      - postgres
    volumes:
      - ./data-collector:/app

volumes:
  postgres_data:
```

#### 4.2.3 创建Dockerfile

**后端Dockerfile** (`backend/Dockerfile`):
```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

**前端Dockerfile** (`frontend/Dockerfile`):
```dockerfile
FROM node:18-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### 4.2.4 启动服务
```bash
# 构建和启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 4.3 Nginx反向代理配置

#### 4.3.1 安装Nginx
```bash
sudo apt install nginx -y
```

#### 4.3.2 配置Nginx
创建配置文件 `/etc/nginx/sites-available/usdt-monitor`:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        proxy_pass http://localhost:5173;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 后端API
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 4.3.3 启用配置
```bash
sudo ln -s /etc/nginx/sites-available/usdt-monitor /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 4.4 SSL证书配置

#### 4.4.1 使用Let's Encrypt
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行:
0 12 * * * /usr/bin/certbot renew --quiet
```

## 5. 系统服务配置

### 5.1 创建系统服务

#### 5.1.1 后端服务
创建 `/etc/systemd/system/usdt-backend.service`:
```ini
[Unit]
Description=USDT Monitor Backend
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/usdt-monitor-platform/backend
Environment=PATH=/path/to/usdt-monitor-platform/backend/venv/bin
ExecStart=/path/to/usdt-monitor-platform/backend/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000
Restart=always

[Install]
WantedBy=multi-user.target
```

#### 5.1.2 数据收集器服务
创建 `/etc/systemd/system/usdt-collector.service`:
```ini
[Unit]
Description=USDT Data Collector
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/usdt-monitor-platform/data-collector
Environment=PATH=/path/to/usdt-monitor-platform/backend/venv/bin
ExecStart=/path/to/usdt-monitor-platform/backend/venv/bin/python collector.py
Restart=always

[Install]
WantedBy=multi-user.target
```

#### 5.1.3 启用服务
```bash
sudo systemctl daemon-reload
sudo systemctl enable usdt-backend
sudo systemctl enable usdt-collector
sudo systemctl start usdt-backend
sudo systemctl start usdt-collector
```

## 6. 监控和维护

### 6.1 日志管理
```bash
# 查看后端日志
sudo journalctl -u usdt-backend -f

# 查看数据收集器日志
sudo journalctl -u usdt-collector -f

# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 6.2 备份策略
```bash
# 创建备份脚本
cat > /usr/local/bin/backup-usdt.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/backup/usdt-monitor"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
pg_dump usdt_monitor > $BACKUP_DIR/database_$DATE.sql

# 备份配置文件
cp /path/to/usdt-monitor-platform/backend/.env $BACKUP_DIR/env_$DATE.backup

# 清理7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.backup" -mtime +7 -delete
EOF

chmod +x /usr/local/bin/backup-usdt.sh

# 设置定时备份
echo "0 2 * * * /usr/local/bin/backup-usdt.sh" | sudo crontab -
```

### 6.3 性能监控
```bash
# 安装监控工具
sudo apt install htop iotop nethogs -y

# 监控系统资源
htop

# 监控网络连接
sudo netstat -tulpn | grep :8000
```

## 7. 故障排除

### 7.1 常见问题

#### 7.1.1 服务无法启动
```bash
# 检查服务状态
sudo systemctl status usdt-backend
sudo systemctl status usdt-collector

# 查看详细错误信息
sudo journalctl -u usdt-backend --no-pager
```

#### 7.1.2 数据库连接问题
```bash
# 检查PostgreSQL状态
sudo systemctl status postgresql

# 测试数据库连接
psql -h localhost -U usdt_user -d usdt_monitor
```

#### 7.1.3 端口占用问题
```bash
# 检查端口占用
sudo netstat -tulpn | grep :8000
sudo lsof -i :8000

# 杀死占用进程
sudo kill -9 <PID>
```

### 7.2 性能优化

#### 7.2.1 数据库优化
```sql
-- 创建索引
CREATE INDEX IF NOT EXISTS idx_usdt_data_timestamp ON usdt_data(timestamp);
CREATE INDEX IF NOT EXISTS idx_usdt_data_created_at ON usdt_data(created_at);

-- 分析表统计信息
ANALYZE usdt_data;
```

#### 7.2.2 应用优化
- 启用数据库连接池
- 配置适当的缓存策略
- 优化查询语句
- 使用CDN加速静态资源

## 8. 安全加固

### 8.1 防火墙配置
```bash
# 安装UFW
sudo apt install ufw -y

# 配置防火墙规则
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443

# 启用防火墙
sudo ufw enable
```

### 8.2 系统安全
- 定期更新系统和依赖包
- 使用强密码和密钥认证
- 限制SSH访问
- 配置fail2ban防止暴力破解
- 定期备份重要数据

### 8.3 应用安全
- 使用HTTPS加密传输
- 配置安全的JWT密钥
- 启用CORS保护
- 输入验证和SQL注入防护
- 定期安全审计
