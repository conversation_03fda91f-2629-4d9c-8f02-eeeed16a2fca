from fastapi import APIRouter

from .auth import router as auth_router
from .users import router as users_router
from .usdt import router as usdt_router
from .system import router as system_router

api_router = APIRouter()

api_router.include_router(auth_router, prefix="/auth", tags=["认证"])
api_router.include_router(users_router, prefix="/users", tags=["用户管理"])
api_router.include_router(usdt_router, prefix="/usdt", tags=["USDT数据"])
api_router.include_router(system_router, prefix="/system", tags=["系统管理"])
