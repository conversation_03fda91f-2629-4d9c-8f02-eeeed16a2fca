/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 布局样式 */
.app-layout {
  min-height: 100vh;
}

.app-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.app-logo {
  font-size: 20px;
  font-weight: bold;
  color: #1890ff;
}

.app-content {
  padding: 24px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
}

/* 卡片样式 */
.dashboard-card {
  margin-bottom: 24px;
}

.price-display {
  font-size: 32px;
  font-weight: bold;
  color: #1890ff;
  text-align: center;
}

.price-change {
  font-size: 16px;
  margin-top: 8px;
}

.price-change.positive {
  color: #52c41a;
}

.price-change.negative {
  color: #ff4d4f;
}

/* 表格样式 */
.data-table {
  background: #fff;
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-content {
    padding: 16px;
  }
  
  .price-display {
    font-size: 24px;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 错误状态 */
.error-container {
  text-align: center;
  padding: 40px;
  color: #ff4d4f;
}
