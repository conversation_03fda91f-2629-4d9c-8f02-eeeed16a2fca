import React, { useEffect, useState } from 'react'
import { Row, Col, Card, Statistic, Typography, Space, Tag, Button, Spin, Alert } from 'antd'
import { 
  DollarOutlined, 
  TrendingUpOutlined, 
  TrendingDownOutlined,
  ReloadOutlined,
  DownloadOutlined 
} from '@ant-design/icons'
import { useDispatch, useSelector } from 'react-redux'
import { RootState, AppDispatch } from '@/store'
import { 
  fetchCurrentData, 
  fetchStats, 
  updateCurrentData,
  setConnectionStatus 
} from '@/store/slices/usdtSlice'
import PriceChart from '@/components/Dashboard/PriceChart'
import DataTable from '@/components/Dashboard/DataTable'
import { useWebSocket } from '@/hooks/useWebSocket'

const { Title, Text } = Typography

const DashboardPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>()
  const { 
    currentData, 
    stats, 
    loading, 
    error, 
    isConnected, 
    lastUpdate 
  } = useSelector((state: RootState) => state.usdt)

  const [autoRefresh, setAutoRefresh] = useState(true)

  // WebSocket连接
  useWebSocket({
    onMessage: (data) => {
      if (data.type === 'usdt_update') {
        dispatch(updateCurrentData(data.data))
      }
    },
    onConnect: () => dispatch(setConnectionStatus(true)),
    onDisconnect: () => dispatch(setConnectionStatus(false)),
  })

  useEffect(() => {
    // 初始加载数据
    dispatch(fetchCurrentData())
    dispatch(fetchStats())

    // 自动刷新
    let interval: NodeJS.Timeout
    if (autoRefresh) {
      interval = setInterval(() => {
        dispatch(fetchCurrentData())
      }, 30000) // 30秒刷新一次
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [dispatch, autoRefresh])

  const handleRefresh = () => {
    dispatch(fetchCurrentData())
    dispatch(fetchStats())
  }

  const formatPrice = (price: number) => {
    return price.toFixed(10)
  }

  const formatChange = (change: number) => {
    const isPositive = change >= 0
    return (
      <Space>
        {isPositive ? <TrendingUpOutlined /> : <TrendingDownOutlined />}
        <Text type={isPositive ? 'success' : 'danger'}>
          {isPositive ? '+' : ''}{change.toFixed(6)}%
        </Text>
      </Space>
    )
  }

  if (loading && !currentData) {
    return (
      <div className="loading-container">
        <Spin size="large" />
      </div>
    )
  }

  return (
    <div className="fade-in">
      {/* 页面标题 */}
      <div className="page-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={2} className="page-title">USDT监控仪表板</Title>
            <Text className="page-description">
              实时监控USDT价格变化和市场数据
            </Text>
          </div>
          <Space>
            <Tag color={isConnected ? 'green' : 'red'}>
              {isConnected ? '实时连接' : '连接断开'}
            </Tag>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleRefresh}
              loading={loading}
            >
              刷新
            </Button>
            <Button 
              type={autoRefresh ? 'primary' : 'default'}
              onClick={() => setAutoRefresh(!autoRefresh)}
            >
              {autoRefresh ? '停止自动刷新' : '开启自动刷新'}
            </Button>
          </Space>
        </div>
      </div>

      {error && (
        <Alert
          message="数据加载失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      {/* 主要统计数据 */}
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card className="dashboard-card">
            <Statistic
              title="当前价格"
              value={currentData ? formatPrice(currentData.currentPriceUsd) : 0}
              prefix="$"
              valueStyle={{ color: '#1890ff', fontSize: '24px' }}
            />
            {currentData && (
              <div style={{ marginTop: 8 }}>
                {formatChange(currentData.priceChangePercentage24h)}
              </div>
            )}
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="dashboard-card">
            <Statistic
              title="24小时变化"
              value={currentData ? currentData.priceChange24h : 0}
              prefix="$"
              precision={8}
              valueStyle={{ 
                color: currentData && currentData.priceChange24h >= 0 ? '#52c41a' : '#ff4d4f' 
              }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="dashboard-card">
            <Statistic
              title="市值"
              value={currentData ? currentData.marketCapUsd : 0}
              formatter={(value) => `$${(Number(value) / 1e9).toFixed(2)}B`}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="dashboard-card">
            <Statistic
              title="24小时交易量"
              value={currentData ? currentData.totalVolumeUsd : 0}
              formatter={(value) => `$${(Number(value) / 1e9).toFixed(2)}B`}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 价格图表和数据表格 */}
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={16}>
          <Card title="价格走势图" className="dashboard-card">
            <PriceChart />
          </Card>
        </Col>
        
        <Col xs={24} lg={8}>
          <Card title="最新数据" className="dashboard-card">
            <DataTable />
          </Card>
        </Col>
      </Row>

      {lastUpdate && (
        <div style={{ textAlign: 'center', marginTop: 24, color: '#8c8c8c' }}>
          最后更新: {new Date(lastUpdate).toLocaleString()}
        </div>
      )}
    </div>
  )
}

export default DashboardPage
