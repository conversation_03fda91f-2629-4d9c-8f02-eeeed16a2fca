import React, { useEffect } from 'react'
import {
  Card,
  Form,
  InputNumber,
  Switch,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Divider,
  message,
  Alert,
} from 'antd'
import { SaveOutlined, ReloadOutlined, UndoOutlined } from '@ant-design/icons'
import { useDispatch, useSelector } from 'react-redux'
import { RootState, AppDispatch } from '@/store'
import {
  fetchSettings,
  updateSettings,
  clearError,
} from '@/store/slices/systemSlice'

const { Title, Text } = Typography

const SystemSettingsPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>()
  const { settings, loading, error } = useSelector((state: RootState) => state.system)

  const [form] = Form.useForm()

  useEffect(() => {
    dispatch(fetchSettings())
  }, [dispatch])

  useEffect(() => {
    if (settings) {
      form.setFieldsValue(settings)
    }
  }, [settings, form])

  const handleSave = async () => {
    try {
      const values = await form.validateFields()
      await dispatch(updateSettings(values)).unwrap()
      message.success('设置保存成功')
    } catch (error: any) {
      message.error(error || '保存设置失败')
    }
  }

  const handleReset = () => {
    if (settings) {
      form.setFieldsValue(settings)
      message.info('已重置为当前保存的设置')
    }
  }

  const handleRefresh = () => {
    dispatch(fetchSettings())
  }

  return (
    <div className="fade-in">
      {/* 页面标题 */}
      <div className="page-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={2} className="page-title">系统设置</Title>
            <Text className="page-description">配置系统参数和监控选项</Text>
          </div>
          <Space>
            <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
              刷新
            </Button>
            <Button icon={<UndoOutlined />} onClick={handleReset}>
              重置
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={loading}
            >
              保存设置
            </Button>
          </Space>
        </div>
      </div>

      {error && (
        <Alert
          message="加载设置失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 24 }}
          closable
          onClose={() => dispatch(clearError())}
        />
      )}

      <Row gutter={[24, 24]}>
        {/* 监控设置 */}
        <Col xs={24} lg={12}>
          <Card title="监控设置" className="dashboard-card">
            <Form form={form} layout="vertical">
              <Form.Item
                name="monitoringInterval"
                label="监控间隔 (秒)"
                help="数据收集的时间间隔，建议5-60秒"
                rules={[
                  { required: true, message: '请输入监控间隔' },
                  { type: 'number', min: 1, max: 3600, message: '间隔必须在1-3600秒之间' },
                ]}
              >
                <InputNumber
                  min={1}
                  max={3600}
                  style={{ width: '100%' }}
                  placeholder="请输入监控间隔"
                />
              </Form.Item>

              <Form.Item
                name="alertThreshold"
                label="告警阈值 (%)"
                help="价格变化超过此百分比时触发告警"
                rules={[
                  { required: true, message: '请输入告警阈值' },
                  { type: 'number', min: 0.001, max: 100, message: '阈值必须在0.001-100%之间' },
                ]}
              >
                <InputNumber
                  min={0.001}
                  max={100}
                  step={0.001}
                  precision={3}
                  style={{ width: '100%' }}
                  placeholder="请输入告警阈值"
                />
              </Form.Item>

              <Form.Item
                name="dataRetentionDays"
                label="数据保留天数"
                help="历史数据保留的天数，超过此时间的数据将被清理"
                rules={[
                  { required: true, message: '请输入数据保留天数' },
                  { type: 'number', min: 1, max: 365, message: '保留天数必须在1-365天之间' },
                ]}
              >
                <InputNumber
                  min={1}
                  max={365}
                  style={{ width: '100%' }}
                  placeholder="请输入数据保留天数"
                />
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 通知设置 */}
        <Col xs={24} lg={12}>
          <Card title="通知设置" className="dashboard-card">
            <Form form={form} layout="vertical">
              <Form.Item
                name="enableNotifications"
                label="启用通知"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Divider />

              <Form.Item
                name="emailNotifications"
                label="邮件通知"
                valuePropName="checked"
                help="通过邮件发送告警通知"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="smsNotifications"
                label="短信通知"
                valuePropName="checked"
                help="通过短信发送重要告警"
              >
                <Switch />
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 系统信息 */}
        <Col xs={24}>
          <Card title="系统信息" className="dashboard-card">
            <Row gutter={[24, 16]}>
              <Col xs={24} sm={12} md={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                    v1.0.0
                  </div>
                  <div style={{ color: '#8c8c8c' }}>系统版本</div>
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                    运行中
                  </div>
                  <div style={{ color: '#8c8c8c' }}>系统状态</div>
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold', color: '#722ed1' }}>
                    {new Date().toLocaleDateString()}
                  </div>
                  <div style={{ color: '#8c8c8c' }}>当前日期</div>
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold', color: '#fa8c16' }}>
                    React 18
                  </div>
                  <div style={{ color: '#8c8c8c' }}>前端框架</div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default SystemSettingsPage
