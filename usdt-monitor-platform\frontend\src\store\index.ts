import { configureStore } from '@reduxjs/toolkit'
import authSlice from './slices/authSlice'
import usdtSlice from './slices/usdtSlice'
import userSlice from './slices/userSlice'
import systemSlice from './slices/systemSlice'

export const store = configureStore({
  reducer: {
    auth: authSlice,
    usdt: usdtSlice,
    user: userSlice,
    system: systemSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
