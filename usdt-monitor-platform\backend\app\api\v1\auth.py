from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.schemas.auth import (
    LoginRequest,
    RegisterRequest,
    RefreshTokenRequest,
    ChangePasswordRequest,
    Token
)
from app.schemas.user import User
from app.schemas.common import ApiResponse
from app.services.auth_service import auth_service
from app.api.deps import get_current_user

router = APIRouter()


@router.post("/login", response_model=ApiResponse[Token])
async def login(
    login_data: LoginRequest,
    db: Session = Depends(get_db)
):
    """用户登录"""
    try:
        result = auth_service.login(db, login_data)
        return ApiResponse(success=True, data=result)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败"
        )


@router.post("/register", response_model=ApiResponse[Token])
async def register(
    register_data: RegisterRequest,
    db: Session = Depends(get_db)
):
    """用户注册"""
    try:
        result = auth_service.register(db, register_data)
        return ApiResponse(success=True, data=result)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册失败"
        )


@router.post("/refresh", response_model=ApiResponse[Token])
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: Session = Depends(get_db)
):
    """刷新访问令牌"""
    try:
        result = auth_service.refresh_token(db, refresh_data.refresh_token)
        return ApiResponse(success=True, data=result)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="刷新令牌失败"
        )


@router.post("/logout", response_model=ApiResponse[dict])
async def logout(
    current_user: User = Depends(get_current_user)
):
    """用户登出"""
    # 这里可以添加令牌黑名单逻辑
    return ApiResponse(success=True, data={"message": "登出成功"})


@router.get("/me", response_model=ApiResponse[User])
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """获取当前用户信息"""
    return ApiResponse(success=True, data=current_user)


@router.post("/change-password", response_model=ApiResponse[dict])
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """修改密码"""
    try:
        auth_service.change_password(
            db,
            current_user.id,
            password_data.current_password,
            password_data.new_password
        )
        return ApiResponse(success=True, data={"message": "密码修改成功"})
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="密码修改失败"
        )


@router.get("/check-username/{username}", response_model=ApiResponse[dict])
async def check_username_availability(
    username: str,
    db: Session = Depends(get_db)
):
    """检查用户名是否可用"""
    available = auth_service.check_username_availability(db, username)
    return ApiResponse(success=True, data={"available": available})


@router.get("/check-email/{email}", response_model=ApiResponse[dict])
async def check_email_availability(
    email: str,
    db: Session = Depends(get_db)
):
    """检查邮箱是否可用"""
    available = auth_service.check_email_availability(db, email)
    return ApiResponse(success=True, data={"available": available})
