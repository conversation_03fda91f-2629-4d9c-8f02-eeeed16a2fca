"""
数据服务层 - 处理USDT监控数据
"""
import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging

logger = logging.getLogger(__name__)

class USDTDataService:
    """USDT数据服务类"""
    
    def __init__(self, data_file_path: str):
        self.data_file_path = data_file_path
        self._cache = None
        self._last_modified = None
    
    def _load_data(self) -> List[Dict]:
        """加载数据文件"""
        try:
            if not os.path.exists(self.data_file_path):
                logger.warning(f"数据文件不存在: {self.data_file_path}")
                return []
            
            # 检查文件是否被修改
            current_modified = os.path.getmtime(self.data_file_path)
            if self._cache is None or current_modified != self._last_modified:
                with open(self.data_file_path, 'r', encoding='utf-8') as f:
                    self._cache = json.load(f)
                self._last_modified = current_modified
                logger.info(f"数据已重新加载，共 {len(self._cache)} 条记录")
            
            return self._cache
        except Exception as e:
            logger.error(f"加载数据文件失败: {e}")
            return []
    
    def get_latest_data(self) -> Optional[Dict]:
        """获取最新数据"""
        data = self._load_data()
        return data[-1] if data else None
    
    def get_recent_data(self, hours: int = 24) -> List[Dict]:
        """获取最近N小时的数据"""
        data = self._load_data()
        if not data:
            return []
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_data = []
        
        for record in reversed(data):
            try:
                record_time = datetime.fromisoformat(record['timestamp'])
                if record_time >= cutoff_time:
                    recent_data.append(record)
                else:
                    break
            except (KeyError, ValueError):
                continue
        
        return list(reversed(recent_data))
    
    def get_price_history(self, limit: int = 100) -> List[Dict]:
        """获取价格历史数据"""
        data = self._load_data()
        if not data:
            return []

        # 取最后limit条记录
        recent_data = data[-limit:] if len(data) > limit else data

        price_history = []
        for record in recent_data:
            try:
                # 优先使用price_precision字段，如果没有则使用current_price_usd
                price_str = record.get('price_precision', '')
                if price_str and price_str != '1.00000000':
                    price = float(price_str)
                else:
                    # 尝试从24h变化计算更精确的价格
                    base_price = float(record.get('current_price_usd', 1.0))
                    price_change_24h = record.get('price_change_24h', 0)
                    if price_change_24h and price_change_24h != 0:
                        price = 1.0 + float(price_change_24h)
                    else:
                        price = base_price

                price_history.append({
                    'timestamp': record['timestamp'],
                    'price': price,
                    'price_precision': f"{price:.10f}",
                    'volume': record.get('total_volume_usd', 0),
                    'market_cap': record.get('market_cap_usd', 0),
                    'price_change_24h': record.get('price_change_24h', 0),
                    'data_source': record.get('data_source', 'standard')
                })
            except (KeyError, ValueError, TypeError):
                continue

        return price_history
    
    def get_statistics(self) -> Dict:
        """获取统计数据"""
        data = self._load_data()
        if not data:
            return {}

        latest = data[-1] if data else {}
        recent_24h = self.get_recent_data(24)

        # 获取高精度价格
        latest_price = self._get_precise_price(latest)

        # 计算统计指标
        stats = {
            'total_records': len(data),
            'latest_price': latest_price,
            'latest_price_precision': f"{latest_price:.10f}",
            'latest_timestamp': latest.get('timestamp', ''),
            'market_cap': latest.get('market_cap_usd', 0),
            'market_cap_rank': latest.get('market_cap_rank', 0),
            'total_volume_24h': latest.get('total_volume_usd', 0),
            'price_change_24h': latest.get('price_change_percentage_24h', 0),
            'market_cap_change_24h': latest.get('market_cap_change_percentage_24h', 0),
            'circulating_supply': latest.get('circulating_supply', 0),
            'records_24h': len(recent_24h),
            'data_source': latest.get('data_source', 'standard')
        }

        # 计算价格波动范围（使用高精度数据）
        if recent_24h:
            prices = [self._get_precise_price(r) for r in recent_24h]
            stats['price_min_24h'] = min(prices)
            stats['price_max_24h'] = max(prices)
            stats['price_avg_24h'] = sum(prices) / len(prices)
            stats['price_range_24h'] = max(prices) - min(prices)

        return stats

    def _get_precise_price(self, record: Dict) -> float:
        """从记录中获取最精确的价格"""
        # 关键修复：始终优先使用price_change_24h计算真实价格
        base_price = float(record.get('current_price_usd', 1.0))
        price_change_24h = record.get('price_change_24h', 0)

        if price_change_24h and price_change_24h != 0:
            try:
                real_price = base_price + float(price_change_24h)
                return real_price
            except (ValueError, TypeError):
                pass

        # 备用：使用price_precision字段
        price_str = record.get('price_precision', '')
        if price_str and price_str != '1.00000000':
            try:
                return float(price_str)
            except (ValueError, TypeError):
                pass

        # 最后使用基础价格
        return base_price
    
    def get_dashboard_data(self) -> Dict:
        """获取仪表板所需的完整数据"""
        return {
            'latest': self.get_latest_data(),
            'statistics': self.get_statistics(),
            'price_history': self.get_price_history(),
            'recent_data': self.get_recent_data(1)  # 最近1小时
        }
