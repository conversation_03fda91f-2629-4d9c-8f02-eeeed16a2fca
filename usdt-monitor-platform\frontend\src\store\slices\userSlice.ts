import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { User, PaginatedResponse } from '@/types'
import { userService } from '@/services/userService'

interface UserState {
  users: User[]
  currentUser: User | null
  total: number
  loading: boolean
  error: string | null
}

const initialState: UserState = {
  users: [],
  currentUser: null,
  total: 0,
  loading: false,
  error: null,
}

// 异步actions
export const fetchUsers = createAsyncThunk(
  'user/fetchUsers',
  async (params: { page?: number; pageSize?: number; search?: string }, { rejectWithValue }) => {
    try {
      return await userService.getUsers(params)
    } catch (error: any) {
      return rejectWithValue(error.message || '获取用户列表失败')
    }
  }
)

export const createUser = createAsyncThunk(
  'user/createUser',
  async (userData: Partial<User>, { rejectWithValue }) => {
    try {
      return await userService.createUser(userData)
    } catch (error: any) {
      return rejectWithValue(error.message || '创建用户失败')
    }
  }
)

export const updateUser = createAsyncThunk(
  'user/updateUser',
  async ({ id, userData }: { id: string; userData: Partial<User> }, { rejectWithValue }) => {
    try {
      return await userService.updateUser(id, userData)
    } catch (error: any) {
      return rejectWithValue(error.message || '更新用户失败')
    }
  }
)

export const deleteUser = createAsyncThunk(
  'user/deleteUser',
  async (id: string, { rejectWithValue }) => {
    try {
      await userService.deleteUser(id)
      return id
    } catch (error: any) {
      return rejectWithValue(error.message || '删除用户失败')
    }
  }
)

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setCurrentUser: (state, action: PayloadAction<User>) => {
      state.currentUser = action.payload
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch users
      .addCase(fetchUsers.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchUsers.fulfilled, (state, action: PayloadAction<PaginatedResponse<User>>) => {
        state.loading = false
        state.users = action.payload.items
        state.total = action.payload.total
        state.error = null
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Create user
      .addCase(createUser.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(createUser.fulfilled, (state, action) => {
        state.loading = false
        state.users.push(action.payload)
        state.total += 1
        state.error = null
      })
      .addCase(createUser.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Update user
      .addCase(updateUser.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateUser.fulfilled, (state, action) => {
        state.loading = false
        const index = state.users.findIndex(user => user.id === action.payload.id)
        if (index !== -1) {
          state.users[index] = action.payload
        }
        state.error = null
      })
      .addCase(updateUser.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Delete user
      .addCase(deleteUser.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteUser.fulfilled, (state, action) => {
        state.loading = false
        state.users = state.users.filter(user => user.id !== action.payload)
        state.total -= 1
        state.error = null
      })
      .addCase(deleteUser.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  },
})

export const { clearError, setCurrentUser } = userSlice.actions
export default userSlice.reducer
