# USDT市值监控程序

## 功能说明
这个程序每隔30秒自动查询泰达币（USDT）的市值信息并保存到本地文件中。

## 文件说明
- `usdt_monitor.py` - 主程序文件
- `start_usdt_monitor.bat` - Windows启动脚本
- `usdt_market_data.json` - 数据存储文件（运行后自动生成）

## 监控的数据包括
- 当前价格（美元）
- 市值（美元）
- 市值排名
- 24小时交易量
- 流通供应量
- 总供应量
- 24小时价格变化
- 24小时市值变化

## 使用方法

### 方法1：直接运行Python脚本
```bash
python usdt_monitor.py
```

### 方法2：使用批处理文件（Windows）
双击 `start_usdt_monitor.bat` 文件

### 方法3：命令行运行
```bash
cd d:\GitHubProjects\myAIProjects
python usdt_monitor.py
```

## 停止程序
按 `Ctrl + C` 组合键停止程序

## 数据文件
- 数据保存在 `usdt_market_data.json` 文件中
- 每次查询的结果都会追加到文件中
- 数据格式为JSON数组，包含时间戳和完整的市值信息

## 依赖库
程序需要以下Python库：
- `requests` - 用于HTTP请求
- `json` - 用于数据处理
- `datetime` - 用于时间戳
- `time` - 用于定时

如果缺少依赖库，请运行：
```bash
pip install requests
```

## 数据源
使用 CoinGecko API 获取数据：
- API地址：https://api.coingecko.com/api/v3/coins/tether
- 免费使用，无需API密钥
- 数据更新频率高，准确可靠

## 注意事项
1. 确保网络连接正常
2. 程序会持续运行直到手动停止
3. 数据文件会不断增长，定期清理或备份
4. 如果API请求失败，程序会自动重试
