from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.schemas.usdt_data import USDTData, USDTStats, PriceHistoryQuery
from app.schemas.common import ApiResponse, PaginatedResponse
from app.services.usdt_service import usdt_service
from app.api.deps import get_current_user

router = APIRouter()


@router.get("/current", response_model=ApiResponse[USDTData])
async def get_current_data(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取当前USDT数据"""
    data = usdt_service.get_current_data(db)
    if not data:
        raise HTTPException(status_code=404, detail="暂无数据")
    
    return ApiResponse(success=True, data=data)


@router.get("/historical", response_model=ApiResponse[PaginatedResponse[USDTData]])
async def get_historical_data(
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取历史USDT数据"""
    skip = (page - 1) * page_size
    data, total = usdt_service.get_historical_data(
        db, start_date=start_date, end_date=end_date, skip=skip, limit=page_size
    )
    
    paginated_data = PaginatedResponse.create(
        items=data,
        total=total,
        page=page,
        page_size=page_size
    )
    
    return ApiResponse(success=True, data=paginated_data)


@router.get("/stats", response_model=ApiResponse[USDTStats])
async def get_stats(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取USDT统计数据"""
    stats = usdt_service.get_stats(db)
    return ApiResponse(success=True, data=stats)


@router.get("/price-history", response_model=ApiResponse[List[USDTData]])
async def get_price_history(
    period: str = Query("24h", regex="^(1h|24h|7d|30d)$", description="时间周期"),
    interval: str = Query("1h", regex="^(1m|5m|15m|1h|1d)$", description="时间间隔"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取价格历史数据"""
    data = usdt_service.get_price_history(db, period=period, interval=interval)
    return ApiResponse(success=True, data=data)


@router.get("/volatility", response_model=ApiResponse[dict])
async def get_volatility_data(
    period: str = Query("24h", regex="^(24h|7d|30d)$", description="时间周期"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取波动率数据"""
    data = usdt_service.get_volatility_data(db, period=period)
    return ApiResponse(success=True, data=data)


@router.get("/data-sources", response_model=ApiResponse[dict])
async def get_data_sources_status(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取数据源状态"""
    data = usdt_service.get_data_sources_status(db)
    return ApiResponse(success=True, data=data)


@router.get("/health", response_model=ApiResponse[dict])
async def get_system_health(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取系统健康状态"""
    # 获取最新数据时间
    latest_data = usdt_service.get_current_data(db)
    
    # 检查数据收集器状态
    data_collector_status = "running"
    last_update = None
    records_count = 0
    
    if latest_data:
        last_update = latest_data.timestamp.isoformat()
        # 如果最新数据超过5分钟，认为数据收集器有问题
        time_diff = (datetime.utcnow() - latest_data.timestamp).total_seconds()
        if time_diff > 300:  # 5分钟
            data_collector_status = "error"
        
        # 获取记录总数
        _, records_count = usdt_service.get_historical_data(db, limit=1)
    else:
        data_collector_status = "stopped"
    
    # 数据库状态（如果能查询到数据说明数据库正常）
    database_status = "connected"
    database_response_time = 10  # 模拟响应时间
    
    # API状态
    apis = [
        {"name": "CoinGecko", "status": "online", "response_time": 150},
        {"name": "Coinbase", "status": "online", "response_time": 120},
        {"name": "Binance", "status": "online", "response_time": 100},
    ]
    
    # 整体状态
    overall_status = "healthy"
    if data_collector_status == "error":
        overall_status = "warning"
    elif data_collector_status == "stopped":
        overall_status = "error"
    
    health_data = {
        "status": overall_status,
        "data_collector": {
            "status": data_collector_status,
            "last_update": last_update,
            "records_count": records_count
        },
        "database": {
            "status": database_status,
            "response_time": database_response_time
        },
        "apis": apis
    }
    
    return ApiResponse(success=True, data=health_data)
