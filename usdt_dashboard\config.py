"""
USDT Dashboard 配置文件
"""
import os

class Config:
    """基础配置"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'usdt-dashboard-secret-key-2025'
    
    # 数据文件路径
    DATA_FILE_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'usdt_monitor', 'usdt_market_data.json')
    
    # WebSocket配置
    SOCKETIO_ASYNC_MODE = 'threading'
    
    # 更新间隔（秒）
    UPDATE_INTERVAL = 30
    
    # 图表配置
    CHART_MAX_POINTS = 100  # 图表最大显示点数
    CHART_COLORS = {
        'primary': '#007bff',
        'success': '#28a745',
        'danger': '#dc3545',
        'warning': '#ffc107',
        'info': '#17a2b8'
    }
    
    # 仪表板配置
    DASHBOARD_TITLE = 'USDT 实时监控仪表板'
    REFRESH_RATE = 1000  # 前端刷新率（毫秒）

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    
class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
