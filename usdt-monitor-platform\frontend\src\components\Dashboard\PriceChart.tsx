import React, { useEffect, useState } from 'react'
import { Select, Spin, Empty } from 'antd'
import { useDispatch, useSelector } from 'react-redux'
import { RootState, AppDispatch } from '@/store'
import { fetchHistoricalData } from '@/store/slices/usdtSlice'

const { Option } = Select

interface ChartData {
  timestamp: string
  price: number
}

const PriceChart: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>()
  const { historicalData, loading } = useSelector((state: RootState) => state.usdt)
  const [period, setPeriod] = useState<'1h' | '24h' | '7d' | '30d'>('24h')
  const [chartData, setChartData] = useState<ChartData[]>([])

  useEffect(() => {
    // 获取历史数据
    const endDate = new Date().toISOString()
    const startDate = new Date()
    
    switch (period) {
      case '1h':
        startDate.setHours(startDate.getHours() - 1)
        break
      case '24h':
        startDate.setDate(startDate.getDate() - 1)
        break
      case '7d':
        startDate.setDate(startDate.getDate() - 7)
        break
      case '30d':
        startDate.setDate(startDate.getDate() - 30)
        break
    }

    dispatch(fetchHistoricalData({
      startDate: startDate.toISOString(),
      endDate,
      limit: 100
    }))
  }, [dispatch, period])

  useEffect(() => {
    // 处理图表数据
    if (historicalData.length > 0) {
      const data = historicalData.map(item => ({
        timestamp: item.timestamp,
        price: item.currentPriceUsd
      }))
      setChartData(data)
    }
  }, [historicalData])

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <Spin size="large" />
      </div>
    )
  }

  if (chartData.length === 0) {
    return (
      <div>
        <div style={{ marginBottom: 16 }}>
          <Select
            value={period}
            onChange={setPeriod}
            style={{ width: 120 }}
          >
            <Option value="1h">1小时</Option>
            <Option value="24h">24小时</Option>
            <Option value="7d">7天</Option>
            <Option value="30d">30天</Option>
          </Select>
        </div>
        <Empty description="暂无数据" />
      </div>
    )
  }

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Select
          value={period}
          onChange={setPeriod}
          style={{ width: 120 }}
        >
          <Option value="1h">1小时</Option>
          <Option value="24h">24小时</Option>
          <Option value="7d">7天</Option>
          <Option value="30d">30天</Option>
        </Select>
      </div>
      
      {/* 这里应该集成一个图表库，比如 ECharts 或 Chart.js */}
      <div style={{ 
        height: 300, 
        border: '1px solid #f0f0f0', 
        borderRadius: 6,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: '#fafafa'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: 16, marginBottom: 8 }}>价格走势图</div>
          <div style={{ color: '#8c8c8c' }}>
            数据点数: {chartData.length}
          </div>
          <div style={{ color: '#8c8c8c', fontSize: 12, marginTop: 8 }}>
            提示: 这里需要集成图表库来显示实际的价格走势
          </div>
        </div>
      </div>
    </div>
  )
}

export default PriceChart
