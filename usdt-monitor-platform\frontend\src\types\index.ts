// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  full_name?: string;
  is_active: boolean;
  is_superuser: boolean;
  is_verified: boolean;
  created_at: string;
  updated_at?: string;
  last_login?: string;
  avatar_url?: string;
  roles: Role[];
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
  permissions: Permission[];
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
}

// 认证相关类型
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  confirm_password: string;
  full_name?: string;
}

export interface AuthResponse {
  user: User;
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

// USDT数据相关类型
export interface USDTData {
  id: string;
  timestamp: string;
  unix_timestamp: number;
  name: string;
  symbol: string;
  current_price_usd: number;
  price_precision?: string;
  market_cap_usd?: number;
  market_cap_rank?: number;
  total_volume_usd?: number;
  circulating_supply?: number;
  total_supply?: number;
  price_change_24h?: number;
  price_change_percentage_24h?: number;
  price_change_from_last?: number;
  market_cap_change_24h?: number;
  market_cap_change_percentage_24h?: number;
  last_updated?: string;
  data_source?: string;
  created_at: string;
}

export interface USDTStats {
  latest: USDTData;
  priceHistory: USDTData[];
  volatility24h: number;
  averagePrice24h: number;
  maxPrice24h: number;
  minPrice24h: number;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 系统设置类型
export interface SystemSettings {
  monitoringInterval: number;
  alertThreshold: number;
  dataRetentionDays: number;
  enableNotifications: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
}

// 告警类型
export interface Alert {
  id: string;
  type: 'price_change' | 'volume_change' | 'system_error';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  isRead: boolean;
  userId?: string;
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: 'usdt_update' | 'alert' | 'system_status';
  data: any;
  timestamp: string;
}
