import { LoginRequest, RegisterRequest, AuthResponse, User } from '@/types'
import apiClient from './apiClient'

class AuthService {
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    return await apiClient.post<AuthResponse>('/auth/login', credentials)
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    return await apiClient.post<AuthResponse>('/auth/register', userData)
  }

  async logout(): Promise<void> {
    return await apiClient.post<void>('/auth/logout')
  }

  async getCurrentUser(): Promise<User> {
    return await apiClient.get<User>('/auth/me')
  }

  async refreshToken(refreshToken: string): Promise<{ accessToken: string; refreshToken: string }> {
    return await apiClient.post<{ accessToken: string; refreshToken: string }>('/auth/refresh', {
      refreshToken,
    })
  }

  async changePassword(data: { currentPassword: string; newPassword: string }): Promise<void> {
    return await apiClient.post<void>('/auth/change-password', data)
  }

  async forgotPassword(email: string): Promise<void> {
    return await apiClient.post<void>('/auth/forgot-password', { email })
  }

  async resetPassword(data: { token: string; newPassword: string }): Promise<void> {
    return await apiClient.post<void>('/auth/reset-password', data)
  }

  async verifyEmail(token: string): Promise<void> {
    return await apiClient.post<void>('/auth/verify-email', { token })
  }

  async resendVerificationEmail(): Promise<void> {
    return await apiClient.post<void>('/auth/resend-verification')
  }

  // 检查用户名是否可用
  async checkUsernameAvailability(username: string): Promise<{ available: boolean }> {
    return await apiClient.get<{ available: boolean }>(`/auth/check-username/${username}`)
  }

  // 检查邮箱是否可用
  async checkEmailAvailability(email: string): Promise<{ available: boolean }> {
    return await apiClient.get<{ available: boolean }>(`/auth/check-email/${email}`)
  }
}

export const authService = new AuthService()
export default authService
