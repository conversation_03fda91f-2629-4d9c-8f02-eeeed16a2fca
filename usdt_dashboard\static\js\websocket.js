/**
 * USDT Dashboard WebSocket客户端
 */

const WebSocketClient = {
    // WebSocket实例
    socket: null,
    
    // 配置
    config: {
        reconnectInterval: 5000, // 重连间隔
        maxReconnectAttempts: 10, // 最大重连次数
        heartbeatInterval: 30000 // 心跳间隔
    },
    
    // 状态
    state: {
        isConnected: false,
        reconnectAttempts: 0,
        heartbeatTimer: null
    },

    // 初始化
    init() {
        console.log('WebSocket客户端初始化...');
        this.connect();
        this.bindEvents();
    },

    // 连接WebSocket
    connect() {
        try {
            this.updateConnectionStatus('connecting');
            
            // 创建Socket.IO连接
            this.socket = io({
                transports: ['websocket', 'polling'],
                timeout: 10000,
                forceNew: true
            });

            this.setupSocketEvents();
            
        } catch (error) {
            console.error('WebSocket连接失败:', error);
            this.handleConnectionError();
        }
    },

    // 设置Socket事件
    setupSocketEvents() {
        // 连接成功
        this.socket.on('connect', () => {
            console.log('WebSocket连接成功');
            this.state.isConnected = true;
            this.state.reconnectAttempts = 0;
            this.updateConnectionStatus('connected');
            this.startHeartbeat();
        });

        // 连接断开
        this.socket.on('disconnect', (reason) => {
            console.log('WebSocket连接断开:', reason);
            this.state.isConnected = false;
            this.updateConnectionStatus('disconnected');
            this.stopHeartbeat();
            
            // 自动重连
            if (reason !== 'io client disconnect') {
                this.scheduleReconnect();
            }
        });

        // 连接错误
        this.socket.on('connect_error', (error) => {
            console.error('WebSocket连接错误:', error);
            this.handleConnectionError();
        });

        // 数据更新
        this.socket.on('data_update', (data) => {
            console.log('收到数据更新:', data);
            if (window.Dashboard) {
                window.Dashboard.updateDashboard(data);
            }
        });

        // 错误处理
        this.socket.on('error', (error) => {
            console.error('WebSocket错误:', error);
        });
    },

    // 绑定页面事件
    bindEvents() {
        // 页面卸载时断开连接
        window.addEventListener('beforeunload', () => {
            this.disconnect();
        });

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopHeartbeat();
            } else if (this.state.isConnected) {
                this.startHeartbeat();
                this.requestData();
            }
        });

        // 网络状态变化
        window.addEventListener('online', () => {
            console.log('网络已连接');
            if (!this.state.isConnected) {
                this.connect();
            }
        });

        window.addEventListener('offline', () => {
            console.log('网络已断开');
            this.updateConnectionStatus('disconnected');
        });
    },

    // 请求数据
    requestData() {
        if (this.state.isConnected && this.socket) {
            this.socket.emit('request_data');
        }
    },

    // 发送消息
    send(event, data) {
        if (this.state.isConnected && this.socket) {
            this.socket.emit(event, data);
        } else {
            console.warn('WebSocket未连接，无法发送消息');
        }
    },

    // 断开连接
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }
        this.state.isConnected = false;
        this.stopHeartbeat();
        this.updateConnectionStatus('disconnected');
    },

    // 处理连接错误
    handleConnectionError() {
        this.state.isConnected = false;
        this.updateConnectionStatus('disconnected');
        this.scheduleReconnect();
    },

    // 安排重连
    scheduleReconnect() {
        if (this.state.reconnectAttempts >= this.config.maxReconnectAttempts) {
            console.error('达到最大重连次数，停止重连');
            return;
        }

        this.state.reconnectAttempts++;
        console.log(`${this.config.reconnectInterval / 1000}秒后尝试第${this.state.reconnectAttempts}次重连...`);
        
        setTimeout(() => {
            if (!this.state.isConnected) {
                this.connect();
            }
        }, this.config.reconnectInterval);
    },

    // 开始心跳
    startHeartbeat() {
        this.stopHeartbeat();
        this.state.heartbeatTimer = setInterval(() => {
            if (this.state.isConnected) {
                this.requestData();
            }
        }, this.config.heartbeatInterval);
    },

    // 停止心跳
    stopHeartbeat() {
        if (this.state.heartbeatTimer) {
            clearInterval(this.state.heartbeatTimer);
            this.state.heartbeatTimer = null;
        }
    },

    // 更新连接状态显示
    updateConnectionStatus(status) {
        const statusIcon = document.getElementById('connection-status');
        const statusText = document.getElementById('connection-text');
        
        if (!statusIcon || !statusText) return;

        // 移除所有状态类
        statusIcon.classList.remove('status-connected', 'status-disconnected', 'status-connecting');
        
        switch (status) {
            case 'connected':
                statusIcon.classList.add('status-connected');
                statusText.textContent = '已连接';
                break;
            case 'connecting':
                statusIcon.classList.add('status-connecting');
                statusText.textContent = '连接中...';
                break;
            case 'disconnected':
                statusIcon.classList.add('status-disconnected');
                statusText.textContent = '已断开';
                break;
        }
    },

    // 检查连接状态
    isConnected() {
        return this.state.isConnected;
    },

    // 获取连接状态
    getStatus() {
        return {
            connected: this.state.isConnected,
            reconnectAttempts: this.state.reconnectAttempts
        };
    }
};

// 导出到全局
window.WebSocketClient = WebSocketClient;
