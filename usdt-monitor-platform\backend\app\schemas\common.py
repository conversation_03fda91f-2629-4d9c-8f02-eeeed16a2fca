from typing import Any, Generic, List, Optional, TypeVar
from pydantic import BaseModel

DataType = TypeVar("DataType")


class ApiResponse(BaseModel, Generic[DataType]):
    """API响应基础模型"""
    success: bool = True
    data: Optional[DataType] = None
    message: Optional[str] = None
    errors: Optional[List[str]] = None


class PaginatedResponse(BaseModel, Generic[DataType]):
    """分页响应模型"""
    items: List[DataType]
    total: int
    page: int
    page_size: int
    total_pages: int
    
    @classmethod
    def create(
        cls,
        items: List[DataType],
        total: int,
        page: int,
        page_size: int
    ) -> "PaginatedResponse[DataType]":
        total_pages = (total + page_size - 1) // page_size
        return cls(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )


class HealthCheck(BaseModel):
    """健康检查响应"""
    status: str = "healthy"
    timestamp: str
    version: str
    services: dict = {}


class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = False
    message: str
    errors: Optional[List[str]] = None
    error_code: Optional[str] = None
