from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.schemas.system import (
    SystemSettings,
    SystemSettingsUpdate,
    Alert,
    AlertCreate,
    SystemStatus,
    SystemMetrics
)
from app.schemas.common import ApiResponse, PaginatedResponse
from app.services.system_service import system_service
from app.api.deps import get_current_user, require_system_settings

router = APIRouter()


# 系统设置
@router.get("/settings", response_model=ApiResponse[SystemSettings])
async def get_settings(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取系统设置"""
    settings = system_service.get_settings(db)
    if not settings:
        # 如果没有设置，创建默认设置
        settings = system_service.create_default_settings(db)
    
    return ApiResponse(success=True, data=settings)


@router.put("/settings", response_model=ApiResponse[SystemSettings])
async def update_settings(
    settings_update: SystemSettingsUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(require_system_settings)
):
    """更新系统设置"""
    settings = system_service.update_settings(db, settings_update, current_user.id)
    return ApiResponse(success=True, data=settings)


# 告警管理
@router.get("/alerts", response_model=ApiResponse[PaginatedResponse[Alert]])
async def get_alerts(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    alert_type: Optional[str] = Query(None, description="告警类型"),
    severity: Optional[str] = Query(None, description="严重程度"),
    unread_only: bool = Query(False, description="仅未读"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取告警列表"""
    skip = (page - 1) * page_size
    alerts, total = system_service.get_alerts(
        db,
        skip=skip,
        limit=page_size,
        alert_type=alert_type,
        severity=severity,
        unread_only=unread_only,
        user_id=current_user.id if not current_user.is_superuser else None,
        start_date=start_date,
        end_date=end_date
    )
    
    paginated_data = PaginatedResponse.create(
        items=alerts,
        total=total,
        page=page,
        page_size=page_size
    )
    
    return ApiResponse(success=True, data=paginated_data)


@router.post("/alerts", response_model=ApiResponse[Alert])
async def create_alert(
    alert_create: AlertCreate,
    db: Session = Depends(get_db),
    current_user = Depends(require_system_settings)
):
    """创建告警"""
    alert = system_service.create_alert(db, alert_create)
    return ApiResponse(success=True, data=alert)


@router.patch("/alerts/{alert_id}/read", response_model=ApiResponse[Alert])
async def mark_alert_as_read(
    alert_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """标记告警为已读"""
    alert = system_service.mark_alert_as_read(db, alert_id)
    if not alert:
        raise HTTPException(status_code=404, detail="告警不存在")
    
    return ApiResponse(success=True, data=alert)


@router.patch("/alerts/read-all", response_model=ApiResponse[dict])
async def mark_all_alerts_as_read(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """标记所有告警为已读"""
    count = system_service.mark_all_alerts_as_read(
        db, user_id=current_user.id if not current_user.is_superuser else None
    )
    return ApiResponse(success=True, data={"marked_count": count})


@router.delete("/alerts/{alert_id}", response_model=ApiResponse[dict])
async def delete_alert(
    alert_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(require_system_settings)
):
    """删除告警"""
    success = system_service.delete_alert(db, alert_id)
    if not success:
        raise HTTPException(status_code=404, detail="告警不存在")
    
    return ApiResponse(success=True, data={"message": "告警删除成功"})


@router.get("/alerts/unread-count", response_model=ApiResponse[dict])
async def get_unread_alert_count(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取未读告警数量"""
    count = system_service.get_unread_alert_count(
        db, user_id=current_user.id if not current_user.is_superuser else None
    )
    return ApiResponse(success=True, data={"count": count})


# 系统监控
@router.get("/status", response_model=ApiResponse[dict])
async def get_system_status(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取系统状态"""
    status_data = system_service.get_system_status(db)
    return ApiResponse(success=True, data=status_data)


@router.get("/metrics", response_model=ApiResponse[dict])
async def get_system_metrics(
    period: str = Query("1h", regex="^(1h|24h|7d)$", description="时间周期"),
    db: Session = Depends(get_db),
    current_user = Depends(require_system_settings)
):
    """获取系统指标"""
    metrics = system_service.get_system_metrics(db, period=period)
    return ApiResponse(success=True, data=metrics)
