.App {
  height: 100vh;
}

/* 布局样式 */
.app-layout {
  min-height: 100vh;
}

.app-header {
  background: #fff;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.app-logo {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  color: #1890ff;
}

.app-logo img {
  width: 32px;
  height: 32px;
  margin-right: 12px;
}

.app-sider {
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.app-content {
  padding: 24px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
  overflow-y: auto;
}

/* 菜单样式 */
.app-menu {
  border-right: none;
}

.app-menu .ant-menu-item {
  margin: 4px 0;
  border-radius: 6px;
}

.app-menu .ant-menu-item-selected {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

/* 用户信息下拉菜单 */
.user-dropdown {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-dropdown:hover {
  background-color: #f5f5f5;
}

.user-avatar {
  margin-right: 8px;
}

/* 页面标题 */
.page-header {
  background: #fff;
  padding: 16px 24px;
  margin-bottom: 24px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.page-description {
  color: #8c8c8c;
  margin-top: 8px;
  margin-bottom: 0;
}

/* 卡片样式 */
.dashboard-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dashboard-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.dashboard-card .ant-card-body {
  padding: 24px;
}

/* 统计卡片 */
.stat-card {
  text-align: center;
  padding: 24px;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #8c8c8c;
}

.stat-change {
  font-size: 12px;
  margin-top: 4px;
}

.stat-change.positive {
  color: #52c41a;
}

.stat-change.negative {
  color: #ff4d4f;
}

/* 价格显示 */
.price-display {
  font-size: 48px;
  font-weight: bold;
  color: #1890ff;
  text-align: center;
  margin: 24px 0;
}

.price-change {
  font-size: 18px;
  text-align: center;
  margin-bottom: 16px;
}

/* 表格样式 */
.data-table {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.data-table .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

/* 状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator.online {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-indicator.offline {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-indicator.warning {
  background: #fffbe6;
  color: #faad14;
  border: 1px solid #ffe58f;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-content {
    padding: 16px;
  }
  
  .price-display {
    font-size: 32px;
  }
  
  .stat-value {
    font-size: 24px;
  }
  
  .page-header {
    padding: 12px 16px;
    margin-bottom: 16px;
  }
  
  .page-title {
    font-size: 20px;
  }
}

/* 加载和错误状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.error-container {
  text-align: center;
  padding: 40px;
  color: #ff4d4f;
}

.empty-container {
  text-align: center;
  padding: 40px;
  color: #8c8c8c;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
