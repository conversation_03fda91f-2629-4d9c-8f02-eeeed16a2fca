@echo off
echo ========================================
echo        USDT高频监控程序启动器
echo ========================================
echo.
echo 选择监控模式:
echo 1. 标准监控 (30秒) - 推荐设置，平衡资源
echo 2. 高频监控 (5秒) - 捕获更多变化
echo 3. 超高频监控 (3秒) - 短期分析
echo 4. 实时监控 (1秒) - 最大化数据捕获
echo 5. 进入文件夹选择更多选项
echo.
set /p choice="请选择 (1-5): "

cd /d "%~dp0\usdt_monitor"

if "%choice%"=="1" (
    echo 🚀 启动标准监控...
    python usdt_monitor.py -i 30
) else if "%choice%"=="2" (
    echo 🚀 启动高频监控...
    python usdt_monitor.py -i 5
) else if "%choice%"=="3" (
    echo 🚀 启动超高频监控...
    python usdt_monitor.py -i 3
) else if "%choice%"=="4" (
    echo 🚀 启动实时监控...
    python usdt_monitor.py -i 1
) else if "%choice%"=="5" (
    echo 打开usdt_monitor文件夹...
    explorer .
    goto end
) else (
    echo 无效选择，使用推荐设置(5秒)
    python usdt_monitor.py -i 5
)

:end
echo.
echo 程序已结束，按任意键退出...
pause > nul
