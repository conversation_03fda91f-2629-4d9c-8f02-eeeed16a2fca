# USDT 实时监控仪表板

## 🚀 项目简介

这是一个结构化、工程化的USDT实时监控仪表板，提供美观的Web界面来可视化USDT市值数据。

## ✨ 主要特性

### 📊 实时数据可视化
- **实时价格显示** - 大屏幕显示当前USDT价格
- **动态图表** - Chart.js驱动的交互式价格趋势图
- **关键指标卡片** - 市值、交易量、供应量等核心数据
- **24小时统计** - 圆环图显示价格和市值变化

### 🔄 实时通信
- **WebSocket连接** - 实时数据推送，无需刷新页面
- **自动重连** - 网络断开时自动重连
- **连接状态指示** - 实时显示连接状态
- **心跳机制** - 保持连接活跃

### 📱 响应式设计
- **移动端适配** - 完美支持手机和平板
- **Bootstrap 5** - 现代化UI框架
- **Font Awesome图标** - 丰富的图标库
- **动画效果** - 平滑的数据更新动画

### 🏗️ 工程化架构
- **模块化设计** - 清晰的代码结构
- **配置分离** - 环境配置独立管理
- **错误处理** - 完善的异常处理机制
- **日志记录** - 详细的运行日志

## 📁 项目结构

```
usdt_dashboard/
├── app.py                 # Flask主应用
├── config.py             # 配置文件
├── data_service.py       # 数据服务层
├── run.py               # 启动脚本
├── requirements.txt     # Python依赖
├── 启动仪表板.bat        # Windows启动脚本
├── static/              # 静态资源
│   ├── css/
│   │   └── dashboard.css # 自定义样式
│   └── js/
│       ├── dashboard.js  # 主要逻辑
│       ├── charts.js     # 图表模块
│       └── websocket.js  # WebSocket客户端
└── templates/
    └── dashboard.html    # HTML模板
```

## 🛠️ 技术栈

### 后端
- **Flask** - Web框架
- **Flask-SocketIO** - WebSocket支持
- **Python** - 编程语言

### 前端
- **HTML5/CSS3/JavaScript** - 基础技术
- **Bootstrap 5** - UI框架
- **Chart.js** - 图表库
- **Socket.IO** - 实时通信
- **Font Awesome** - 图标库

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动服务器
```bash
python run.py
```

### 3. 访问仪表板
打开浏览器访问：http://localhost:5000

### 4. 一键启动（Windows）
双击 `启动仪表板.bat` 文件

## 📊 功能说明

### 主要界面组件

1. **顶部价格显示**
   - 当前USDT价格（8位精度）
   - 24小时价格变化百分比
   - 最后更新时间

2. **关键指标卡片**
   - 市值和排名
   - 24小时交易量
   - 流通供应量
   - 数据记录统计

3. **价格趋势图表**
   - 实时价格曲线
   - 时间段切换（1小时/6小时/24小时）
   - 交互式缩放和悬停

4. **统计圆环图**
   - 价格变化比例
   - 市值变化比例
   - 交易量比率

5. **最新数据表格**
   - 最近10条记录
   - 时间、价格、市值、变化等信息

### 实时功能

- **自动数据更新** - 每30秒自动获取最新数据
- **WebSocket推送** - 服务器主动推送数据更新
- **连接状态监控** - 实时显示连接状态
- **断线重连** - 自动重连机制

## ⚙️ 配置说明

### 环境配置
- `development` - 开发环境（默认）
- `production` - 生产环境

### 主要配置项
- `DATA_FILE_PATH` - 数据文件路径
- `UPDATE_INTERVAL` - 更新间隔（秒）
- `CHART_MAX_POINTS` - 图表最大点数

## 🔧 开发说明

### 添加新功能
1. 在 `data_service.py` 中添加数据处理逻辑
2. 在 `app.py` 中添加API端点
3. 在前端JavaScript中添加UI逻辑
4. 更新HTML模板和CSS样式

### 自定义样式
编辑 `static/css/dashboard.css` 文件

### 修改图表
编辑 `static/js/charts.js` 文件

## 📝 API接口

- `GET /` - 仪表板主页
- `GET /api/data` - 获取完整数据
- `GET /api/history/<hours>` - 获取历史数据
- `WebSocket /` - 实时数据推送

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   - 修改 `run.py` 中的端口号

2. **数据文件不存在**
   - 确保USDT监控程序已运行并生成数据

3. **依赖安装失败**
   - 使用 `pip install --upgrade pip` 更新pip
   - 检查Python版本（需要3.7+）

4. **WebSocket连接失败**
   - 检查防火墙设置
   - 确保浏览器支持WebSocket

## 📈 性能优化

- 数据缓存机制
- 图表数据点限制
- WebSocket心跳优化
- 响应式图片加载

## 🔒 安全考虑

- CORS配置
- 输入验证
- 错误信息过滤
- 连接限制

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！
