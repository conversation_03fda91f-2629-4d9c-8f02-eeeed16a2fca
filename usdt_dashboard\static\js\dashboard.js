/**
 * USDT Dashboard 主要JavaScript模块
 */

const Dashboard = {
    // 配置
    config: {
        updateInterval: 30000, // 30秒
        animationDuration: 300,
        numberFormat: {
            minimumFractionDigits: 2,
            maximumFractionDigits: 8
        }
    },

    // 状态
    state: {
        lastData: null,
        isConnected: false,
        currentPeriod: 1
    },

    // 初始化
    init() {
        console.log('Dashboard 初始化...');
        this.bindEvents();
        this.showLoading();
        this.loadInitialData();
    },

    // 绑定事件
    bindEvents() {
        // 时间段切换按钮
        document.querySelectorAll('[data-period]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const period = parseInt(e.target.dataset.period);
                this.changePeriod(period);
            });
        });

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.requestDataUpdate();
            }
        });
    },

    // 加载初始数据
    async loadInitialData() {
        try {
            const response = await fetch('/api/data');
            const result = await response.json();
            
            if (result.success) {
                this.updateDashboard(result.data);
                this.hideLoading();
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('加载初始数据失败:', error);
            this.showError('加载数据失败，请刷新页面重试');
            this.hideLoading();
        }
    },

    // 更新仪表板
    updateDashboard(data) {
        console.log('更新仪表板数据:', data);
        
        if (data.latest) {
            this.updatePriceDisplay(data.latest);
            this.updateMetrics(data.latest, data.statistics);
        }
        
        if (data.price_history) {
            Charts.updatePriceChart(data.price_history);
        }
        
        if (data.statistics) {
            Charts.updateStatsChart(data.statistics);
            this.updateDataTable(data.recent_data || []);
            this.updateVolatility(data.price_history || []);
        }
        
        this.updateLastUpdateTime();
        this.state.lastData = data;
    },

    // 更新价格显示
    updatePriceDisplay(latest) {
        const priceElement = document.getElementById('current-price');
        const changeElement = document.getElementById('price-change');

        if (!priceElement || !changeElement) return;

        const currentPrice = parseFloat(latest.current_price_usd || 1.0);
        const priceChange = parseFloat(latest.price_change_percentage_24h || 0);

        // 使用高精度价格显示（显示到小数点后10位）
        const formattedPrice = this.formatHighPrecisionPrice(currentPrice);
        if (priceElement.textContent !== formattedPrice) {
            this.animateNumberChange(priceElement, formattedPrice);
        }

        // 更新变化百分比（显示到小数点后6位）
        const changeText = `${priceChange >= 0 ? '+' : ''}${priceChange.toFixed(6)}%`;
        changeElement.textContent = changeText;
        changeElement.className = `badge ${priceChange >= 0 ? 'bg-success' : 'bg-danger'}`;

        // 如果有与上次的变化，也显示出来
        if (latest.price_change_from_last !== undefined && latest.price_change_from_last !== 0) {
            const instantChange = parseFloat(latest.price_change_from_last);
            const instantText = `${instantChange >= 0 ? '+' : ''}${instantChange.toFixed(8)}%`;
            // 可以在这里添加即时变化显示
        }
    },

    // 更新指标
    updateMetrics(latest, statistics) {
        const updates = [
            { id: 'market-cap', value: this.formatCurrency(latest.market_cap_usd) },
            { id: 'market-rank', value: latest.market_cap_rank || 0 },
            { id: 'volume-24h', value: this.formatCurrency(latest.total_volume_usd) },
            { id: 'circulating-supply', value: this.formatNumber(latest.circulating_supply / 1e9, 2) + 'B' },
            { id: 'total-records', value: statistics?.total_records || 0 },
            { id: 'records-24h', value: statistics?.records_24h || 0 }
        ];

        updates.forEach(({ id, value }) => {
            const element = document.getElementById(id);
            if (element && element.textContent !== value.toString()) {
                this.animateNumberChange(element, value);
            }
        });

        // 更新交易量变化（如果有数据）
        if (latest.price_change_percentage_24h !== undefined) {
            const volumeChangeElement = document.getElementById('volume-change');
            if (volumeChangeElement) {
                const volumeChange = latest.price_change_percentage_24h || 0;
                volumeChangeElement.textContent = `${volumeChange >= 0 ? '+' : ''}${volumeChange.toFixed(6)}%`;
                volumeChangeElement.className = volumeChange >= 0 ? 'text-success' : 'text-danger';
            }
        }
    },

    // 更新数据表格
    updateDataTable(recentData) {
        const tableBody = document.getElementById('data-table');
        if (!tableBody) return;

        tableBody.innerHTML = '';
        
        recentData.slice(-10).reverse().forEach((record, index) => {
            const row = document.createElement('tr');
            const time = new Date(record.timestamp).toLocaleTimeString();
            const price = this.formatHighPrecisionPrice(record.current_price_usd);
            const marketCap = this.formatCurrency(record.market_cap_usd);
            const change = record.price_change_percentage_24h || 0;
            const volume = this.formatCurrency(record.total_volume_usd);

            // 计算与上一条记录的即时变化
            let instantChange = '';
            if (index < recentData.length - 1) {
                const currentPrice = parseFloat(record.current_price_usd || 1.0);
                const previousPrice = parseFloat(recentData[recentData.length - 1 - index - 1]?.current_price_usd || 1.0);
                const instantChangePercent = ((currentPrice - previousPrice) / previousPrice * 100);
                if (Math.abs(instantChangePercent) > 0.000001) {
                    instantChange = ` <small class="${instantChangePercent >= 0 ? 'text-success' : 'text-danger'}">
                        (${instantChangePercent >= 0 ? '+' : ''}${instantChangePercent.toFixed(8)}%)
                    </small>`;
                }
            }

            row.innerHTML = `
                <td>${time}</td>
                <td>$${price}${instantChange}</td>
                <td>${marketCap}</td>
                <td class="${change >= 0 ? 'text-success' : 'text-danger'}">
                    ${change >= 0 ? '+' : ''}${change.toFixed(6)}%
                </td>
                <td>${volume}</td>
            `;

            tableBody.appendChild(row);
        });
    },

    // 更新最后更新时间
    updateLastUpdateTime() {
        const element = document.getElementById('last-update');
        if (element) {
            element.textContent = new Date().toLocaleTimeString();
        }
    },

    // 切换时间段
    changePeriod(period) {
        this.state.currentPeriod = period;
        
        // 更新按钮状态
        document.querySelectorAll('[data-period]').forEach(btn => {
            btn.classList.toggle('active', parseInt(btn.dataset.period) === period);
        });
        
        // 重新加载图表数据
        this.loadHistoryData(period);
    },

    // 加载历史数据
    async loadHistoryData(hours) {
        try {
            const response = await fetch(`/api/history/${hours}`);
            const result = await response.json();
            
            if (result.success) {
                Charts.updatePriceChart(result.data);
            }
        } catch (error) {
            console.error('加载历史数据失败:', error);
        }
    },

    // 请求数据更新
    requestDataUpdate() {
        if (window.WebSocketClient && window.WebSocketClient.isConnected()) {
            window.WebSocketClient.requestData();
        } else {
            this.loadInitialData();
        }
    },

    // 数字动画
    animateNumberChange(element, newValue) {
        element.classList.add('number-animate');
        element.textContent = newValue;
        
        setTimeout(() => {
            element.classList.remove('number-animate');
        }, this.config.animationDuration);
    },

    // 格式化数字
    formatNumber(num, decimals = 2) {
        if (typeof num !== 'number' || isNaN(num)) return '0';
        return num.toFixed(decimals);
    },

    // 格式化高精度价格（专门用于USDT显示）
    formatHighPrecisionPrice(price) {
        if (typeof price !== 'number' || isNaN(price)) return '1.0000000000';

        // 对于USDT，显示10位小数以捕获微小变化
        const formatted = price.toFixed(10);

        // 移除末尾的0，但至少保留6位小数
        let trimmed = formatted.replace(/0+$/, '');
        if (trimmed.endsWith('.')) {
            trimmed += '000000';
        } else {
            const decimalPart = trimmed.split('.')[1] || '';
            if (decimalPart.length < 6) {
                trimmed += '0'.repeat(6 - decimalPart.length);
            }
        }

        return trimmed;
    },

    // 计算和更新价格波动性
    updateVolatility(priceHistory) {
        const volatilityElement = document.getElementById('price-volatility');
        if (!volatilityElement || !priceHistory || priceHistory.length < 2) return;

        // 计算价格变化的标准差作为波动性指标
        const prices = priceHistory.map(record => parseFloat(record.price || 1.0));
        const changes = [];

        for (let i = 1; i < prices.length; i++) {
            const change = (prices[i] - prices[i-1]) / prices[i-1];
            changes.push(change);
        }

        if (changes.length === 0) return;

        // 计算标准差
        const mean = changes.reduce((sum, change) => sum + change, 0) / changes.length;
        const variance = changes.reduce((sum, change) => sum + Math.pow(change - mean, 2), 0) / changes.length;
        const volatility = Math.sqrt(variance) * 100; // 转换为百分比

        // 显示波动性
        const formattedVolatility = volatility.toFixed(8) + '%';
        if (volatilityElement.textContent !== formattedVolatility) {
            this.animateNumberChange(volatilityElement, formattedVolatility);
        }

        // 根据波动性大小改变颜色
        volatilityElement.className = '';
        if (volatility > 0.01) {
            volatilityElement.classList.add('text-danger'); // 高波动
        } else if (volatility > 0.005) {
            volatilityElement.classList.add('text-warning'); // 中等波动
        } else {
            volatilityElement.classList.add('text-success'); // 低波动
        }
    },

    // 格式化货币
    formatCurrency(amount) {
        if (typeof amount !== 'number' || isNaN(amount)) return '$0';
        
        if (amount >= 1e12) {
            return `$${(amount / 1e12).toFixed(2)}T`;
        } else if (amount >= 1e9) {
            return `$${(amount / 1e9).toFixed(2)}B`;
        } else if (amount >= 1e6) {
            return `$${(amount / 1e6).toFixed(2)}M`;
        } else if (amount >= 1e3) {
            return `$${(amount / 1e3).toFixed(2)}K`;
        } else {
            return `$${amount.toFixed(2)}`;
        }
    },

    // 显示加载
    showLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.add('show');
        }
    },

    // 隐藏加载
    hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.remove('show');
        }
    },

    // 显示错误
    showError(message) {
        console.error('Dashboard Error:', message);
        // 这里可以添加错误提示UI
    }
};

// 导出到全局
window.Dashboard = Dashboard;
