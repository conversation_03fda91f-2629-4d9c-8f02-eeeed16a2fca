<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>USDT监控平台 - 登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .login-form {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
    </style>
</head>
<body>
    <div class="login-form">
        <h2>USDT监控平台登录测试</h2>
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" value="admin" required>
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="admin123" required>
            </div>
            <button type="submit">登录</button>
        </form>
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                resultDiv.innerHTML = '<div>正在登录...</div>';
                
                const response = await fetch('http://localhost:8000/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ 登录成功!</h3>
                            <p><strong>用户:</strong> ${data.data.user.username}</p>
                            <p><strong>邮箱:</strong> ${data.data.user.email}</p>
                            <p><strong>Token:</strong> ${data.data.access_token.substring(0, 50)}...</p>
                            <p><strong>过期时间:</strong> ${data.data.expires_in}秒</p>
                        </div>
                    `;
                    
                    // 保存token到localStorage
                    localStorage.setItem('accessToken', data.data.access_token);
                    localStorage.setItem('refreshToken', data.data.refresh_token);
                    
                    // 测试获取用户信息
                    setTimeout(testGetUserInfo, 1000);
                    
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ 登录失败</h3>
                            <p>${data.message || '未知错误'}</p>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>${error.message}</p>
                        <p>请检查后端服务是否正常运行</p>
                    </div>
                `;
            }
        });
        
        async function testGetUserInfo() {
            const token = localStorage.getItem('accessToken');
            if (!token) return;
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/auth/me', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const resultDiv = document.getElementById('result');
                    resultDiv.innerHTML += `
                        <div class="result success" style="margin-top: 10px;">
                            <h3>✅ 获取用户信息成功!</h3>
                            <p><strong>ID:</strong> ${data.data.id}</p>
                            <p><strong>姓名:</strong> ${data.data.full_name || '未设置'}</p>
                            <p><strong>超级用户:</strong> ${data.data.is_superuser ? '是' : '否'}</p>
                            <p><strong>账户状态:</strong> ${data.data.is_active ? '激活' : '停用'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
            }
        }
        
        // 页面加载时检查是否已登录
        window.addEventListener('load', function() {
            const token = localStorage.getItem('accessToken');
            if (token) {
                document.getElementById('result').innerHTML = `
                    <div class="result success">
                        <p>检测到已保存的登录信息</p>
                        <button onclick="testGetUserInfo()">测试获取用户信息</button>
                        <button onclick="localStorage.clear(); location.reload()">清除登录信息</button>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
