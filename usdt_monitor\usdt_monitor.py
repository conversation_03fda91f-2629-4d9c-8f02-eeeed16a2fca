#!/usr/bin/env python3
"""
泰达币（USDT）市值监控脚本
每隔30秒查询USDT市值并保存到本地文件
"""

import requests
import json
import time
from datetime import datetime
import os
import sys

class USDTMonitor:
    def __init__(self, data_file="usdt_market_data.json"):
        self.data_file = data_file
        self.api_url = "https://api.coingecko.com/api/v3/coins/tether"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
    def fetch_usdt_data(self):
        """获取USDT市值数据"""
        try:
            response = requests.get(self.api_url, headers=self.headers, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            # 提取关键信息
            market_data = data.get('market_data', {})
            usdt_info = {
                'timestamp': datetime.now().isoformat(),
                'name': data.get('name', 'Tether'),
                'symbol': data.get('symbol', 'USDT').upper(),
                'current_price_usd': market_data.get('current_price', {}).get('usd'),
                'market_cap_usd': market_data.get('market_cap', {}).get('usd'),
                'market_cap_rank': market_data.get('market_cap_rank'),
                'total_volume_usd': market_data.get('total_volume', {}).get('usd'),
                'circulating_supply': market_data.get('circulating_supply'),
                'total_supply': market_data.get('total_supply'),
                'price_change_24h': market_data.get('price_change_24h'),
                'price_change_percentage_24h': market_data.get('price_change_percentage_24h'),
                'market_cap_change_24h': market_data.get('market_cap_change_24h'),
                'market_cap_change_percentage_24h': market_data.get('market_cap_change_percentage_24h')
            }
            
            return usdt_info
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求错误: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析错误: {e}")
            return None
        except Exception as e:
            print(f"❌ 获取数据时发生错误: {e}")
            return None
    
    def save_data(self, data):
        """保存数据到文件"""
        try:
            # 如果文件存在，读取现有数据
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            else:
                existing_data = []
            
            # 添加新数据
            existing_data.append(data)
            
            # 保存回文件
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)
                
            return True
            
        except Exception as e:
            print(f"❌ 保存数据时发生错误: {e}")
            return False
    
    def format_number(self, num):
        """格式化数字显示"""
        if num is None:
            return "N/A"
        if num >= 1e9:
            return f"{num/1e9:.2f}B"
        elif num >= 1e6:
            return f"{num/1e6:.2f}M"
        elif num >= 1e3:
            return f"{num/1e3:.2f}K"
        else:
            return f"{num:.2f}"
    
    def display_data(self, data):
        """显示数据到控制台"""
        if not data:
            return
            
        print(f"\n🕒 {data['timestamp']}")
        print(f"💰 {data['name']} ({data['symbol']})")
        print(f"💵 当前价格: ${data['current_price_usd']:.4f}")
        print(f"📊 市值: ${self.format_number(data['market_cap_usd'])}")
        print(f"🏆 市值排名: #{data['market_cap_rank']}")
        print(f"📈 24小时交易量: ${self.format_number(data['total_volume_usd'])}")
        print(f"🔄 流通供应量: {self.format_number(data['circulating_supply'])}")
        
        if data['price_change_percentage_24h'] is not None:
            change_emoji = "📈" if data['price_change_percentage_24h'] >= 0 else "📉"
            print(f"{change_emoji} 24小时价格变化: {data['price_change_percentage_24h']:.2f}%")
        
        if data['market_cap_change_percentage_24h'] is not None:
            cap_change_emoji = "📈" if data['market_cap_change_percentage_24h'] >= 0 else "📉"
            print(f"{cap_change_emoji} 24小时市值变化: {data['market_cap_change_percentage_24h']:.2f}%")
        
        print("-" * 50)
    
    def run(self):
        """运行监控程序"""
        print("🚀 USDT市值监控程序启动")
        print(f"📁 数据将保存到: {os.path.abspath(self.data_file)}")
        print("⏰ 每30秒更新一次数据")
        print("🛑 按 Ctrl+C 停止程序\n")
        
        try:
            while True:
                # 获取数据
                data = self.fetch_usdt_data()
                
                if data:
                    # 显示数据
                    self.display_data(data)
                    
                    # 保存数据
                    if self.save_data(data):
                        print("✅ 数据已保存")
                    else:
                        print("❌ 数据保存失败")
                else:
                    print("❌ 获取数据失败，30秒后重试...")
                
                # 等待30秒
                time.sleep(30)
                
        except KeyboardInterrupt:
            print("\n\n🛑 程序已停止")
            print(f"📊 数据已保存到: {os.path.abspath(self.data_file)}")
            sys.exit(0)
        except Exception as e:
            print(f"\n❌ 程序运行时发生错误: {e}")
            sys.exit(1)

def main():
    """主函数"""
    monitor = USDTMonitor()
    monitor.run()

if __name__ == "__main__":
    main()
