import React, { useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { Layout, Spin } from 'antd'
import { RootState, AppDispatch } from './store'
import { getCurrentUser } from './store/slices/authSlice'
import AppLayout from './components/Layout/AppLayout'
import LoginPage from './pages/Auth/LoginPage'
import RegisterPage from './pages/Auth/RegisterPage'
import DashboardPage from './pages/Dashboard/DashboardPage'
import UserManagementPage from './pages/UserManagement/UserManagementPage'
import SystemSettingsPage from './pages/SystemSettings/SystemSettingsPage'
import ProtectedRoute from './components/Auth/ProtectedRoute'
import './App.css'

const { Content } = Layout

function App() {
  const dispatch = useDispatch<AppDispatch>()
  const { isAuthenticated, loading, user } = useSelector((state: RootState) => state.auth)

  useEffect(() => {
    // 如果有token但没有用户信息，尝试获取当前用户
    if (isAuthenticated && !user) {
      dispatch(getCurrentUser())
    }
  }, [dispatch, isAuthenticated, user])

  if (loading) {
    return (
      <Layout style={{ minHeight: '100vh' }}>
        <Content style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Spin size="large" />
        </Content>
      </Layout>
    )
  }

  return (
    <div className="App">
      <Routes>
        {/* 公开路由 */}
        <Route 
          path="/login" 
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <LoginPage />
          } 
        />
        <Route 
          path="/register" 
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <RegisterPage />
          } 
        />

        {/* 受保护的路由 */}
        <Route
          path="/*"
          element={
            <ProtectedRoute>
              <AppLayout>
                <Routes>
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="/dashboard" element={<DashboardPage />} />
                  <Route 
                    path="/users" 
                    element={
                      <ProtectedRoute requiredPermission="user_management">
                        <UserManagementPage />
                      </ProtectedRoute>
                    } 
                  />
                  <Route 
                    path="/settings" 
                    element={
                      <ProtectedRoute requiredPermission="system_settings">
                        <SystemSettingsPage />
                      </ProtectedRoute>
                    } 
                  />
                  {/* 404页面 */}
                  <Route path="*" element={<Navigate to="/dashboard" replace />} />
                </Routes>
              </AppLayout>
            </ProtectedRoute>
          }
        />
      </Routes>
    </div>
  )
}

export default App
