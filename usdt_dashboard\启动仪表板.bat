@echo off
echo ========================================
echo        USDT实时监控仪表板
echo ========================================
echo.
echo 正在检查依赖...

cd /d "%~dp0"

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 安装依赖
echo 📦 安装依赖包...
pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo.
echo ✅ 依赖安装完成
echo 🚀 启动仪表板服务器...
echo.
echo 📊 访问地址: http://localhost:5000
echo 🛑 按 Ctrl+C 停止服务器
echo.

python run.py

echo.
echo 服务器已停止，按任意键退出...
pause > nul
