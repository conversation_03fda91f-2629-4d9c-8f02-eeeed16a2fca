from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from app.core.config import settings
from app.core.database import init_db
from app.api.v1 import api_router
from app.schemas.common import ApiResponse

# 创建FastAPI应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="USDT监控平台后端API",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc"
)

# 设置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 全局异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content=ApiResponse(
            success=False,
            message=exc.detail,
            errors=[exc.detail] if exc.detail else None
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content=ApiResponse(
            success=False,
            message="内部服务器错误",
            errors=[str(exc)] if settings.DEBUG else None
        ).dict()
    )


# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时执行"""
    # 初始化数据库
    init_db()
    print(f"🚀 {settings.PROJECT_NAME} 启动成功!")
    print(f"📖 API文档: http://{settings.HOST}:{settings.PORT}{settings.API_V1_STR}/docs")


# 健康检查
@app.get("/health", response_model=ApiResponse[dict])
async def health_check():
    """健康检查端点"""
    return ApiResponse(
        success=True,
        data={
            "status": "healthy",
            "version": settings.VERSION,
            "service": settings.PROJECT_NAME
        }
    )


# 根路径
@app.get("/", response_model=ApiResponse[dict])
async def root():
    """根路径"""
    return ApiResponse(
        success=True,
        data={
            "message": f"欢迎使用 {settings.PROJECT_NAME}",
            "version": settings.VERSION,
            "docs": f"{settings.API_V1_STR}/docs"
        }
    )


# 包含API路由
app.include_router(api_router, prefix=settings.API_V1_STR)


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
