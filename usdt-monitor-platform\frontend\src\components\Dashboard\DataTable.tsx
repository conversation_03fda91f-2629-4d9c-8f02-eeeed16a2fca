import React from 'react'
import { Table, Tag, Typography, Space } from 'antd'
import { useSelector } from 'react-redux'
import { RootState } from '@/store'
import { USDTData } from '@/types'

const { Text } = Typography

const DataTable: React.FC = () => {
  const { historicalData, loading } = useSelector((state: RootState) => state.usdt)

  const columns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp: string) => (
        <Text style={{ fontSize: 12 }}>
          {new Date(timestamp).toLocaleTimeString()}
        </Text>
      ),
    },
    {
      title: '价格',
      dataIndex: 'currentPriceUsd',
      key: 'price',
      render: (price: number) => (
        <Text strong style={{ color: '#1890ff' }}>
          ${price.toFixed(8)}
        </Text>
      ),
    },
    {
      title: '变化',
      dataIndex: 'priceChangePercentage24h',
      key: 'change',
      render: (change: number) => {
        const isPositive = change >= 0
        return (
          <Tag color={isPositive ? 'green' : 'red'}>
            {isPositive ? '+' : ''}{change.toFixed(4)}%
          </Tag>
        )
      },
    },
    {
      title: '数据源',
      dataIndex: 'dataSource',
      key: 'dataSource',
      render: (source: string) => (
        <Tag color="blue">{source || 'API'}</Tag>
      ),
    },
  ]

  // 取最近20条数据
  const recentData = historicalData.slice(-20).reverse()

  return (
    <Table
      columns={columns}
      dataSource={recentData}
      rowKey="id"
      loading={loading}
      pagination={false}
      size="small"
      scroll={{ y: 400 }}
      style={{ fontSize: 12 }}
    />
  )
}

export default DataTable
